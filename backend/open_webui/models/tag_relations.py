import logging
import time
import uuid
from typing import Optional, List

from open_webui.internal.db import Base, get_db
from open_webui.env import SRC_LOG_LEVELS
from pydantic import BaseModel, ConfigDict
from sqlalchemy import BigInteger, Column, String, JSON, UniqueConstraint

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MODELS"])

####################
# TagRelation DB Schema
####################

class TagRelation(Base):
    __tablename__ = "tag_relation"
    
    id = Column(String, primary_key=True)
    user_id = Column(String, nullable=False)
    created_at = Column(BigInteger, nullable=False)
    updated_at = Column(BigInteger, nullable=False)
    meta = Column(JSON, nullable=True)
    table_name = Column(String, nullable=False)
    table_id = Column(String, nullable=False)
    tag_id = Column(String, nullable=False)
    access_control = Column(JSON, nullable=True)
    
    # 确保表名、表ID和标签ID的组合是唯一的
    __table_args__ = (
        UniqueConstraint('table_name', 'table_id', 'tag_id', name='uq_table_id_tag_id'),
    )


class TagRelationModel(BaseModel):
    id: str
    user_id: str
    created_at: int  # timestamp in epoch
    updated_at: int  # timestamp in epoch
    meta: Optional[dict] = None
    table_name: str
    table_id: str
    tag_id: str
    access_control: Optional[dict] = None
    
    model_config = ConfigDict(from_attributes=True)


####################
# Forms
####################

class TagRelationCreateForm(BaseModel):
    table_name: str
    table_id: str
    tag_id: str
    meta: Optional[dict] = None
    access_control: Optional[dict] = None


class TagRelationUpdateForm(BaseModel):
    meta: Optional[dict] = None
    access_control: Optional[dict] = None


####################
# TagRelation Table
####################

class TagRelationTable:
    def create_tag_relation(self, user_id: str, form: TagRelationCreateForm) -> Optional[TagRelationModel]:
        """创建一个新的标签关系"""
        try:
            with get_db() as db:
                current_time = int(time.time())
                tag_relation = TagRelation(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    created_at=current_time,
                    updated_at=current_time,
                    meta=form.meta,
                    table_name=form.table_name,
                    table_id=form.table_id,
                    tag_id=form.tag_id,
                    access_control=form.access_control
                )
                
                db.add(tag_relation)
                db.commit()
                db.refresh(tag_relation)
                
                return TagRelationModel.model_validate(tag_relation)
        except Exception as e:
            log.error(f"创建标签关系失败: {e}")
            return None
    
    def get_tag_relations_by_table(self, table_name: str, table_id: str) -> List[TagRelationModel]:
        """获取指定表和ID的所有标签关系"""
        try:
            with get_db() as db:
                tag_relations = db.query(TagRelation).filter_by(
                    table_name=table_name,
                    table_id=table_id
                ).all()
                
                return [TagRelationModel.model_validate(tr) for tr in tag_relations]
        except Exception as e:
            log.error(f"获取标签关系失败: {e}")
            return []
    
    def get_tag_relations_by_tag(self, tag_id: str) -> List[TagRelationModel]:
        """获取指定标签的所有关系"""
        try:
            with get_db() as db:
                tag_relations = db.query(TagRelation).filter_by(tag_id=tag_id).all()
                
                return [TagRelationModel.model_validate(tr) for tr in tag_relations]
        except Exception as e:
            log.error(f"获取标签关系失败: {e}")
            return []
    
    def delete_tag_relation(self, table_name: str, table_id: str, tag_id: str) -> bool:
        """删除指定的标签关系"""
        try:
            with get_db() as db:
                result = db.query(TagRelation).filter_by(
                    table_name=table_name,
                    table_id=table_id,
                    tag_id=tag_id
                ).delete()
                
                db.commit()
                return result > 0
        except Exception as e:
            log.error(f"删除标签关系失败: {e}")
            return False
    
    def update_tag_relation(
        self, table_name: str, table_id: str, tag_id: str, form: TagRelationUpdateForm
    ) -> Optional[TagRelationModel]:
        """更新标签关系"""
        try:
            with get_db() as db:
                tag_relation = db.query(TagRelation).filter_by(
                    table_name=table_name,
                    table_id=table_id,
                    tag_id=tag_id
                ).first()
                
                if not tag_relation:
                    return None
                
                if form.meta is not None:
                    tag_relation.meta = form.meta
                
                if form.access_control is not None:
                    tag_relation.access_control = form.access_control
                
                tag_relation.updated_at = int(time.time())
                
                db.add(tag_relation)
                db.commit()
                db.refresh(tag_relation)
                
                return TagRelationModel.model_validate(tag_relation)
        except Exception as e:
            log.error(f"更新标签关系失败: {e}")
            return None


# 创建一个全局实例
TagRelations = TagRelationTable()
