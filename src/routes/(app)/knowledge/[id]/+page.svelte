<script lang="ts">
  import { onMount, getContext } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import Notebook from '$lib/components/knowledge/Notebook.svelte';
  import { getKnowledgeById } from '$lib/apis/knowledge';
  import { toast } from 'svelte-sonner';
  import Spinner from '$lib/components/common/Spinner.svelte';
  import type { Writable } from 'svelte/store';
	import type { Collection } from '$lib/models/collection';
  import type { I18n } from '$lib/models/i18n';
  
  const i18n = getContext<Writable<I18n>>('i18n');
  
  // 獲取路由參數中的ID
  const id = $page.params.id;
  
  // 狀態
  let collection: Collection | null = null;
  let isLoading: boolean = true;
  
  // 返回上一頁
  const goBack = () => {
    goto('/');
  };
  
  onMount(async () => {
    try {
      // 獲取筆記本數據
      collection = await getKnowledgeById(localStorage.token, id);
      // 填充文件缺少的屬性
      if (collection !== null) {
        collection.files ??= [];
        collection.files = collection.files.map(file => ({
          ...file,
          // 在這裡添加缺少的屬性
          collection: {
            name: collection!.name,
            description: collection!.description,
          },
          name: file.filename || '',
          description: `${collection!.name} - ${collection!.description}`,
          type: 'file',
          status: 'processed',
        }));
      }
      isLoading = false;
    } catch (error: unknown) {
      toast.error($i18n.t('Load notebook failed') + `: ${error instanceof Error ? error.message : 'Unknown error'}`);
      isLoading = false;
    }
  });
</script>

<div class="flex flex-col w-full h-screen max-h-[100dvh]">
  <!-- 主要內容 -->
  <div class="flex-1 overflow-auto">
    {#if isLoading}
      <div class="flex justify-center items-center h-full">
        <Spinner />
      </div>
    {:else if collection}
      <Notebook
        title={collection.name}
        collection={collection}
        files={(collection.files ?? []).map(file => [true, file])}
        isLoading={false}
        createdAt={new Date(collection.created_at ?? 0 * 1000)}
        updatedAt={new Date(collection.updated_at ?? 0 * 1000)}
      />
    {:else}
      <div class="flex flex-col items-center justify-center h-full">
        <p class="text-xl font-medium text-gray-700 dark:text-gray-300 mb-4">
          {$i18n.t('Notebook not found')}
        </p>
        <a 
          href="/" 
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {$i18n.t('Back') + $i18n.t('Workspace')}
        </a>
      </div>
    {/if}
  </div>
</div>