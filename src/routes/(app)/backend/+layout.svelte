<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { WEBUI_NAME, showSidebar, user } from '$lib/stores';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import Navbar from '$lib/components/backend/Navbar.svelte';

	const i18n = getContext('i18n');

	let loaded = false;

	onMount(async () => {
		// 確認用戶授權 (根據需求可以調整)
		if ($user?.role !== 'admin') {
			await goto('/');
		}
		loaded = true;
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Backend Management')} | {$WEBUI_NAME}
	</title>
</svelte:head>

{#if loaded}
	<div
		class="flex flex-col w-full h-screen max-h-[100dvh] transition-width duration-200 ease-in-out {$showSidebar
			? 'md:max-w-[calc(100%-260px)]'
			: ''} max-w-full"
	>
		<Navbar />
		<div class="px-[16px] flex-1 max-h-full overflow-y-auto">
			<slot />
		</div>
	</div>
{:else}
	<div class="w-full flex-1 h-full flex items-center justify-center">
		<div
			class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900 dark:border-white"
		></div>
	</div>
{/if}
