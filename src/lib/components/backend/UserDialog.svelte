<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import { toast } from 'svelte-sonner';
	import { updateUserById } from '$lib/apis/users';
	import { addUser } from '$lib/apis/auths';
	import Modal from '$lib/components/common/Modal.svelte';
	import XMark from '$lib/components/icons/XMark.svelte';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<Writable<I18n>>('i18n');
	const dispatch = createEventDispatcher();

	// 對話框顯示控制
	export let show = false;
	// id
	export let id: string | null = null;
	// name
	export let name: string | null = null;
	// email
	export let email: string | null = null;

	// 判斷是否為編輯模式
	$: isEditMode = !!id;
	$: isAddMode = !isEditMode;

	// 表單數據
	let userForm = {
		name: '',
		email: '',
		password: '',
		profile_image_url: '/user.png',
		role: 'user'
	};

	// 當對話框打開時，初始化表單數據
	$: if (show) {
		userForm = {
			name: name || '',
			email: email || '',
			password: '', // 編輯時密碼為空，表示不修改
			profile_image_url: '/user.png',
			role: 'user' // 預設角色為 user
		};
	}

	// 儲存用戶資料
	const saveUser = async () => {
		const token = localStorage.getItem('token');
		if (!token) {
			toast.error($i18n.t('Authentication failed, please login again'));
			return;
		}

		try {
			if (id) {
				// 更新用戶
				await updateUserById(token, id, {
					name: userForm.name,
					email: userForm.email,
					password: userForm.password,
					profile_image_url: userForm.profile_image_url
				});
				toast.success($i18n.t('User updated successfully'));
			} else {
				// 添加用戶，指定角色為 user
				await addUser(token, userForm.name, userForm.email, userForm.password, userForm.role);
				toast.success($i18n.t('User added successfully'));
			}
			closeDialog(true);
		} catch (err) {
			console.error('Save user data failed:', err);
			toast.error(err instanceof Error ? err.message : String(err));
		}
	};

	// 關閉對話框
	const closeDialog = (saved: boolean = false) => {
		show = false;
		if (saved) {
			dispatch('save');
		} else {
			dispatch('cancel');
		}
	};
</script>

<Modal bind:show size="sm">
	<div>
		<div class="flex justify-between dark:text-gray-100 px-5 pt-4 pb-2">
			<div class="text-lg font-medium self-center font-primary">
				{isEditMode ? $i18n.t('Edit User') : $i18n.t('Add User')}
			</div>
			<button class="self-center" on:click={() => closeDialog()}>
				<XMark className="w-5 h-5" />
			</button>
		</div>

		<form on:submit|preventDefault={saveUser} class="flex flex-col space-y-4 px-5 pb-5">
			<div class="flex flex-col">
				<div class="mb-1 text-xs text-gray-500">{$i18n.t('name')}</div>
				<input
					type="text"
					bind:value={userForm.name}
					class="w-full rounded-lg py-2 px-3 text-sm dark:text-gray-300 dark:bg-gray-800 outline-hidden"
					placeholder={$i18n.t('Enter you name')}
					required
				/>
			</div>

			<div class="flex flex-col">
				<div class="mb-1 text-xs text-gray-500">{$i18n.t('email')}</div>
				<input
					type="email"
					bind:value={userForm.email}
					class="w-full rounded-lg py-2 px-3 text-sm dark:text-gray-300 dark:bg-gray-800 outline-hidden"
					placeholder={$i18n.t('Enter your email')}
					required
				/>
			</div>

			<div class="flex flex-col">
				<div class="mb-1 text-xs text-gray-500">
					{isEditMode ? $i18n.t('Password') + `(${$i18n.t('leave empty to keep unchanged')})` : $i18n.t('Password')}
				</div>
				<input
					type="password"
					bind:value={userForm.password}
					class="w-full rounded-lg py-2 px-3 text-sm dark:text-gray-300 dark:bg-gray-800 outline-hidden"
					placeholder={$i18n.t('Please enter password')}
					required={isAddMode}
				/>
			</div>

			<div class="flex justify-end gap-2 mt-4">
				<button
					type="button"
					class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition"
					on:click={() => closeDialog()}
				>
					{$i18n.t('Cancel')}
				</button>
				<button
					type="submit"
					class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
				>
					{$i18n.t('Save')}
				</button>
			</div>
		</form>
	</div>
</Modal>
