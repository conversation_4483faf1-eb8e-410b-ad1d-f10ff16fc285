<script lang="ts">
	import { getContext } from 'svelte';
	import { WEBUI_NAME, showSidebar } from '$lib/stores';
	import ChevronLeft from '$lib/components/icons/ChevronLeft.svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import type { Writable } from 'svelte/store';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<Writable<I18n>>('i18n');
</script>

<nav class="px-2.5 h-16 backdrop-blur-xl drag-region flex items-center">
	<div class="flex items-center gap-1 w-full">
		<button
			id="back-button"
			class="shrink-0 mr-2 p-1.5 hover:bg-black/5 dark:hover:bg-white/5 rounded-lg transition flex items-center gap-1 text-sm"
			on:click={() => {
				goto('/');
			}}
			aria-label="Go Back"
		>
			<ChevronLeft className="size-4" />
			<span>{$i18n.t('Back')}</span>
		</button>

		<div class="flex w-full">
			<div
				class="flex gap-1 scrollbar-none overflow-x-auto w-fit text-center text-sm font-medium rounded-full bg-transparent items-center"
			>
				<a
					class="min-w-fit rounded-full p-1.5 {$page.url.pathname.includes('/backend/users')
						? ''
						: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition"
					href="/backend/users">{$i18n.t('{{type}} Management', { type: $i18n.t('Users') })}</a
				>
				<a
					class="min-w-fit rounded-full p-1.5 {$page.url.pathname.includes('/backend/groups')
						? ''
						: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition"
					href="/backend/groups">{$i18n.t('{{type}} Management', { type: $i18n.t('Groups') })}</a
				>
				<a
					class="min-w-fit rounded-full p-1.5 {$page.url.pathname.includes('/backend/settings')
						? ''
						: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition"
					href="/backend/settings">{$i18n.t('Settings')}</a
				>
			</div>
		</div>
	</div>
</nav>
