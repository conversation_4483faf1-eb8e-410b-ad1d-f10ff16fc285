<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import { toast } from 'svelte-sonner';
	import { getUsers, deleteUserById, updateUserRole } from '$lib/apis/users/index';
	import type { User } from '$lib/models/user';
	import UserDialog from './UserDialog.svelte';
	import ConfirmDialog from '$lib/components/common/ConfirmDialog.svelte';
	import WrenchSolid from '$lib/components/icons/WrenchSolid.svelte';
	import GarbageBin from '$lib/components/icons/GarbageBin.svelte';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<Writable<I18n>>('i18n');

	let users: User[] = [];
	let loading = true;
	let searchQuery = '';
	let error = '';

	// 用戶編輯/添加對話框狀態
	let showUserModal = false;
	let selectedUser: User | null = null;

	// 確認刪除對話框狀態
	let showDeleteConfirm = false;
	let userToDelete: string | null = null;

	// 搜索用戶
	$: filteredUsers = users.filter(
		(user) =>
			user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
			user.email?.toLowerCase().includes(searchQuery.toLowerCase())
	);

	// 從 API 獲取用戶資料
	const fetchUsers = async () => {
		loading = true;
		try {
			const token = localStorage.getItem('token');
			if (!token) {
				toast.error($i18n.t('Authentication failed, please login again'));
				return;
			}

			const userData = await getUsers(token);
			users = userData.users;
		} catch (err) {
			console.error('Failed to get user data:', err);
			error = err instanceof Error ? err.message : String(err);
			toast.error($i18n.t('Failed to get user list'));
		} finally {
			loading = false;
		}
	};

	// 開啟添加用戶對話框
	const openAddUserModal = () => {
		selectedUser = null;
		showUserModal = true;
	};

	// 開啟編輯用戶對話框
	const openEditUserModal = (user: User) => {
		selectedUser = user;
		showUserModal = true;
	};

	// 處理對話框保存事件
	const handleSave = () => {
		fetchUsers(); // 重新載入用戶列表
	};

	// 確認刪除用戶對話框
	const confirmDeleteUser = (userId: string) => {
		userToDelete = userId;
		showDeleteConfirm = true;
	};

	// 刪除用戶
	const deleteUser = async () => {
		if (!userToDelete) return;

		try {
			const token = localStorage.getItem('token');
			if (!token) {
				toast.error($i18n.t('Authentication failed, please login again'));
				return;
			}

			const deletingToast = toast.loading($i18n.t('Deleting user, please wait...'));

			await deleteUserById(token, userToDelete);
			toast.dismiss(deletingToast);
			toast.success($i18n.t('User deleted'));

			// 更新用戶列表
			users = users.filter((user) => user.id !== userToDelete);
		} catch (err) {
			console.error('Failed to delete user:', err);
			toast.error($i18n.t('Failed to delete user') + (err instanceof Error ? err.message : String(err)));
		} finally {
			userToDelete = null;
			showDeleteConfirm = false;
		}
	};

	onMount(() => {
		fetchUsers();
	});
</script>

<div class="flex flex-col w-full min-h-full">
	<!-- 搜索和篩選區域 -->
	<div class="mb-4 flex flex-wrap gap-2">
		<div class="flex-grow">
			<input
				type="text"
				bind:value={searchQuery}
				placeholder={$i18n.t('Search User') + '...'}
				class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
			/>
		</div>
		<button
			class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
			on:click={openAddUserModal}
		>
			{$i18n.t('Add User')}
		</button>
	</div>

	<!-- 用戶列表 -->
	{#if loading}
		<div class="flex justify-center py-8">
			<div
				class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900 dark:border-white"
			></div>
		</div>
	{:else if error}
		<div class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 p-4 rounded-lg">
			<p>{$i18n.t('Load User Data Error')}: {error}</p>
			<button class="mt-2 text-sm underline" on:click={fetchUsers}>
				{$i18n.t('Retry')}
			</button>
		</div>
	{:else}
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
					<thead class="bg-gray-50 dark:bg-gray-700">
						<tr>
							<th
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
							>
								{$i18n.t('Name')}
							</th>
							<th
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
							>
								{$i18n.t('Email')}
							</th>
							<th
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
							>
								{$i18n.t('Role')}
							</th>
							<th
								class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
							>
								{$i18n.t('Actions')}
							</th>
						</tr>
					</thead>
					<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
						{#each filteredUsers as user}
							<tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
									{user.name}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
									{user.email}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
									<div class="relative w-full">
										<select
											class="appearance-none w-full py-2 pl-7 pr-10 border border-gray-200 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-transparent transition-all duration-200 bg-white dark:bg-gray-800 cursor-pointer"
											value={user.role ?? 'pending'}
											on:change={async (e) => {
												const token = localStorage.getItem('token');
												if (!token) {
													toast.error($i18n.t('Authentication failed, please login again'));
													return;
												}

												const newRole = e.currentTarget.value;

												// 顯示正在更新的提示
												const updatingToast = toast.loading($i18n.t('Updating user role'));

												try {
													await updateUserRole(token, user.id ?? '', newRole);
													toast.dismiss(updatingToast);
													toast.success($i18n.t('User role updated'));
													// 更新用戶角色
													user.role = newRole;
												} catch (err) {
													toast.dismiss(updatingToast);
													toast.error(
														$i18n.t('Failed to update user role') +
															(err instanceof Error ? err.message : String(err))
													);
												}
											}}
										>
											<option
												value="admin"
												class="py-2 px-3 hover:bg-blue-50 dark:hover:bg-blue-900/20"
											>
												admin
											</option>
											<option
												value="user"
												class="py-2 px-3 hover:bg-green-50 dark:hover:bg-green-900/20"
											>
												user
											</option>
											<option
												value="pending"
												class="py-2 px-3 hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
											>
												pending
											</option>
										</select>
										<!-- 角色顏色標記 -->
										<div
											class="absolute left-0 inset-y-0 flex items-center ml-2.5 pointer-events-none"
										>
											<div
												class={`w-3 h-3 rounded-full ${
													user.role === 'admin'
														? 'bg-blue-500'
														: user.role === 'user'
															? 'bg-green-500'
															: 'bg-yellow-500'
												}`}
											></div>
										</div>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<button
										class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200 mr-2"
										on:click={() => openEditUserModal(user)}
									>
										<WrenchSolid className="size-4 inline-block" />
										<span class="sr-only">{$i18n.t('Edit')}</span>
									</button>
									<button
										class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200"
										on:click={() => confirmDeleteUser(user.id ?? '')}
									>
										<GarbageBin className="w-4 h-4 inline-block" />
										<span class="sr-only">{$i18n.t('Delete')}</span>
									</button>
								</td>
							</tr>
						{:else}
							<tr>
								<td
									colspan="4"
									class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
								>
									{$i18n.t('User not found')}
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		</div>
	{/if}

	<!-- 引入用戶編輯/添加對話框組件 -->
	<UserDialog
		bind:show={showUserModal}
		on:save={handleSave}
		id={selectedUser?.id}
		name={selectedUser?.name}
		email={selectedUser?.email}
	/>

	<!-- 確認刪除對話框 -->
	<ConfirmDialog
		bind:show={showDeleteConfirm}
		title={$i18n.t('Confirm Delete')}
		message={$i18n.t('Are you sure you want to delete this user? This action cannot be undone, and all user data will be permanently deleted.')}
		confirmLabel={$i18n.t('Delete')}
		cancelLabel={$i18n.t('Cancel')}
		onConfirm={deleteUser}
	/>
</div>
