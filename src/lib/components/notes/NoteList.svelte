<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { goto } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import { WEBUI_NAME } from '$lib/stores';
	import {
		getNotes,
		createNewNote,
		deleteNoteById,
		getNoteById,
		updateNoteById
	} from '$lib/apis/notes';
	import { getTimeRange } from '$lib/utils';
	import type { Writable } from 'svelte/store';
	import type { I18n } from '$lib/models/i18n';

	// 导入图标组件
	import Search from '../icons/Search.svelte';
	import Plus from '../icons/Plus.svelte';
	import DeleteConfirmDialog from '$lib/components/common/ConfirmDialog.svelte';
	import Spinner from '../common/Spinner.svelte';
	import ByteMDEditor from '../common/ByteMDEditor.svelte';

	const i18n = getContext<Writable<I18n>>('i18n');
	let loaded = false;

	// 定义笔记类型
	interface NoteContent {
		json: null;
		html: string;
		md: string;
	}

	interface NoteData {
		content: NoteContent;
		[key: string]: any;
	}

	interface NoteTag {
		name: string;
		color?: string;
		textColor?: string;
	}

	interface NoteMeta {
		tags?: NoteTag[];
		[key: string]: any;
	}

	interface Note {
		id: string;
		title: string;
		data: NoteData;
		meta?: NoteMeta | null;
		access_control?: any;
		updated_at: number;
		created_at: number;
		timeRange?: string;
		[key: string]: any;
	}

	// 状态变量
	let query = '';
	let notes: Record<string, Note[]> = {};
	let filteredNotes: Record<string, Note[]> = {};
	let selectedNote: Note | null = null;
	let showDeleteConfirm = false;
	let activeFilter = 'all'; // 当前激活的过滤器
	let loading = false; // 加载状态
	let noteContent = null; // 当前选中笔记的内容
	let isEditing = false; // 是否处于编辑模式
	let isEditingTitle = false; // 是否处于标题编辑模式
	let editableTitle = ''; // 可编辑标题

	// 类型断言函数
	function isNote(obj: any): obj is Note {
		return obj && typeof obj === 'object' && 'id' in obj;
	}

	// 初始化函数，获取笔记列表
	const init = async (autoSelectFirst = true) => {
		try {
			notes = await getNotes(localStorage.token);
			applyFilter(activeFilter);

			// 如果有笔记且需要自动选择，默认选择第一个
			if (autoSelectFirst && Object.keys(notes).length > 0) {
				const firstTimeRange = Object.keys(notes)[0];
				if (notes[firstTimeRange] && notes[firstTimeRange].length > 0) {
					await selectNote(notes[firstTimeRange][0]);
				}
			}
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 选择笔记并加载详细内容
	const selectNote = async (note: Note) => {
		if (!note || !note.id) return;

		loading = true;
		selectedNote = note;
		isEditing = false; // 重置编辑模式
		isEditingTitle = false; // 重置标题编辑模式

		try {
			// 获取完整的笔记内容
			const fullNote = await getNoteById(localStorage.token, note.id);
			if (fullNote) {
				selectedNote = fullNote;
				noteContent = fullNote.data?.content;
			}
		} catch (error) {
			toast.error(`${error}`);
		} finally {
			loading = false;
		}
	};

	// 创建新笔记
	const createNoteHandler = async () => {
		try {
			const res = await createNewNote(localStorage.token, {
				title: $i18n.t('New Note'),
				data: {
					content: {
						json: null,
						html: '',
						md: ''
					}
				},
				meta: null,
				access_control: null
			});

			if (res) {
				goto(`/notes/${res.id}`);
			}
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 删除笔记
	const deleteNoteHandler = async (id: string) => {
		try {
			const deletedNoteId = id;
			await deleteNoteById(localStorage.token, id);
			
			// 清空当前选中的笔记状态
			selectedNote = null;
			noteContent = null;
			isEditing = false;
			isEditingTitle = false;
			
			// 重新获取笔记列表，但不自动选择第一个
			await init(false);
			
			// 如果删除后还有笔记，选择第一个可用的笔记
			if (Object.keys(notes).length > 0) {
				const firstTimeRange = Object.keys(notes)[0];
				if (notes[firstTimeRange] && notes[firstTimeRange].length > 0) {
					// 选择第一个可用的笔记
					await selectNote(notes[firstTimeRange][0]);
				}
			}
			
			toast.success($i18n.t('Note deleted'));
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 应用过滤器
	const applyFilter = (filter: string) => {
		activeFilter = filter;

		if (!notes || Object.keys(notes).length === 0) {
			filteredNotes = {};
			return;
		}

		// 如果有搜索查询，先按查询过滤
		if (query.trim() !== '') {
			const searchResults: Record<string, Note[]> = {};

			Object.keys(notes).forEach((timeRange) => {
				if (notes[timeRange]) {
					const matchingNotes = notes[timeRange].filter(
						(note) =>
							note.title.toLowerCase().includes(query.toLowerCase()) ||
							(note.data?.content?.md &&
								note.data.content.md.toLowerCase().includes(query.toLowerCase()))
					);

					if (matchingNotes.length > 0) {
						searchResults[timeRange] = matchingNotes;
					}
				}
			});

			filteredNotes = searchResults;
		} else {
			filteredNotes = { ...notes };
		}

		// 然后应用标签过滤器
		if (filter !== 'all') {
			const filteredResults: Record<string, Note[]> = {};

			Object.keys(filteredNotes).forEach((timeRange) => {
				if (filteredNotes[timeRange]) {
					const matchingNotes = filteredNotes[timeRange].filter((note) => {
						// 这里假设笔记有标签字段，根据实际数据结构调整
						const tags = note.meta?.tags || [];
						return tags.some((tag) => tag.name === filter);
					});

					if (matchingNotes.length > 0) {
						filteredResults[timeRange] = matchingNotes;
					}
				}
			});

			filteredNotes = filteredResults;
		}
	};

	// 监听搜索查询变化
	$: if (loaded) {
		applyFilter(activeFilter);
	}

	// 生命周期钩子
	onMount(async () => {
		await init();
		loaded = true;
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Notes')} • {$WEBUI_NAME}
	</title>
</svelte:head>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
	{#if showDeleteConfirm && selectedNote !== null && typeof selectedNote === 'object' && 'id' in selectedNote}
		<DeleteConfirmDialog
			bind:show={showDeleteConfirm}
			title={$i18n.t('Delete note?')}
			on:confirm={() => {
				if (selectedNote && 'id' in selectedNote) {
					deleteNoteHandler(selectedNote.id);
					showDeleteConfirm = false;
				}
			}}
		>
			<div class="text-sm text-gray-500 dark:text-gray-400">
				{$i18n.t('This will delete')} <span class="font-semibold">{selectedNote.title}</span>.
			</div>
		</DeleteConfirmDialog>
	{/if}

	<!-- 笔记界面 -->
	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 px-4 sm:px-0 lg:items-start">
		<!-- 笔记列表侧边栏 -->
		<div
			class="lg:col-span-1 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm"
		>
			<!-- 搜索栏 -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<div class="flex gap-2">
					<div class="relative flex-1">
						<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<Search className="h-4 w-4 text-gray-400" />
						</div>
						<input
							type="search"
							bind:value={query}
							on:input={() => applyFilter(activeFilter)}
							class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-offset-2 focus:ring-black dark:focus:ring-white focus:border-black dark:focus:border-white text-sm dark:bg-gray-700 dark:text-white"
							placeholder={$i18n.t('Search notes...')}
						/>
					</div>
					<button
						type="button"
						class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded text-white bg-black dark:bg-gray-700 hover:bg-gray-800 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white flex-shrink-0"
						on:click={createNoteHandler}
					>
						<Plus className="h-4 w-4 mr-1" />
						{$i18n.t('New Note')}
					</button>
				</div>
			</div>

			<!-- 笔记列表 -->
			<div class="overflow-auto">
				{#if !loaded}
					<div class="flex justify-center items-center h-40">
						<Spinner />
					</div>
				{:else if Object.keys(filteredNotes).length === 0}
					<div class="flex flex-col items-center justify-center h-32 text-center px-4">
						<p class="text-gray-500 dark:text-gray-400 text-sm">{$i18n.t('No notes found')}</p>
					</div>
				{:else}
					<ul class="divide-y divide-gray-200 dark:divide-gray-700">
						{#each Object.keys(filteredNotes) as timeRange}
							{#if filteredNotes[timeRange] && Array.isArray(filteredNotes[timeRange])}
								{#each filteredNotes[timeRange] as note (note.id)}
									{#if note && typeof note === 'object' && 'id' in note}
										{@const noteId = note.id}
										{@const noteTitle = note.title}
										{@const noteUpdatedAt = note.updated_at}
										{@const noteContent = note.data?.content?.md || ''}
										{@const noteTags = note.meta?.tags || []}

										<li
											class={noteId ===
											(selectedNote && 'id' in selectedNote ? selectedNote.id : null)
												? 'bg-gray-50 dark:bg-gray-700'
												: ''}
										>
											<button
												type="button"
												class="block w-full text-left py-3 px-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
												on:click={() => selectNote(note)}
												on:keydown={(e) => e.key === 'Enter' && selectNote(note)}
											>
												<div class="flex items-center justify-between mb-1">
													<h3 class="text-sm font-medium text-gray-900 dark:text-white">
														{noteTitle}
													</h3>
													<span class="text-xs text-gray-500 dark:text-gray-400"
														>{getTimeRange(noteUpdatedAt / 1000000000)}</span
													>
												</div>
												<p class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
													{noteContent
														? noteContent.substring(0, 150) +
															(noteContent.length > 150 ? '...' : '')
														: ''}
												</p>
												{#if Array.isArray(noteTags) && noteTags.length > 0}
													<div class="flex items-center mt-2 flex-wrap gap-1">
														{#each noteTags as tag}
															{#if tag && typeof tag === 'object' && 'name' in tag}
																<span
																	class="text-xs font-medium px-2 py-0.5 rounded-full"
																	style="background-color: {tag.color ||
																		'#e5e7eb'}; color: {tag.textColor || '#374151'}"
																>
																	{tag.name}
																</span>
															{/if}
														{/each}
													</div>
												{/if}
											</button>
										</li>
									{/if}
								{/each}
							{/if}
						{/each}
					</ul>
				{/if}
			</div>
		</div>

		<!-- 笔记内容区域 -->
		<div
			class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden flex flex-col"
		>
			{#if selectedNote && selectedNote.id}
				<!-- 笔记详情 -->
				<div class="flex flex-col h-full">
					<!-- 笔记标题 -->
					<div class="p-4 border-b border-gray-200 dark:border-gray-700">
						<div class="flex justify-between items-center">
							{#if isEditingTitle}
								<input
									type="text"
									bind:value={editableTitle}
									class="text-xl font-semibold text-gray-900 dark:text-white bg-transparent border-b border-gray-300 dark:border-gray-600 focus:outline-none focus:border-primary-500 w-full mr-2"
									placeholder={$i18n.t('Enter title...')}
									on:keydown={(e) => {
										if (e.key === 'Enter') {
											e.preventDefault(); // Prevent form submission if any
											if (selectedNote && selectedNote.id) {
												selectedNote.title = editableTitle;
												updateNoteById(localStorage.token, selectedNote.id, selectedNote)
													.then(async () => {
														// Make callback async
														toast.success($i18n.t('Note updated'));
														isEditing = false;
														isEditingTitle = false;

														const currentNoteId = selectedNote?.id; // Save current note ID
														await init(); // Refresh the entire list

														if (currentNoteId) {
															// If there was a selected note
															let noteToReselect = null;
															// Find the note in the refreshed 'notes' object
															for (const timeGroup in notes) {
																const foundNote = notes[timeGroup].find(
																	(n) => n.id === currentNoteId
																);
																if (foundNote) {
																	noteToReselect = foundNote;
																	break;
																}
															}
															if (noteToReselect) {
																await selectNote(noteToReselect); // Reselect the note
															}
														}
													})
													.catch((error) => {
														toast.error(`${error}`);
													});
											}
										} else if (e.key === 'Escape') {
											if (selectedNote && selectedNote.id) {
												selectNote(selectedNote); // Revert title and content by re-selecting
												isEditing = false;
												isEditingTitle = false;
											}
										}
									}}
								/>
							{:else}
								<h2
									class="text-xl font-semibold text-gray-900 dark:text-white truncate mr-2"
									title={selectedNote.title}
								>
									{selectedNote.title}
								</h2>
							{/if}
							<div class="flex space-x-2 flex-shrink-0">
								<button
									type="button"
									class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white"
									on:click={() => {
										if (isEditing) {
											// Save action
											if (selectedNote && selectedNote.id) {
												if (isEditingTitle) {
													selectedNote.title = editableTitle;
												}
												updateNoteById(localStorage.token, selectedNote.id, selectedNote)
													.then(async () => {
														// Make callback async
														toast.success($i18n.t('Note updated'));
														isEditing = false;
														isEditingTitle = false; // Exit title editing mode

														const currentNoteId = selectedNote?.id; // Save current note ID
														await init(); // Refresh the entire list

														if (currentNoteId) {
															// If there was a selected note
															let noteToReselect = null;
															// Find the note in the refreshed 'notes' object
															for (const timeGroup in notes) {
																const foundNote = notes[timeGroup].find(
																	(n) => n.id === currentNoteId
																);
																if (foundNote) {
																	noteToReselect = foundNote;
																	break;
																}
															}
															if (noteToReselect) {
																await selectNote(noteToReselect); // Reselect the note
															}
														}
													})
													.catch((error) => {
														toast.error(`${error}`);
														// Keep editing mode active on error
													});
											}
										} else {
											// 点击编辑按钮时，跳转到笔记编辑页面
											if (selectedNote && selectedNote.id) {
												goto(`/notes/${selectedNote.id}`);
											}
										}
									}}
								>
									{isEditing ? $i18n.t('Save') : $i18n.t('Edit')}
								</button>
								{#if isEditing}
									<button
										type="button"
										class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white"
										on:click={() => {
											// Cancel action
											if (selectedNote && selectedNote.id) {
												selectNote(selectedNote); // This should reload original title and content
												isEditing = false;
												isEditingTitle = false; // Exit title editing mode
											}
										}}
									>
										{$i18n.t('Cancel')}
									</button>
								{:else}
									<button
										type="button"
										class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white"
										on:click={() => {
											showDeleteConfirm = true;
										}}
									>
										{$i18n.t('Delete')}
									</button>
								{/if}
							</div>
						</div>
						<div class="mt-1 text-sm text-gray-500 dark:text-gray-400">
							{$i18n.t('Last updated')}: {getTimeRange(selectedNote.updated_at / 1000000000)}
						</div>
					</div>

					<!-- 笔记内容 -->
					<div class="flex-1 overflow-auto p-4">
						{#if loading}
							<div class="flex justify-center items-center h-full">
								<Spinner />
							</div>
						{:else}
							<div class="prose dark:prose-invert max-w-none">
								<ByteMDEditor
									value={selectedNote.data.content.md}
									placeholder={$i18n.t('No content')}
									editable={false}
									on:change={(e) => {
										if (isEditing && selectedNote) {
											selectedNote.data.content.html = e.detail.html;
											selectedNote.data.content.md = e.detail.md;
										}
									}}
								/>
							</div>
						{/if}
					</div>
				</div>
			{:else}
				<!-- 空状态 -->
				<div class="flex items-center justify-center h-full p-8 text-center">
					<div>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
							/>
						</svg>
						<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">
							{$i18n.t('Select a note or create a new one')}
						</h3>
						<p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
							{$i18n.t('Your notes will appear here')}
						</p>
					</div>
				</div>
			{/if}
		</div>
	</div>
</div>
