<script lang="ts">
    import { getContext } from 'svelte';
    import type { Writable } from 'svelte/store';
    import type { I18n } from '$lib/models/i18n';
    import PlusIcon from '$lib/components/icons/PlusIcon.svelte';

    const i18n = getContext<Writable<I18n>>('i18n');

    export let onClick: () => void;
</script>

<div
    role="button"
    tabindex="0"
    class="flex items-center p-3 rounded-lg border border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-400 bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-colors cursor-pointer"
    on:click={onClick}
    on:keydown={(e) => {
        if (e.key === 'Enter') onClick();
    }}
>
    <div
        class="size-10 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center mr-4"
    >
        <PlusIcon className="size-5 text-blue-500" />
    </div>
    <div class="flex-1">
        <h3 class="text-base font-medium text-blue-500 dark:text-blue-400">
            {$i18n.t('Create a new knowledge base')}
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400">
            {$i18n.t('Create a new knowledge base')}
        </p>
    </div>
</div>
