<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import { toast } from 'svelte-sonner';
	import { getGroups } from '$lib/apis/groups';
	import type { Group } from '$lib/models/group';

	const i18n = getContext<Writable<I18n>>('i18n');

	import { goto } from '$app/navigation';
	import {
		getKnowledgeBases,
		deleteKnowledgeById,
		updateKnowledgeById,
		createNewKnowledge
	} from '$lib/apis/knowledge';
	import type { Collection } from '$lib/models/collection';
	import { user, showArchivedChats, theme } from '$lib/stores'; // 添加必要的 stores
	import Spinner from '../common/Spinner.svelte';
	import NotebookCard from './NotebookCard.svelte';
	import NotebookListItem from './NotebookListItem.svelte';
	import SearchBar from './SearchBar.svelte';
	import ConfirmDialog from '../common/ConfirmDialog.svelte';
	import InputDialog from '../common/InputDialog.svelte';
	import UserMenu from '$lib/components/layout/Sidebar/UserMenuLite.svelte'; // 添加 UserMenu 組件
	import ViewGrid from '$lib/components/icons/ViewGrid.svelte';
	import ViewList from '$lib/components/icons/ViewList.svelte';
	import ChevronDown from '$lib/components/icons/ChevronDown.svelte';
	import Cog6Solid from '$lib/components/icons/Cog6Solid.svelte';
	import ThemeToggle from '$lib/components/common/ThemeToggle.svelte';
	import type { I18n } from '$lib/models/i18n';
	import CreateKnowledgeCard from './CreateKnowledgeCard.svelte';
	import CreateKnowledgeListItem from './CreateKnowledgeListItem.svelte';
	import CreateKnowledgeEmptyState from './CreateKnowledgeEmptyState.svelte';

	// 狀態變數
	let loaded = false;
	let collections: Collection[] = [];
	let searchQuery = '';
	let viewMode: 'grid' | 'list' = 'grid';
	let sortBy: 'newest' | 'oldest' | 'name' = 'newest';

	// 用戶群組
	let userGroups: Group[] = [];

	// 刪除確認對話框
	let showDeleteConfirm = false;
	let collectionToDelete: string | null = null;

	// 輸入對話框
	let showInputDialog = false;
	let inputDialogValue = '';
	let collectionToUpdate: string | null = null;

	// 創建知識庫對話框
	let showCreateDialog = false;
	let newKnowledgeNameValue = '';

	// 過濾集合列表
	$: filteredCollections = collections
		.filter(
			(collection) => collection.name?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false
		)
		.sort((a, b) => {
			if (sortBy === 'newest') return (b.updated_at ?? 0) - (a.updated_at ?? 0);
			if (sortBy === 'oldest') return (a.updated_at ?? 0) - (b.updated_at ?? 0);
			if (sortBy === 'name') return (a.name ?? '').localeCompare(b.name ?? '');
			return 0;
		});

	// 初始加載數據
	onMount(async () => {
		try {
			// 同時加載知識庫和用戶群組
			const [collectionsData, groupsData] = await Promise.all([
				getKnowledgeBases(localStorage.token),
				getGroups(localStorage.token)
			]);

			collections = collectionsData;
			userGroups = groupsData;
			loaded = true;
		} catch (e) {
			toast.error(`${e}`);
		}
	});

	// 顯示或隱藏創建知識庫彈窗
	const toggleCreateDialog = () => {
		showCreateDialog = !showCreateDialog;
		if (showCreateDialog) {
			newKnowledgeNameValue = '';
		}
	};

	// 切換視圖模式
	const toggleViewMode = (mode: 'grid' | 'list') => {
		viewMode = mode;
	};

	// 切換排序方式
	const setSortBy = (sort: 'newest' | 'oldest' | 'name') => {
		sortBy = sort;
	};

	// 開啟集合
	/**
	 * 打開一個集合
	 * @param id - 集合的ID
	 */
	const openCollection = (id: string) => {
		goto(`/knowledge/${id}`);
	};

	// 確認刪除知識庫
	const confirmDelete = (id: string) => {
		collectionToDelete = id;
		showDeleteConfirm = true;
	};

	// 刪除知識庫
	const deleteCollection = async () => {
		if (!collectionToDelete) return;

		const deletingToast = toast.loading($i18n.t('Deleting knowledge base, please wait') + '...');

		try {
			await deleteKnowledgeById(localStorage.token, collectionToDelete);
			collections = collections.filter((c) => c.id !== collectionToDelete);
			toast.dismiss(deletingToast);
			toast.success($i18n.t('Deleted Knowledge base'));
		} catch (e) {
			toast.dismiss(deletingToast);
			toast.error($i18n.t('Delete failed') + `: ${e}`);
		} finally {
			collectionToDelete = null;
		}
	};

	// 更新知識庫
	const updateCollection = (collection: Collection) => {
		collectionToUpdate = collection.id ?? '';
		showInputDialog = true;
	};

	// 處理更新確認
	const handleUpdateConfirm = async (data: {
		value: string;
		accessControl: any;
		knowledge: any;
	}) => {
		if (!collectionToUpdate || !data.value.trim()) return;

		const renamingToast = toast.loading($i18n.t('Renaming knowledge base') + '...');

		try {
			// 使用從 InputDialog 獲取的完整知識庫數據
			const knowledge = data.knowledge;

			// 如果有完整的知識庫數據，則使用它，否則回退到舊方法
			if (knowledge) {
				await updateKnowledgeById(localStorage.token, collectionToUpdate, {
					...knowledge,
					name: data.value.trim(),
					description: knowledge.description || data.value.trim(),
					access_control: data.accessControl
				});
			}

			// 更新本地集合列表
			collections = collections.map((c) =>
				c.id === collectionToUpdate ? { ...c, name: data.value.trim() } : c
			);

			toast.dismiss(renamingToast);
			toast.success($i18n.t('Rename Knowledge Base Succeed'));
		} catch (e) {
			toast.dismiss(renamingToast);
			toast.error($i18n.t('Rename Knowledge Base Failed') + `: ${e}`);
		} finally {
			collectionToUpdate = null;
		}
	};

	// 創建新知識庫
	const handleCreateConfirm = async (data: {
		value: string;
		accessControl: any;
		knowledge: Collection;
	}) => {
		if (!data.value.trim()) return;

		const creatingToast = toast.loading($i18n.t('Creating a new knowledge base, please wait...'));

		try {
			// 創建新知識庫時不需要使用 knowledgeData，因為這是新建的
			const result = await createNewKnowledge(
				localStorage.token,
				data.value.trim(),
				data.value.trim(),
				data.accessControl // 使用用戶設置的訪問控制
			);

			if (result && result.id) {
				// 更新知識庫列表
				collections = await getKnowledgeBases(localStorage.token);
				toast.dismiss(creatingToast);
				toast.success($i18n.t('Create knowledge base succeed'));
				// 打開新創建的知識庫
				openCollection(result.id);
			}
		} catch (e) {
			toast.dismiss(creatingToast);
			toast.error($i18n.t('Create knowledge base failed') + `: ${e}`);
		}
	};
</script>

<div class="w-full">
	<div class="p-4 md:p-6">
		<div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
			<SearchBar bind:value={searchQuery} onChange={(value) => (searchQuery = value)} />
			<div class="flex flex-col md:flex-row gap-4 w-full md:w-auto">
				<div class="flex items-center gap-2">
					<!-- View toggle buttons -->
					<div class="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
						<button
							class="p-1.5 rounded-md {viewMode === 'grid'
								? 'bg-white dark:bg-gray-700 shadow-sm'
								: 'text-gray-500 dark:text-gray-400'}"
							on:click={() => toggleViewMode('grid')}
							aria-label="Grid view"
						>
							<ViewGrid className="size-5" />
						</button>
						<button
							class="p-1.5 rounded-md {viewMode === 'list'
								? 'bg-white dark:bg-gray-700 shadow-sm'
								: 'text-gray-500 dark:text-gray-400'}"
							on:click={() => toggleViewMode('list')}
							aria-label="List view"
						>
							<ViewList className="size-5" />
						</button>
					</div>

					<!-- Sort dropdown -->
					<div class="relative">
						<button
							class="flex items-center gap-1 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
							on:click={() => {
								if (sortBy === 'newest') setSortBy('oldest');
								else if (sortBy === 'oldest') setSortBy('name');
								else setSortBy('newest');
							}}
						>
							<span
								>{$i18n.t(
									sortBy === 'newest'
										? $i18n.t('latest')
										: sortBy === 'oldest'
											? $i18n.t('oldest')
											: $i18n.t('name')
								)}</span
							>
							<ChevronDown />
						</button>
					</div>
				</div>
			</div>
		</div>

		{#if loaded}
			{#if filteredCollections.length > 0}
				{#if viewMode === 'grid'}
					<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
						<!-- 新建知識庫卡片 -->
						<CreateKnowledgeCard onClick={toggleCreateDialog} />

						{#each filteredCollections as collection}
							<NotebookCard
								{collection}
								onOpen={openCollection}
								onDelete={confirmDelete}
								onEdit={() => updateCollection(collection)}
								{userGroups}
							/>
						{/each}
					</div>
				{:else}
					<div class="flex flex-col gap-2">
						<!-- 新建知識庫列表項 -->
						<CreateKnowledgeListItem onClick={toggleCreateDialog} />

						{#each filteredCollections as collection}
							<NotebookListItem
								{collection}
								onOpen={openCollection}
								onDelete={confirmDelete}
								onEdit={() => updateCollection(collection)}
								{userGroups}
							/>
						{/each}
					</div>
				{/if}
			{:else}
				<CreateKnowledgeEmptyState
					onClick={toggleCreateDialog}
					onClearSearch={() => (searchQuery = '')}
					{searchQuery}
				/>
			{/if}
		{:else}
			<div class="flex items-center justify-center h-48">
				<Spinner />
			</div>
		{/if}

		<!-- 刪除確認對話框 -->
		<ConfirmDialog
			show={showDeleteConfirm}
			title={$i18n.t('Confirm delete')}
			message={$i18n.t(
				'This operation cannot be undone, are you sure you want to delete this knowledge base?'
			)}
			cancelLabel={$i18n.t('Cancel')}
			confirmLabel={$i18n.t('Delete')}
			onConfirm={deleteCollection}
			on:cancel={() => (showDeleteConfirm = false)}
			on:confirm={() => (showDeleteConfirm = false)}
		/>

		<!-- 更新對話框 -->
		<InputDialog
			show={showInputDialog}
			title={$i18n.t('Rename Knowledge Base')}
			message={$i18n.t('Please enter a new knowledge base name')}
			placeholder={$i18n.t('Knowledge base name')}
			bind:inputValue={inputDialogValue}
			showPermissions={true}
			collectionId={collectionToUpdate}
			on:confirm={(e) => {
				showInputDialog = false;
				handleUpdateConfirm(e.detail);
			}}
			on:cancel={() => {
				showInputDialog = false;
				collectionToUpdate = null;
			}}
		/>

		<!-- 新建知識庫對話框 -->
		<InputDialog
			show={showCreateDialog}
			title={$i18n.t('Create a new knowledge base')}
			message={$i18n.t('Name your knowledge base')}
			placeholder={$i18n.t('Knowledge base name')}
			bind:inputValue={newKnowledgeNameValue}
			showPermissions={true}
			on:confirm={(e) => handleCreateConfirm(e.detail)}
			on:cancel={() => {
				showCreateDialog = false;
				newKnowledgeNameValue = '';
			}}
		/>
	</div>
</div>
