<script lang="ts">
    import CreateKnowledgeButton from './CreateKnowledgeButton.svelte';

    export let onClick: () => void;
</script>

<div
    role="button"
    tabindex="0"
    class="flex flex-col border border-dashed border-gray-300 dark:border-gray-600 rounded-xl overflow-hidden hover:border-blue-400 dark:hover:border-blue-400 transition-colors cursor-pointer bg-white dark:bg-gray-800 shadow-sm hover:shadow-md"
    on:click={onClick}
    on:keydown={(e) => {
        if (e.key === 'Enter') onClick();
    }}
>
    <div class="flex items-center justify-center h-40 bg-gray-50 dark:bg-gray-700/50">
        <CreateKnowledgeButton />
    </div>
</div>
