<script lang="ts">
  import { getContext } from 'svelte';
  import type { Writable } from 'svelte/store';
  import type { I18n } from '$lib/models/i18n';

  const i18n = getContext<Writable<I18n>>('i18n');

  export let value = '';
  export let onChange: (newValue: string) => void = (_) => {};
</script>

<div class="relative w-full md:w-64">
  <input
    type="text"
    placeholder={$i18n.t('Search Knowledge')}...
    bind:value
    on:input={() => onChange(value)}
    class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:outline-none"
  />
  <svg class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400" width="18" height="18" fill="none" viewBox="0 0 24 24">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 3.75a6.75 6.75 0 100 13.5 6.75 6.75 0 000-13.5zM2.25 10.5a8.25 8.25 0 1114.59 5.28l4.69 4.69a.75.75 0 11-1.06 1.06l-4.69-4.69A8.25 8.25 0 012.25 10.5z" fill="currentColor" />
  </svg>
</div>
