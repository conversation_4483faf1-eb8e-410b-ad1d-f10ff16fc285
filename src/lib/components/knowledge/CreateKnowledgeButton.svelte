<script lang="ts">
    import { getContext } from 'svelte';
    import type { Writable } from 'svelte/store';
    import type { I18n } from '$lib/models/i18n';
    import PlusIcon from '$lib/components/icons/PlusIcon.svelte';

    const i18n = getContext<Writable<I18n>>('i18n');

    export let iconSize = "size-7";
    export let iconContainerSize = "size-14";
    export let titleClass = "text-lg";
</script>

<div
    role="button"
    tabindex="0"
    class="flex items-center justify-center cursor-pointer"
>
    <div class="flex flex-col items-center justify-center p-6 text-center">
        <div
            class="{iconContainerSize} bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-3"
        >
            <PlusIcon className="{iconSize} text-blue-500" />
        </div>
        <h3 class="{titleClass} font-medium text-blue-500 dark:text-blue-400">
            {$i18n.t('Create a knowledge base')}
        </h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {$i18n.t('Create a new knowledge base')}
        </p>
    </div>
</div>
