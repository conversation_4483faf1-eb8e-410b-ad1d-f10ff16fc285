<script lang="ts">
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import dayjs from 'dayjs';
	import type { Collection } from '$lib/models/collection';
	import PencilSquare from '$lib/components/icons/PencilSquare.svelte';
	import GarbageBin from '$lib/components/icons/GarbageBin.svelte';
	import FolderOpen from '$lib/components/icons/FolderOpen.svelte';
	import { user } from '$lib/stores';
	import type { Group } from '$lib/models/group';
	import { checkWritePermission } from '$lib/utils/permissions';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<Writable<I18n>>('i18n');

	export let collection: Collection;
	export let onOpen: (id: string) => void = (_) => {};
	// 添加編輯和刪除處理函數
	export let onEdit: (id: string) => void = (_) => {};
	export let onDelete: (id: string) => void = (_) => {};
	// 從外部傳入用戶群組
	export let userGroups: Group[] = [];

	// 處理按鈕點擊，防止事件冒泡到卡片
	const handleButtonClick = (event: MouseEvent, callback: () => void) => {
		event.stopPropagation();
		callback();
	};

	// 檢查用戶是否有寫入權限
	$: hasWritePermission = checkWritePermission($user, collection, userGroups);
</script>

<div
	role="button"
	class="flex flex-col border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-850 hover:shadow-lg dark:hover:bg-gray-800 cursor-pointer transition-all duration-200"
	on:click={() => onOpen(collection.id || '')}
	on:keydown={(e) => {
		if (e.key === 'Enter') onOpen(collection.id || '');
	}}
	tabindex="0"
	aria-label={collection.name}
>
	<div class="flex justify-between items-start mb-2">
		<div class="w-12 h-12 flex items-center justify-center text-gray-700 dark:text-gray-300">
			<FolderOpen className="w-10 h-10" />
		</div>
		<!-- 添加編輯和刪除按鈕，只在有寫入權限時顯示 -->
		{#if hasWritePermission}
			<div class="flex gap-2">
				<button
					class="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
					on:click={(event) => handleButtonClick(event, () => onEdit(collection.id || ''))}
					aria-label={$i18n.t('Edit')}
				>
					<PencilSquare className="w-5 h-5" />
				</button>
				<button
					class="p-1.5 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-500 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
					on:click={(event) => handleButtonClick(event, () => onDelete(collection.id || ''))}
					aria-label={$i18n.t('Delete')}
				>
					<GarbageBin className="w-5 h-5" />
				</button>
			</div>
		{/if}
	</div>
	<div class="flex-1 mt-2">
		<h2 class="text-lg font-semibold mb-2 line-clamp-2">{collection.name}</h2>
		<p class="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
			<span>{dayjs((collection.updated_at || 0) * 1000).format('YYYY年MM月DD日')}</span>
			<span class="mx-1">•</span>
			<span>{$i18n.t('{{COUNT}} sources', { COUNT: collection?.total_files || collection?.files?.length || 0 })}</span>
		</p>
	</div>
</div>
