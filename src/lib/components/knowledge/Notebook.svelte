<script lang="ts">
	import Chat from '$lib/components/chat/Chat.svelte';
	import { onMount, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import type { Writable } from 'svelte/store';
	import type { FileRes } from '$lib/models/file_res';
	import type { Collection } from '$lib/models/collection';
	import { removeFileFromKnowledgeById } from '$lib/apis/knowledge';
	import ConfirmDialog from '$lib/components/common/ConfirmDialog.svelte';
	import { getUserGroups } from '$lib/apis/users';
	import { user } from '$lib/stores';
	import { checkWritePermission as checkPermission } from '$lib/utils/permissions';

	// 引入拆分後的子組件
	import NotebookHeader from './notebook/NotebookHeader.svelte';
	import FileUploader from './notebook/FileUploader.svelte';
	import FileList from './notebook/FileList.svelte';
	import type { I18n } from '$lib/models/i18n';

	import type { Group } from '$lib/models/group';

	const i18n = getContext<Writable<I18n>>('i18n');

	// Props
	export let title: string = '';
	export const content = ''; // For API compatibility
	export const createdAt = new Date(); // For API compatibility
	export const updatedAt = new Date(); // For API compatibility
	export let files: [boolean, FileRes][] = []; // Updated type to NotebookFile[]
	export const isLoading = false; // Changed to const since it's not used
	export let collection: Collection | null = null;

	// 確認對話框變數
	let showDeleteConfirm = false;
	let fileToDelete: FileRes | null = null;

	// 用戶權限相關
	let userGroups: Group[] = [];

	// 使用反應式宣告來檢查用戶是否有寫入權限
	$: hasWritePermission = checkPermission($user, collection as Collection | undefined, userGroups);

	// 處理檔案上傳完成事件
	function handleFileUploaded(event: CustomEvent) {
		const uploadedFile = event.detail;
		files = [...files, [true, uploadedFile]];
	}

	// 處理知識庫更新事件
	function handleCollectionUpdate(event: CustomEvent) {
		collection = event.detail;
	}

	// 處理檔案列表變更事件
	function handleFilesChanged(event: CustomEvent) {
		files = event.detail;
	}

	// 處理刪除檔案事件
	function handleDeleteFile(event: CustomEvent) {
		// 儲存要刪除的檔案資訊，並顯示確認對話框
		fileToDelete = event.detail;
		showDeleteConfirm = true;
	}

	// 確認刪除後執行實際刪除操作
	async function confirmDeleteFile() {
		if (!fileToDelete) return;

		// 確保有 collection ID（必要參數）
		if (!collection || !collection.id) {
			toast.error($i18n.t('Failed to remove file: Unable to find knowledge base ID'));
			return;
		}

		// 顯示處理中的提示
		const toastId = toast.loading($i18n.t('Removing file from knowledge base'));

		try {
			// 從知識庫中移除檔案，傳遞正確的參數：token、知識庫ID、檔案ID
			await removeFileFromKnowledgeById(localStorage.token, collection.id, fileToDelete.id ?? '');

			// 刪除成功後，更新UI
			// 更新集合中的檔案列表（如果需要）
			if (collection && collection.files) {
				collection.files = collection.files.filter((f) => !(f.id === fileToDelete?.id));
			}

			// 從本地檔案陣列中移除檔案
			files = files.filter(([_, file]) => file.id !== fileToDelete?.id);

			// 關閉處理中的提示
			toast.dismiss(toastId);

			// 如果移除成功，提示用戶
			toast.success($i18n.t('Removed file from knowledge base successfully'));
		} catch (error) {
			// 關閉處理中的提示
			toast.dismiss(toastId);

			// 提示錯誤
			toast.error(`${error}`);
			throw error; // 重新拋出錯誤，讓外層 catch 捕獲
		}
	}

	let chatRef: Chat | null = null;
	// 處理新聊天事件
	// 這個函數會在 NotebookHeader 中被調用
	// 當用戶點擊「新聊天」按鈕時，會觸發這個函數
	// 這個函數會調用 Chat 組件的 initNewChat 方法
	// 這個方法會初始化一個新的聊天會話
	function handleNewChat() {
		chatRef?.startNewChat();
	}

	// Initialize component
	onMount(async () => {
		// 取得使用者群組
		try {
			userGroups = await getUserGroups(localStorage.token);
		} catch (error) {
			console.error('無法取得使用者群組:', error);
			userGroups = [];
		}
	});
</script>

<div class="flex w-full h-full">
	<!-- Left sidebar with sources -->
	<div class="w-[32rem] h-full border-r border-gray-200 dark:border-gray-700 flex flex-col">
		<!-- 筆記本標頭 -->
		<NotebookHeader {title} onNewChat={handleNewChat} />

		<!-- 可滾動的內容區域 -->
		<div class="flex-grow overflow-auto h-0">
			<!-- 檔案上傳器，只在有寫入權限時顯示 -->
			{#if hasWritePermission}
				<FileUploader
					{collection}
					on:fileUploaded={handleFileUploaded}
					on:updateCollection={handleCollectionUpdate}
				/>
			{/if}

			<!-- 檔案列表 -->
			<FileList
				{files}
				uploadingFilesCount={0}
				{hasWritePermission}
				on:filesChanged={handleFilesChanged}
				on:deleteFile={handleDeleteFile}
			/>
		</div>
	</div>

	<!-- Main content area -->
	<Chat
		bind:this={chatRef}
		getFilesCallback={async () => {
			const allFilesChecked = files.length > 0 && files.every((file) => file[0]);
			if (allFilesChecked) {
				if (collection) {
					return [
						{
							...collection,
							files: [],
							type: 'collection',
							status: 'processed'
						}
					];
				}
			}
			const checkedFiles = files.filter((file) => file[0]).map((file) => file[1]);
			return checkedFiles;
		}}
	/>
</div>

<!-- 刪除確認對話框 -->
<ConfirmDialog
	bind:show={showDeleteConfirm}
	title={$i18n.t('Confirm delete')}
	message={$i18n.t('Confirm to delete this file? This action cannot be undone.')}
	confirmLabel={$i18n.t('Delete')}
	cancelLabel={$i18n.t('Cancel')}
	on:confirm={() => {
		confirmDeleteFile();
	}}
/>
