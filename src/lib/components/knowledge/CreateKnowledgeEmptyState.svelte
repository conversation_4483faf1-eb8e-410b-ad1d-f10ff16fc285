<script lang="ts">
    import { getContext } from 'svelte';
    import type { Writable } from 'svelte/store';
    import type { I18n } from '$lib/models/i18n';
    import CreateKnowledgeButton from './CreateKnowledgeButton.svelte';

    const i18n = getContext<Writable<I18n>>('i18n');

    export let onClick: () => void;
    export let onClearSearch: (() => void) | null = null;
    export let searchQuery: string = '';
</script>

<div
    class="flex flex-col items-center justify-center py-12 text-gray-600 dark:text-gray-300"
>
    <div
        role="button"
        tabindex="0"
        class="flex flex-col items-center justify-center p-6 text-center border border-dashed border-gray-300 dark:border-gray-600 rounded-xl hover:border-blue-400 dark:hover:border-blue-400 transition-colors cursor-pointer bg-white dark:bg-gray-800 shadow-sm hover:shadow-md max-w-md mx-auto mb-5 min-w-[360px]"
        on:click={onClick}
        on:keydown={(e) => {
            if (e.key === 'Enter') onClick();
        }}
    >
        <CreateKnowledgeButton />
    </div>

    {#if searchQuery && onClearSearch}
        <button
            class="px-3 py-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 underline"
            on:click={onClearSearch}
        >
            {$i18n.t('Clear search history')}
        </button>
    {/if}
</div>
