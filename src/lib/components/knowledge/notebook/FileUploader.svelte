<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { v4 as uuidv4 } from 'uuid';
	import type { Writable } from 'svelte/store';
	import type { FileRes } from '$lib/models/file_res';
	import { uploadFile } from '$lib/apis/files';
	import { addFileToKnowledgeById } from '$lib/apis/knowledge';
	import type { Collection } from '$lib/models/collection';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<Writable<I18n>>('i18n');
	const dispatch = createEventDispatcher();

	// Props
	export let collection: Collection | null = null;

	let inputFiles: FileList | null = null;
	let uploadingFiles = new Map<string, { progress: number; size: number }>();
	let isDragging = false;

	// 處理檔案上傳功能
	const uploadFileHandler = async (file: File) => {
		console.log('Uploading file:', file);

		const tempItemId = uuidv4();
		const fileItem = {
			type: 'file',
			file: '',
			id: null,
			url: '',
			name: file.name,
			size: file.size,
			status: 'uploading',
			error: '',
			itemId: tempItemId
		};

		if (fileItem.size == 0) {
			toast.error($i18n.t('You cannot upload an empty file.'));
			return null;
		}

		// Add file to uploading files map with 0 progress
		uploadingFiles.set(file.name, {
			progress: 0,
			size: file.size
		});
		uploadingFiles = uploadingFiles; // Trigger reactivity

		try {
			// Create a mock progress update (since we don't have actual progress events from the API)
			const progressInterval = setInterval(() => {
				const currentProgress = uploadingFiles.get(file.name)?.progress || 0;
				if (currentProgress < 95) {
					// Cap at 95% until actual completion
					uploadingFiles.set(file.name, {
						progress: currentProgress + Math.random() * 10,
						size: file.size
					});
					uploadingFiles = uploadingFiles; // Trigger reactivity
				}
			}, 300);

			const uploadedFile: FileRes = await uploadFile(localStorage.token, file).catch((e) => {
				clearInterval(progressInterval);
				uploadingFiles.delete(file.name);
				uploadingFiles = uploadingFiles; // Trigger reactivity
				toast.error(`${e}`);
				return null;
			});

			clearInterval(progressInterval);

			if (uploadedFile) {
				// Set progress to 100% when complete
				uploadingFiles.set(file.name, {
					progress: 100,
					size: file.size
				});
				uploadingFiles = uploadingFiles; // Trigger reactivity

				// Remove from uploading files after a short delay to show 100% completion
				setTimeout(() => {
					uploadingFiles.delete(file.name);
					uploadingFiles = uploadingFiles; // Trigger reactivity
				}, 1000);

				uploadedFile.collection = {
					name: collection!.name,
					description: collection!.description
				};
				uploadedFile.name = uploadedFile.filename;
				uploadedFile.description = `${collection!.name} - ${collection!.description}`;
				uploadedFile.type = 'file';
				uploadedFile.status = 'processed';
				console.log('File uploaded:', uploadedFile);

				// 如果有知識庫ID，將檔案加入知識庫
				if (collection && collection.id) {
					try {
						// 顯示添加檔案到知識庫的進度指示
						const addingToast = toast.loading($i18n.t('Adding file to knowledge base...'));

						const updatedKnowledge = await addFileToKnowledgeById(
							localStorage.token,
							collection.id,
							uploadedFile.id!
						).catch((e) => {
							// 關閉進度指示並顯示錯誤
							toast.dismiss(addingToast);
							toast.error(`${e}`);
							return null;
						});

						// 關閉進度指示
						toast.dismiss(addingToast);

						if (updatedKnowledge) {
							// 更新知識庫資訊
							dispatch('updateCollection', updatedKnowledge);
							toast.success($i18n.t('File added to knowledge base successfully.'));

							// 只有成功添加到知識庫後，才通知父組件檔案上傳成功
							dispatch('fileUploaded', uploadedFile);
						} else {
							toast.error($i18n.t('Failed to add file to knowledge base.'));
						}
					} catch (e) {
						toast.error(`${e}`);
					}
				} else {
					// 如果不需要加入知識庫，直接通知父組件檔案上傳成功
					toast.success($i18n.t('File uploaded successfully.'));
					dispatch('fileUploaded', uploadedFile);
				}
			} else {
				uploadingFiles.delete(file.name);
				uploadingFiles = uploadingFiles; // Trigger reactivity
				toast.error($i18n.t('Failed to upload file.'));
			}
		} catch (e) {
			uploadingFiles.delete(file.name);
			uploadingFiles = uploadingFiles; // Trigger reactivity
			toast.error(`${e}`);
		}
	};

	// 處理上傳按鈕點擊
	const handleUploadClick = () => {
		const fileInput = document.getElementById('files-input');
		if (fileInput) {
			fileInput.click();
		}
	};

	// 處理檔案選擇變更
	const handleFileChange = async () => {
		if (inputFiles && inputFiles.length > 0) {
			const files = Array.from(inputFiles);

			// 先將所有檔案加入上傳佇列
			for (const file of files) {
				// 檢查檔案大小
				if (file.size === 0) {
					toast.error($i18n.t('You cannot upload an empty file.'));
					continue;
				}

				// 將檔案加入上傳佇列，初始進度為0
				uploadingFiles.set(file.name, {
					progress: 0,
					size: file.size
				});
			}
			uploadingFiles = uploadingFiles; // 觸發反應性更新

			// 一個一個處理檔案的上傳，而非並行
			for (const file of files) {
				if (file.size > 0) {
					await uploadFileHandler(file);
				}
			}

			inputFiles = null;
			const fileInputElement = document.getElementById('files-input');
			if (fileInputElement instanceof HTMLInputElement) {
				fileInputElement.value = '';
			}
		} else {
			toast.error($i18n.t(`File not found.`));
		}
	};

	// 拖放相關函數
	const handleDragEnter = (event: DragEvent) => {
		event.preventDefault();
		event.stopPropagation();
		isDragging = true;
	};

	const handleDragOver = (event: DragEvent) => {
		event.preventDefault();
		event.stopPropagation();
		if (!isDragging) isDragging = true;
	};

	const handleDragLeave = (event: DragEvent) => {
		event.preventDefault();
		event.stopPropagation();
		isDragging = false;
	};

	const handleDrop = async (event: DragEvent) => {
		event.preventDefault();
		event.stopPropagation();
		isDragging = false;

		if (!event.dataTransfer) return;

		const files = event.dataTransfer.files;
		if (files && files.length > 0) {
			const filesArray = Array.from(files);

			// 先將所有檔案加入上傳佇列
			for (const file of filesArray) {
				// 檢查檔案大小
				if (file.size === 0) {
					toast.error($i18n.t('You cannot upload an empty file.'));
					continue;
				}

				// 將檔案加入上傳佇列，初始進度為0
				uploadingFiles.set(file.name, {
					progress: 0,
					size: file.size
				});
			}
			uploadingFiles = uploadingFiles; // 觸發反應性更新

			// 一個一個處理檔案的上傳，而非並行
			for (const file of filesArray) {
				if (file.size > 0) {
					await uploadFileHandler(file);
				}
			}
		}
	};
</script>

<div
	class="p-4 border-b border-gray-200 dark:border-gray-700"
	on:dragenter={handleDragEnter}
	on:dragover={handleDragOver}
	on:dragleave={handleDragLeave}
	on:drop={handleDrop}
>
	<!-- 拖放提示區域 -->
	{#if isDragging}
		<div
			class="p-6 border-2 border-dashed border-blue-400 dark:border-blue-600 rounded-lg bg-blue-50 dark:bg-blue-900/30 flex flex-col items-center justify-center cursor-pointer"
			on:click={handleUploadClick}
		>
			<div class="text-blue-600 dark:text-blue-400 text-3xl mb-2">+</div>
			<p class="text-blue-600 dark:text-blue-400 font-medium">
				{$i18n.t('Release to upload files')}
			</p>
		</div>
	{:else}
		<div
			class="p-6 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/30 flex flex-col items-center justify-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 dark:hover:border-blue-600 dark:hover:bg-blue-900/30 transition-colors"
			on:click={handleUploadClick}
		>
			<div
				class="text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-3xl mb-2"
			>
				+
			</div>
			<p class="text-gray-500 dark:text-gray-400">
				{$i18n.t('Drop files here to upload')}
			</p>
		</div>
	{/if}
</div>

<!-- 上傳進度顯示 -->
{#if uploadingFiles.size > 0}
	<div class="p-4 mb-3">
		<h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
			{$i18n.t('Uploading Files')}
		</h3>
		{#each [...uploadingFiles.entries()] as [filename, data]}
			<div class="mb-2">
				<div
					class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1"
				>
					<span class="truncate max-w-[180px]">{filename}</span>
					<span>{Math.round(data.progress)}%</span>
				</div>
				<div class="w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
					<div
						class="h-full bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out"
						style="width: {Math.max(5, data.progress)}%"
					></div>
				</div>
			</div>
		{/each}
	</div>
{/if}

<!-- 隱藏的檔案上傳 input -->
<input
	id="files-input"
	bind:files={inputFiles}
	type="file"
	multiple
	hidden
	on:change={handleFileChange}
/>
