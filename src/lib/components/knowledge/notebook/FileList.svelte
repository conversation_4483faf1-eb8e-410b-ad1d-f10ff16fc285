<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { FileRes } from '$lib/models/file_res';
	import FileListItem from './FileListItem.svelte';
	import type { I18n } from '$lib/models/i18n';

	// 設置 indeterminate 屬性的 action
	function setIndeterminate(node: HTMLInputElement, indeterminate: boolean) {
		node.indeterminate = indeterminate;

		return {
			update(newValue: boolean) {
				node.indeterminate = newValue;
			}
		};
	}

	const i18n = getContext<Writable<I18n>>('i18n');
	const dispatch = createEventDispatcher();

	// Props
	export let files: [boolean, FileRes][] = [];
	export let uploadingFilesCount: number = 0;
	export let hasWritePermission: boolean = false;

	// 全選狀態
	$: allSelected = files.length > 0 && files.every(file => file[0]);
	$: someSelected = files.some(file => file[0]);

	// 處理全選/取消全選
	function toggleSelectAll() {
		const newState = !allSelected;
		files = files.map(file => [newState, file[1]]);
		dispatch('filesChanged', files);
	}

	// Handle file selection change
	function handleFileChanged(event: CustomEvent) {
		const { index, selected } = event.detail;
		files[index][0] = selected;
		dispatch('filesChanged', files);
	}

	// Handle file delete request
	function handleDeleteFile(event: CustomEvent) {
		// 只發送刪除檔案的事件，不直接從列表中移除檔案
		// 父組件應在確認刪除後才實際移除檔案
		dispatch('deleteFile', event.detail);
	}
</script>

<div class="p-4">
	{#if files.length > 0}
		<div class="mb-3 flex items-center">
			<input
				type="checkbox"
				id="select-all-files"
				checked={allSelected}
				use:setIndeterminate={someSelected && !allSelected}
				on:change={toggleSelectAll}
				class="flex-shrink-0 mr-5 w-5 h-5 rounded-md border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600 dark:bg-gray-700"
			/>
			<label
				for="select-all-files"
				class="text-sm font-medium text-gray-700 dark:text-gray-300"
			>
				{allSelected ? $i18n.t('取消全選') : $i18n.t('全選')}
			</label>
		</div>
		<div class="">
			{#each files as file, index}
				<FileListItem
					{file}
					{index}
					{hasWritePermission}
					on:fileChanged={handleFileChanged}
					on:deleteFile={handleDeleteFile}
				/>
			{/each}
		</div>
	{:else if uploadingFilesCount === 0}
		<div class="text-sm text-gray-500 dark:text-gray-400 italic">
			{$i18n.t('No files uploaded yet')}
		</div>
	{/if}
</div>
