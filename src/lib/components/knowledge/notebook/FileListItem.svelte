<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { FileRes } from '$lib/models/file_res';
	import GarbageBin from '$lib/components/icons/GarbageBin.svelte';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<Writable<I18n>>('i18n');
	const dispatch = createEventDispatcher();

	// Props
	export let file: [boolean, FileRes];
	export let index: number;
	export let hasWritePermission: boolean = false;

	// Toggle file selection
	function toggleFileSelection() {
		file[0] = !file[0];
		dispatch('fileChanged', { index, selected: file[0] });
	}

	// Handle delete button click with stopPropagation
	function handleDeleteClick(event: MouseEvent) {
		event.stopPropagation();
		dispatch('deleteFile', file[1]);
	}

	// Format file size
	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<div class="flex items-center py-3">
	<input
		type="checkbox"
		id={`file-${file[1].id}`}
		checked={file[0]}
		on:change={toggleFileSelection}
		class="flex-shrink-0 mr-5 w-5 h-5 rounded-md border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600 dark:bg-gray-700"
	/>
	<div class="flex-grow min-w-0">
		<div class="flex items-center justify-between">
			<label
				for={`file-${file[1].id}`}
				class="text-sm font-medium text-gray-700 dark:text-gray-300 line-clamp-2 flex-1"
			>
				{file[1].meta?.name}
			</label>
		</div>
		{#if file[1].meta?.size}
			<div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
				{formatFileSize(file[1].meta.size)}
			</div>
		{/if}
	</div>
	{#if hasWritePermission}
		<button
			class="p-3 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-500 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
			on:click={handleDeleteClick}
			aria-label={$i18n.t('Delete')}
		>
			<GarbageBin />
		</button>
	{/if}
</div>
