<script lang="ts">
	import { goto } from '$app/navigation';
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import PencilSquare from '$lib/components/icons/PencilSquare.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import ChevronLeft from '$lib/components/icons/ChevronLeft.svelte';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<Writable<I18n>>('i18n');

	// Props
	export let title: string = '';
	export let onNewChat: () => void = () => {};

	// Handle back button click
	const handleBack = () => {
		goto('/index/knowledge');
	};
</script>

<div class="px-2 h-16 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
	<div class="flex items-center">
		<button
			on:click={handleBack}
			class="shrink-0 mr-2 p-1.5 hover:bg-black/5 dark:hover:bg-white/5 rounded-lg transition flex items-center gap-1 text-sm"
		>
			<ChevronLeft className="size-4" />
			<span>{$i18n.t('Back')}</span>
		</button>

		{#if title}
			<h2 class="ml-3 text-lg font-medium text-gray-800 dark:text-gray-200 truncate">{title}</h2>
		{/if}
	</div>

	<div class="flex items-center">
		<Tooltip content={$i18n.t('New Chat')}>
			<button
				id="new-chat-button"
				class="cursor-pointer px-2 py-2 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-850 transition"
				on:click={onNewChat}
				aria-label="New Chat"
			>
				<div class="m-auto self-center">
					<PencilSquare className="size-6" strokeWidth="2" />
				</div>
			</button>
		</Tooltip>
	</div>
</div>
