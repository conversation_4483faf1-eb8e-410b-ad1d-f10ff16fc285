<script lang="ts">
  import { Editor, Viewer } from 'bytemd';
  import gfm from '@bytemd/plugin-gfm';
  import highlight from '@bytemd/plugin-highlight';
  import math from '@bytemd/plugin-math';
  import mermaid from '@bytemd/plugin-mermaid';
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  import 'bytemd/dist/index.css';
  import 'highlight.js/styles/vs.css';
  import 'katex/dist/katex.css';

  const dispatch = createEventDispatcher();

  export let value: string = '';
  export let placeholder: string = '';
  export let editable: boolean = true;

  let isDarkMode = false;
  let observer: MutationObserver | null = null;

  const plugins = [
    gfm(),
    highlight(),
    math(),
    mermaid()
  ];

  function handleChange(e: any) {
    const mdContent = e.detail.value;
    dispatch('change', {
      md: mdContent,
      html: mdContent // 原始Markdown内容，可以在父组件中转换为HTML
    });
  }

  // 檢測主題變化
  function checkTheme() {
    isDarkMode = document.documentElement.classList.contains('dark');
  }

  onMount(() => {
    checkTheme();

    // 監聽主題變化
    observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          checkTheme();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
  });

  onDestroy(() => {
    if (observer) {
      observer.disconnect();
    }
  });
</script>

<div class="bytemd-editor-container w-full h-full" class:dark-theme={isDarkMode}>
  {#if editable}
    <Editor
      value={value}
      plugins={plugins}
      placeholder={placeholder}
      mode="split"
      on:change={handleChange}
    />
  {:else}
    <Viewer
      value={value}
      plugins={plugins}
    />
  {/if}
</div>

<style>
  /* ByteMD编辑器样式调整 */
  :global(.bytemd) {
    height: 100%;
    width: 100%;
    background-color: transparent;
    color: inherit;
  }

  :global(.bytemd-toolbar) {
    background-color: transparent;
    border-bottom: 1px solid rgba(125, 125, 125, 0.2);
  }

  :global(.bytemd-editor) {
    background-color: transparent;
    color: inherit;
  }

  :global(.bytemd-preview) {
    background-color: transparent;
    color: inherit;
  }

  /* 編輯器文字區域 */
  :global(.bytemd-editor .CodeMirror) {
    background-color: transparent !important;
    color: inherit !important;
  }

  :global(.bytemd-editor .CodeMirror-gutters) {
    background-color: transparent !important;
    border-right: none !important;
  }

  :global(.bytemd-editor .CodeMirror-cursor) {
    border-left-color: currentColor !important;
  }

  /* 添加Markdown预览样式 */
  :global(.markdown-body) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
    line-height: 1.6;
    padding: 0 1rem;
  }

  :global(.markdown-body h1) {
    font-size: 2em;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.25;
  }

  :global(.markdown-body h2) {
    font-size: 1.5em;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    line-height: 1.25;
  }

  :global(.markdown-body h3) {
    font-size: 1.25em;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    line-height: 1.25;
  }

  :global(.markdown-body p) {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  :global(.markdown-body ul, .markdown-body ol) {
    margin-top: 0;
    margin-bottom: 1rem;
    padding-left: 2rem;
  }

  :global(.markdown-body li) {
    margin-bottom: 0.25rem;
  }

  :global(.markdown-body code) {
    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
  }

  :global(.dark .markdown-body code) {
    background-color: rgba(240, 246, 252, 0.15);
  }

  :global(.markdown-body pre) {
    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 3px;
    margin-top: 0;
    margin-bottom: 1rem;
  }

  :global(.dark .markdown-body pre) {
    background-color: #1e1e1e;
  }

  :global(.markdown-body pre code) {
    padding: 0;
    margin: 0;
    font-size: 100%;
    background-color: transparent;
  }

  :global(.markdown-body blockquote) {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
    margin: 0 0 1rem 0;
  }

  :global(.dark .markdown-body blockquote) {
    color: #959da5;
    border-left-color: #2f363d;
  }

  :global(.markdown-body table) {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
    overflow: auto;
  }

  :global(.markdown-body table th, .markdown-body table td) {
    padding: 6px 13px;
    border: 1px solid #dfe2e5;
  }

  :global(.dark .markdown-body table th, .dark .markdown-body table td) {
    border-color: #2f363d;
  }

  :global(.markdown-body table tr) {
    background-color: #fff;
    border-top: 1px solid #c6cbd1;
  }

  :global(.dark .markdown-body table tr) {
    background-color: transparent;
    border-top-color: #2f363d;
  }

  :global(.markdown-body table tr:nth-child(2n)) {
    background-color: #f6f8fa;
  }

  :global(.dark .markdown-body table tr:nth-child(2n)) {
    background-color: rgba(255, 255, 255, 0.05);
  }

  :global(.markdown-body img) {
    max-width: 100%;
    box-sizing: content-box;
  }

  :global(.markdown-body hr) {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: #e1e4e8;
    border: 0;
  }

  :global(.dark .markdown-body hr) {
    background-color: #2f363d;
  }

  /* 深色主題樣式 */
  :global(.dark .bytemd-toolbar) {
    border-color: rgba(125, 125, 125, 0.2);
    background-color: transparent;
  }

  :global(.dark .bytemd-toolbar-icon) {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  :global(.dark .bytemd-toolbar-icon:hover) {
    color: rgba(255, 255, 255, 1) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  :global(.dark .bytemd-preview) {
    color: rgba(255, 255, 255, 0.8);
  }

  :global(.dark .bytemd-editor .CodeMirror) {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  :global(.dark .bytemd-editor .CodeMirror-selected) {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  :global(.dark .bytemd-editor .CodeMirror-line::selection) {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  :global(.dark .bytemd-editor .CodeMirror-focused .CodeMirror-selected) {
    background-color: rgba(255, 255, 255, 0.15) !important;
  }

  .bytemd-editor-container {
    height: 100%;
    overflow: hidden;
  }
</style>
