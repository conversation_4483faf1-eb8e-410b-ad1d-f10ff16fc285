<script lang="ts">
	import { getContext, onMount } from 'svelte';
	import type { Writable } from 'svelte/store';

	const i18n = getContext<Writable<I18n>>('i18n');

	import { getGroups } from '$lib/apis/groups';
	import { getAllUsersForVerifiedUser } from '$lib/apis/users';
	import Badge from '$lib/components/common/Badge.svelte';
	import XMark from '$lib/components/icons/XMark.svelte';
	import Search from '$lib/components/icons/Search.svelte';
	import ChevronDown from '$lib/components/icons/ChevronDown.svelte';
	import type { Group } from '$lib/models/group';
	import type { AccessControl } from '$lib/models/access_control';
	import type { I18n } from '$lib/models/i18n';
	import type { User } from '$lib/models/user';
	import { user as currentUser } from '$lib/stores';

	export let accessControl: AccessControl | null = null;

	// Initialize accessControl if it's null
	$: if (accessControl === null) {
		accessControl = {
			read: {
				group_ids: [],
				user_ids: []
			},
			write: {
				group_ids: [],
				user_ids: []
			}
		};
	}

	let groups: Group[] = [];
	let selectedGroupId = '';
	let isDropdownOpen = false;

	// 用户相关变量
	let userId = '';
	let users: User[] = [];
	let filteredUsers: User[] = [];
	let isUserDropdownOpen = false;
	// 存储用户ID和电子邮件的映射
	let userEmailMap: Record<string, string> = {};

	onMount(async () => {
		try {
			// 获取群组
			groups = await getGroups(localStorage.token);

			// 获取所有用户
			const response = await getAllUsersForVerifiedUser(localStorage.token);
			if (response && response.users) {
				// 过滤掉当前登录用户
				users = response.users.filter((user: User) => user.id !== $currentUser?.id);

				// 初始化用户ID和电子邮件的映射
				users.forEach((user: User) => {
					if (user.id && user.email) {
						userEmailMap[user.id] = user.email;
					}
				});
			}
		} catch (error) {
			console.error('Failed to load data:', error);
		}
	});

	const onSelectGroup = () => {
		if (selectedGroupId !== '') {
			// Add to read permissions by default
			if (accessControl && !accessControl.read.group_ids.includes(selectedGroupId)) {
				accessControl.read.group_ids = [...accessControl.read.group_ids, selectedGroupId];
			}
			selectedGroupId = '';
			isDropdownOpen = false;
		}
	};

	const onAddUser = () => {
		if (userId.trim() !== '') {
			// 检查用户ID是否已存在
			if (accessControl && !accessControl.read.user_ids.includes(userId.trim())) {
				accessControl.read.user_ids = [...accessControl.read.user_ids, userId.trim()];
			}
			userId = '';
		}
	};

	$: if (selectedGroupId) {
		onSelectGroup();
	}

	// 当用户输入时，过滤用户列表
	$: {
		if (userId.trim() !== '') {
			filteredUsers = users.filter(user => {
				const nameMatch = user.name ? user.name.toLowerCase().includes(userId.toLowerCase()) : false;
				const idMatch = user.id ? user.id.toLowerCase().includes(userId.toLowerCase()) : false;
				return nameMatch || idMatch;
			});
			// 如果有匹配结果且下拉菜单未打开，则打开下拉菜单
			if (filteredUsers.length > 0 && !isUserDropdownOpen) {
				isUserDropdownOpen = true;
			}
		} else {
			filteredUsers = [];
			isUserDropdownOpen = false;
		}
	}

	const toggleDropdown = () => {
		isDropdownOpen = !isDropdownOpen;
	};

	const handleKeyDown = (event: KeyboardEvent) => {
		if (event.key === 'Enter' || event.key === ' ') {
			toggleDropdown();
			event.preventDefault();
		} else if (event.key === 'Escape' && isDropdownOpen) {
			isDropdownOpen = false;
			event.preventDefault();
		}
	};

	const handleItemKeyDown = (event: KeyboardEvent, groupId: string) => {
		if (event.key === 'Enter' || event.key === ' ') {
			selectedGroupId = groupId;
			onSelectGroup();
			event.preventDefault();
		}
	};

	const handleUserKeyDown = (event: KeyboardEvent) => {
		if (event.key === 'Enter' && userId.trim() !== '') {
			onAddUser();
			event.preventDefault();
		} else if (event.key === 'Escape' && isUserDropdownOpen) {
			isUserDropdownOpen = false;
			event.preventDefault();
		} else if (event.key === 'ArrowDown' && isUserDropdownOpen && filteredUsers.length > 0) {
			// 可以在这里添加键盘导航功能
			event.preventDefault();
		}
	};

	// 选择用户
	const selectUser = (user: User) => {
		if (user && user.id) {
			userId = user.id;
			onAddUser();
			isUserDropdownOpen = false;
		}
	};


</script>

<div class="mt-4">
	<div class="text-sm font-semibold mb-2 dark:text-white">{$i18n.t('Group permissions')}</div>

	<div class="mb-2 relative">
		<!-- Custom dropdown trigger -->
		<button
			type="button"
			class="flex items-center justify-between w-full px-4 py-2.5 text-sm rounded-lg cursor-pointer
      border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-750
      transition-colors duration-150 ease-in-out"
			on:click={toggleDropdown}
			on:keydown={handleKeyDown}
			aria-haspopup="listbox"
			aria-expanded={isDropdownOpen}
		>
			<div class="flex items-center gap-2">
				<Search className="size-4 text-gray-500 dark:text-gray-400" strokeWidth="2" />
				<span class={selectedGroupId ? 'dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
					{$i18n.t('Select a group')}
				</span>
			</div>
			<ChevronDown
				className="size-4 text-gray-500 dark:text-gray-400 transition-transform duration-200 ease-in-out
        {isDropdownOpen ? 'rotate-180' : ''}"
				strokeWidth="2"
			/>
		</button>

		<!-- Dropdown menu -->
		{#if isDropdownOpen}
			<div
				class="absolute z-10 w-full mt-1 py-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg
        border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto"
				role="listbox"
			>
				{#each groups.filter((group) => !accessControl?.read.group_ids.includes(group.id)) as group}
					<button
						type="button"
						class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-700 dark:text-gray-300"
						on:click={() => {
							selectedGroupId = group.id;
							onSelectGroup();
						}}
						on:keydown={(e) => handleItemKeyDown(e, group.id)}
						role="option"
						aria-selected={false}
					>
						{group.name}
					</button>
				{:else}
					<div class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
						{$i18n.t('There are no selectable groups')}
					</div>
				{/each}
			</div>
		{/if}

		<!-- Hidden select for binding value -->
		<select class="hidden" bind:value={selectedGroupId}>
			<option value="" disabled></option>
			{#each groups.filter((group) => !accessControl?.read.group_ids.includes(group.id)) as group}
				<option value={group.id}>{group.name}</option>
			{/each}
		</select>
	</div>

	<div class="mt-2">
		{#if accessControl && accessControl.read.group_ids.length > 0}
			<div class="text-xs text-gray-500 dark:text-gray-400 mb-1">{$i18n.t('Selected groups')}</div>
			<div class="flex flex-col gap-2">
				{#each accessControl.read.group_ids as groupId}
					{@const group = groups.find((g) => g.id === groupId)}
					{#if group}
						<div
							class="flex items-center justify-between bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2.5 border border-gray-200/50 dark:border-gray-700/50 shadow-sm"
						>
							<div class="text-sm font-medium dark:text-white">{group.name}</div>
							<div class="flex items-center gap-2">
								<button
									type="button"
									class="transition"
									on:click={() => {
										if (accessControl) {
											if (accessControl.write.group_ids.includes(groupId)) {
												accessControl.write.group_ids = accessControl.write.group_ids.filter(
													(id) => id !== groupId
												);
											} else {
												accessControl.write.group_ids = [...accessControl.write.group_ids, groupId];
											}
										}
									}}
								>
									{#if accessControl?.write.group_ids.includes(groupId)}
										<Badge type={'success'} content={$i18n.t('Read & Write')} />
									{:else}
										<Badge type={'info'} content={$i18n.t('Read only')} />
									{/if}
								</button>

								<button
									class="rounded-full p-1.5 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-150 ease-in-out"
									type="button"
									on:click={() => {
										if (accessControl) {
											accessControl.read.group_ids = accessControl.read.group_ids.filter(
												(id) => id !== groupId
											);
											accessControl.write.group_ids = accessControl.write.group_ids.filter(
												(id) => id !== groupId
											);
										}
									}}
								>
									<XMark className="size-4 dark:text-white" />
								</button>
							</div>
						</div>
					{/if}
				{/each}
			</div>
		{:else}
			<div
				class="text-xs text-gray-500 dark:text-gray-400 text-center py-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200/50 dark:border-gray-700/50"
			>
				{$i18n.t("Haven't selected any groups, please select from the menu above")}
			</div>
		{/if}
	</div>
</div>

<!-- 用户权限部分 -->
<div class="mt-6">
	<div class="text-sm font-semibold mb-2 dark:text-white">{$i18n.t('User permissions')}</div>
	<div class="text-xs text-gray-500 dark:text-gray-400 mb-2">{$i18n.t('Enter user ID to grant access')}</div>

	<div class="mb-2 relative">
		<div class="flex items-center gap-2">
			<div class="relative w-full">
				<input
					type="text"
					bind:value={userId}
					placeholder={$i18n.t('Enter user ID')}
					class="w-full px-4 py-2.5 text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors duration-150 ease-in-out"
					on:keydown={handleUserKeyDown}
					on:blur={() => {
						// 延迟关闭下拉菜单，以便点击下拉菜单项时能够触发点击事件
						setTimeout(() => {
							isUserDropdownOpen = false;
						}, 200);
					}}
				/>

				<!-- 用户下拉菜单 -->
				{#if isUserDropdownOpen && filteredUsers.length > 0}
					<div
						class="absolute z-10 w-full mt-1 py-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg
						border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto"
						role="listbox"
					>
						{#each filteredUsers as user}
							<button
								type="button"
								class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-700 dark:text-gray-300"
								on:click={() => selectUser(user)}
								role="option"
								aria-selected="false"
							>
								<div class="flex items-center">
									<div class="flex-shrink-0 mr-2">
										{#if user.profile_image_url}
											<img src={user.profile_image_url} alt="" class="size-6 rounded-full" />
										{:else}
											<div class="size-6 rounded-full bg-gray-300 dark:bg-gray-600"></div>
										{/if}
									</div>
									<div>
										<div class="font-medium">{user.name || user.id}</div>
										{#if user.email}
											<div class="text-xs text-gray-500">{user.email}</div>
										{/if}
									</div>
								</div>
							</button>
						{/each}
					</div>
				{/if}
			</div>
		</div>
	</div>

	<div class="mt-2">
		{#if accessControl && accessControl.read.user_ids.length > 0}
			<div class="text-xs text-gray-500 dark:text-gray-400 mb-1">{$i18n.t('Selected users')}</div>
			<div class="flex flex-col gap-2">
				{#each accessControl.read.user_ids as userId}
					<div
						class="flex items-center justify-between bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2.5 border border-gray-200/50 dark:border-gray-700/50 shadow-sm"
					>
						<div class="text-sm font-medium dark:text-white">{userEmailMap[userId] || userId}</div>
						<div class="flex items-center gap-2">
							<button
								type="button"
								class="transition"
								on:click={() => {
									if (accessControl) {
										if (accessControl.write.user_ids.includes(userId)) {
											accessControl.write.user_ids = accessControl.write.user_ids.filter(
												(id) => id !== userId
											);
										} else {
											accessControl.write.user_ids = [...accessControl.write.user_ids, userId];
										}
									}
								}}
							>
								{#if accessControl?.write.user_ids.includes(userId)}
									<Badge type={'success'} content={$i18n.t('Read & Write')} />
								{:else}
									<Badge type={'info'} content={$i18n.t('Read only')} />
								{/if}
							</button>

							<button
								class="rounded-full p-1.5 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-150 ease-in-out"
								type="button"
								on:click={() => {
									if (accessControl) {
										accessControl.read.user_ids = accessControl.read.user_ids.filter(
											(id) => id !== userId
										);
										accessControl.write.user_ids = accessControl.write.user_ids.filter(
											(id) => id !== userId
										);
									}
								}}
							>
								<XMark className="size-4 dark:text-white" />
							</button>
						</div>
					</div>
				{/each}
			</div>
		{:else}
			<div
				class="text-xs text-gray-500 dark:text-gray-400 text-center py-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200/50 dark:border-gray-700/50"
			>
				{$i18n.t("Haven't added any users, please enter user ID above")}
			</div>
		{/if}
	</div>
</div>
