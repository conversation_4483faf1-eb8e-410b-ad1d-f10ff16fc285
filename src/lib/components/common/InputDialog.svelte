<script lang="ts">
  import { onMount, getContext, createEventDispatcher } from 'svelte';
  import type { Writable } from 'svelte/store';
  import { toast } from 'svelte-sonner';
  import type { I18n } from '$lib/models/i18n';

  const i18n = getContext<Writable<I18n>>('i18n');
  const dispatch = createEventDispatcher();

  import { fade } from 'svelte/transition';
  import { flyAndScale } from '$lib/utils/transitions';
  import GroupPermissions from './GroupPermissions.svelte';
  import { getKnowledgeById } from '$lib/apis/knowledge';
	import type { Collection } from '$lib/models/collection';

  export let title = '';
  export let message = '';
  export let placeholder = '';
  export let cancelLabel = $i18n.t('Cancel');
  export let confirmLabel = $i18n.t('Confirm');
  export let inputValue = '';
  export let show = false;
  export let showPermissions = false;
  export let accessControl: any = null;
  export let collectionId: string | null = null;

  let modalElement: HTMLElement | null = null;
  let mounted = false;
  let isLoading = false;
  let knowledge: Collection | null = null; // Store the fetched knowledge base data

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      cancelHandler();
    }

    if (event.key === 'Enter' && !showPermissions) {
      confirmHandler();
    }
  };

  const confirmHandler = () => {
    show = false;
    // Pass the knowledge object with updated name and access control
    if (knowledge) {
      knowledge.name = inputValue;
      knowledge.access_control = accessControl;
    }
    dispatch('confirm', { value: inputValue, accessControl, knowledge });
  };

  const cancelHandler = () => {
    console.log('Cancel handler called with access control:', accessControl);
    show = false;
    // Reset accessControl when canceling if showPermissions is true
    if (showPermissions) {
      console.log('Resetting accessControl');
      accessControl = null;
    }
    // Reset knowledgeData when canceling
    knowledge = null;
    dispatch('cancel');
  };

  // Fetch knowledge base details when collectionId is provided
  const fetchKnowledge = async () => {
    console.log('Fetching knowledge base details for collectionId:', collectionId);
    if (!collectionId) return;
    if (isLoading) return; // Prevent multiple fetches
    isLoading = true;
    try {
      // Store the complete knowledge object
      knowledge = await getKnowledgeById(localStorage.token, collectionId);

      // Set the input value and access control from the knowledge object
      inputValue = knowledge?.name || '';
      accessControl = knowledge?.access_control || {
        read: {
          group_ids: [],
          user_ids: []
        },
        write: {
          group_ids: [],
          user_ids: []
        }
      };
    } catch (e) {
      toast.error($i18n.t('Fetch knowledge base details failed') + `: ${e}`);
    } finally {
      isLoading = false;
    }
  };

  onMount(() => {
    mounted = true;
    console.log('InputDialog mounted with access control:', accessControl);
    // Initialize access control if needed
    if (showPermissions && !accessControl) {
      accessControl = {
        read: {
          group_ids: [],
          user_ids: []
        },
        write: {
          group_ids: [],
          user_ids: []
        }
      };
    }
  });

  $: if (mounted) {
    console.log('InputDialog updated with access control:', accessControl);
    if (show && modalElement) {
      document.body.appendChild(modalElement);
      window.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';

      // 只有在對話框首次顯示且有 collectionId 時才獲取知識庫信息
      if (collectionId && !knowledge) {
        fetchKnowledge();
      }
    } else if (modalElement) {
      window.removeEventListener('keydown', handleKeyDown);
      if (document.body.contains(modalElement)) {
        document.body.removeChild(modalElement);
      }
      document.body.style.overflow = 'unset';
    }
  }
</script>

{#if show}
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div
    bind:this={modalElement}
    class="fixed top-0 right-0 left-0 bottom-0 bg-black/60 w-full h-screen max-h-[100dvh] flex justify-center z-99999999 overflow-hidden overscroll-contain"
    in:fade={{ duration: 10 }}
    on:mousedown={cancelHandler}
  >
    <div
      class="m-auto rounded-2xl max-w-full w-[32rem] mx-2 bg-white dark:bg-gray-900 max-h-[100dvh] shadow-3xl"
      in:flyAndScale
      on:mousedown={(e) => {
        e.stopPropagation();
      }}
    >
      <div class="px-[1.75rem] py-6 flex flex-col">
        <div class="text-lg font-semibold dark:text-white mb-2.5">
          {title}
        </div>

        <div class="text-sm text-gray-600 dark:text-gray-300 flex-1 mb-4">
          {message}
        </div>

        {#if isLoading}
          <div class="w-full mb-4 flex items-center justify-center py-2">
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">{$i18n.t('Loading...')}</span>
          </div>
        {:else}
          <input
            bind:value={inputValue}
            placeholder={placeholder}
            class="w-full mb-4 rounded-lg px-4 py-2 text-sm bg-gray-50 dark:bg-gray-800 dark:text-white border border-gray-200 dark:border-gray-700 outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
          />
        {/if}

        {#if showPermissions}
          <GroupPermissions bind:accessControl />
        {/if}

        <div class="flex justify-between gap-1.5 mt-4">
          <button
            class="bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white font-medium w-full py-2.5 rounded-lg transition"
            on:click={cancelHandler}
            type="button"
          >
            {cancelLabel}
          </button>
          <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-medium w-full py-2.5 rounded-lg transition"
            on:click={confirmHandler}
            type="button"
            disabled={!inputValue.trim()}
          >
            {confirmLabel}
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  @keyframes scaleUp {
    from {
      transform: scale(0.985);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
</style>