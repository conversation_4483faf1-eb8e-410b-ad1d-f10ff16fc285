<script lang="ts">
	import { getContext } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { showSidebar, user, showArchivedChats } from '$lib/stores';
	import MenuLines from '$lib/components/icons/MenuLines.svelte';
	import ArrowLeft from '$lib/components/icons/ArrowLeft.svelte';
	import UserMenu from '$lib/components/layout/Sidebar/UserMenuLite.svelte';
	import type { I18n } from '$lib/models/i18n';
	import type { Writable } from 'svelte/store';
	import ThemeToggle from '../common/ThemeToggle.svelte';
	import Cog6Solid from '../icons/Cog6Solid.svelte';

	const i18n = getContext<Writable<I18n>>('i18n');

	// 导航菜单项
	const menuItems = [
		{ label: 'Knowledge base', href: '/index/knowledge' },
		{ label: 'Notes', href: '/index/notes' },
	];

	// 当前活动页面 - 基于路径名而不是URL参数
	$: activePage = $page.url.pathname;
</script>

<nav class="px-4 sm:px-6 lg:px-8 backdrop-blur-xl drag-region border-b border-gray-200 dark:border-gray-700">
	<div class="flex items-center justify-between h-16">
		<div class="flex items-center gap-2">
			<h1 class="text-2xl font-bold text-gray-800 dark:text-gray-100">
				{$user?.name}, {$i18n.t('Welcome to')} Bubble
			</h1>

			<!-- 导航菜单 -->
			<div class="flex items-center space-x-2 ml-2">
				{#each menuItems as item}
					<a
						href={item.href}
						class="px-3 py-1.5 rounded-lg text-sm font-medium transition {activePage === item.href || activePage.startsWith(item.href + '/')
							? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
							: 'hover:bg-gray-50 dark:hover:bg-gray-850 text-gray-600 dark:text-gray-400'}"
					>
						{$i18n.t(item.label)}
					</a>
				{/each}
			</div>
		</div>

		<!-- 用户菜单 -->
		<div class="flex items-center gap-2">
			<!-- Theme Toggle Button -->
			<ThemeToggle />

			<!-- 設定按鈕 - 只對管理員顯示 -->
			{#if $user?.role === 'admin'}
				<button
					class="flex items-center gap-1 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
					aria-label={$i18n.t('Settings')}
					on:click={() => goto('/backend')}
				>
					<Cog6Solid className="size-5" />
				</button>
			{/if}

			{#if $user !== undefined && $user !== null}
				<UserMenu
					className="max-w-[200px]"
					role={$user?.role}
					on:show={(e) => {
						if (e.detail === 'archived-chat') {
							showArchivedChats.set(true);
						}
					}}
				>
					<button
						class="select-none flex rounded-xl p-1.5 w-full hover:bg-gray-50 dark:hover:bg-gray-850 transition"
						aria-label="User Menu"
					>
						<div class="self-center">
							<img
								src={$user?.profile_image_url}
								class="size-8 object-cover rounded-full"
								alt="User profile"
								draggable="false"
							/>
						</div>
					</button>
				</UserMenu>
			{/if}
		</div>
	</div>
</nav>
