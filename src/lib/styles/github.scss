.carta-theme__github {
	// Core styles
	$background: #0d1117;
	$background-light: #161b22;
	$border: #2b3138;
	$accent: #1f6feb;

	&.carta-editor {
		background-color: $background;
		border: 1px solid $border;
		border-radius: 0.5rem;

		&:focus-within {
			outline: 2px solid $accent;
		}

		.carta-wrapper {
			padding: 1rem;
			flex-grow: 1;
		}

		.carta-input,
		.carta-renderer {
			min-height: 120px;
			max-height: 160px;
			overflow: auto;
		}

		.carta-font-code {
			font-family: 'Fira Code', monospace;
			caret-color: white;
			font-size: 1.1rem;
		}

		.carta-toolbar {
			height: 2.5rem;

			background-color: $background-light;

			border-top-left-radius: 0.5rem;
			border-top-right-radius: 0.5rem;

			.carta-icon {
				width: 2rem;
				height: 2rem;

				&:hover {
					color: white;
					background-color: $border;
				}
			}
		}

		.carta-toolbar-left button,
		.carta-toolbar-right,
		.carta-filler {
			border-bottom: 1px solid $border;
		}

		.carta-toolbar-left {
			& > *:first-child {
				border-top-left-radius: 0.5rem;
			}

			& > * {
				padding-left: 1rem;
				padding-right: 1rem;
				font-size: 0.95rem;
			}

			button {
				height: 100%;
			}

			.carta-active {
				background-color: $background;
				color: white;

				border-right: 1px solid $border;
				border-bottom: 1px solid $background;

				&:not(:first-child) {
					border-left: 1px solid $border;
				}
			}
		}

		.carta-toolbar-right {
			padding-right: 12px;
		}

		.carta-icons-menu {
			padding: 8px;
			border: 1px solid $border;
			border-radius: 6px;
			min-width: 180px;
			background: $background;

			.carta-icon-full {
				padding-left: 6px;
				padding-right: 6px;

				margin-top: 2px;
				&:first-child {
					margin-top: 0;
				}

				&:hover {
					color: white;
					background-color: $border;
				}

				span {
					margin-left: 6px;
					color: white;
					font-size: 0.85rem;
				}
			}
		}
	}

	// Plugin emoji
	&.carta-emoji {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;

		width: 19rem;
		max-height: 14rem;
		overflow-x: auto;
		overflow-y: auto;
		border-radius: 4px;
		font-family: inherit;
		background-color: $background;
		word-break: break-word;
		scroll-padding: 6px;
	}

	&.carta-emoji button {
		background: $background-light;

		cursor: pointer;
		display: inline-block;
		border-radius: 4px;
		border: 0;
		padding: 0;
		margin: 0.175rem;

		min-width: 2rem;
		height: 2rem;
		font-size: 1.2rem;
		line-height: 100%;
		text-align: center;
		white-space: nowrap;
	}

	&.carta-emoji button:hover,
	&.carta-emoji button.carta-active {
		background: $border;
	}

	// Plugin slash
	&.carta-slash {
		width: 18rem;
		max-height: 14rem;
		overflow-y: scroll;
		border-radius: 4px;
		font-family: inherit;
		background-color: $background;
		padding: 6px;
		scroll-padding: 6px;
	}

	&.carta-slash span {
		width: fit-content;
	}

	&.carta-slash button {
		background: none;
		width: 100%;
		padding: 10px;
		border: 0;
		border-radius: 4px;
	}

	&.carta-slash .carta-slash-group {
		padding: 0 4px 0 4px;
		margin-bottom: 4px;
		font-size: 0.8rem;
	}

	&.carta-slash button.carta-active,
	&.carta-slash button:hover {
		background: $background-light;
		cursor: pointer;
	}

	&.carta-slash .carta-snippet-title {
		font-size: 0.85rem;
		font-weight: 600;
	}

	&.carta-slash .carta-snippet-description {
		font-size: 0.8rem;
		text-overflow: ellipsis;
	}
}

html.dark .carta-theme__github .shiki,
html.dark .carta-theme__github .shiki span {
	color: var(--shiki-dark) !important;
}
