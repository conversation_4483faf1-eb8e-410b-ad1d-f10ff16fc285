{"-1 for no limit, or a positive integer for a specific limit": "-1 för ingen grä<PERSON>, eller ett positivt heltal för en specifik gräns", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' eller '-1' för inget utg<PERSON>datum", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(t.ex. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(t.ex. `sh webui.sh --api`)", "(latest)": "(senaste)", "(leave blank for to use commercial endpoint)": "(lämna tomt för att använda kommersiell endpoint)", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} Tillgängliga verktyg", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} dolda rader", "{{COUNT}} Replies": "{{COUNT}} <PERSON>var", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}}s <PERSON><PERSON><PERSON>", "{{webUIName}} Backend Required": "{{webUIName}} Backend krävs", "*Prompt node ID(s) are required for image generation": "*Prompt node ID(s) krävs för bildgenerering", "A new version (v{{LATEST_VERSION}}) is now available.": "En ny version (v{{LATEST_VERSION}}) är nu tillgänglig.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "En uppgiftsmodell används när du utför uppgifter som att generera titlar för chattar och webbsökningsfrågor", "a user": "en användare", "About": "Om", "Accept autocomplete generation / Jump to prompt variable": "Acceptera automatisk komplettering / Hoppa till promptvariabel", "Access": "Åtkomst", "Access Control": "Åtkomstkontroll", "Accessible to all users": "Tillgänglig för alla användare", "Account": "Ko<PERSON>", "Account Activation Pending": "Kontoaktivering väntar", "Accurate information": "Exakt information", "Action": "", "Actions": "Åtgärder", "Activate": "Aktivera", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivera detta kommando genom att skriva \"/{{COMMAND}}\" i chattrutan.", "Active Users": "Aktiva användare", "Add": "<PERSON><PERSON><PERSON> till", "Add a model ID": "Lägg till ett modell-ID", "Add a short description about what this model does": "Lägg till en kort beskrivning om vad den här modellen gör", "Add a tag": "Lägg till en tagg", "Add Arena Model": "Lägg till Arenamodel", "Add Connection": "Lägg till anslutning", "Add Content": "Lägg till innehåll", "Add content here": "Lägg till innehåll här", "Add Custom Parameter": "Lägg till anpassad parameter", "Add custom prompt": "Lägg till en anpassad instruktion", "Add Files": "Lägg till filer", "Add Group": "Lägg till grupp", "Add Memory": "Lägg till minne", "Add Model": "Lägg till modell", "Add Reaction": "Lägg till reaktion", "Add Tag": "Lägg till tagg", "Add Tags": "Lägg till taggar", "Add text content": "Lägg till textinnehåll", "Add User": "Lägg till användare", "Add User Group": "Lägg till användargrupp", "Adjusting these settings will apply changes universally to all users.": "Justering av dessa inställningar kommer att tillämpa ändringar universellt för alla användare.", "admin": "administratör", "Admin": "Admin", "Admin Panel": "Administrationspanel", "Admin Settings": "Administratörsinställningar", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratörer har tillgång till alla verktyg hela tiden, medan användare behöver verktyg som tilldelas per modell i arbetsytan.", "Advanced Parameters": "Avancerade parametrar", "Advanced Params": "Avancerade parametrar", "AI": "", "All": "<PERSON>a", "All Documents": "Alla dokument", "All models deleted successfully": "Alla modeller har raderats framgångsrikt", "Allow Call": "<PERSON><PERSON><PERSON> sa<PERSON>", "Allow Chat Controls": "<PERSON><PERSON><PERSON>", "Allow Chat Delete": "<PERSON><PERSON><PERSON> radering av chatt", "Allow Chat Deletion": "<PERSON><PERSON><PERSON> chatt<PERSON>", "Allow Chat Edit": "<PERSON><PERSON><PERSON> redigering av chatt", "Allow Chat Export": "Tillåt export av chatt", "Allow Chat Share": "<PERSON><PERSON><PERSON> del<PERSON> av chatt", "Allow Chat System Prompt": "<PERSON><PERSON>t systemprompt i chatt", "Allow File Upload": "<PERSON><PERSON><PERSON>", "Allow Multiple Models in Chat": "<PERSON><PERSON><PERSON> flera modeller i chatt", "Allow non-local voices": "<PERSON><PERSON><PERSON> i<PERSON>-lo<PERSON>a rö<PERSON>", "Allow Speech to Text": "<PERSON><PERSON><PERSON> tal till text", "Allow Temporary Chat": "<PERSON><PERSON><PERSON> till<PERSON><PERSON><PERSON><PERSON> chatt", "Allow Text to Speech": "<PERSON><PERSON>t text till tal", "Allow User Location": "<PERSON><PERSON><PERSON> an<PERSON>", "Allow Voice Interruption in Call": "<PERSON><PERSON><PERSON> röstavbrott under sam<PERSON>", "Allowed Endpoints": "Tillåtna Endpoints", "Allowed File Extensions": "<PERSON><PERSON><PERSON><PERSON>", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Tillåtna filändelser för uppladdning. Separera flera filändelser med kommatecken. Lämna tomt för alla filtyper.", "Already have an account?": "Har du redan ett konto?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternativ till top_p, och syftar till att säkerställa en balans mellan kvalitet och variation. Parametern p representerar den minsta sannolikheten för att en token ska beaktas, relativt sannolikheten för den mest sannolika token. Till exempel, med p=0.05 och den mest sannolika token som har en sannolikhet på 0.9, filtreras logits med ett värde mindre än 0.045 bort.", "Always": "Alltid", "Always Collapse Code Blocks": "<PERSON><PERSON><PERSON> alltid ihop kodblock", "Always Expand Details": "Visa alltid detaljer", "Always Play Notification Sound": "<PERSON><PERSON><PERSON> alltid aviseringsljud", "Amazing": "Fantastiskt", "an assistant": "en assistent", "Analyzed": "Analyserad", "Analyzing...": "Analyserar...", "and": "och", "and {{COUNT}} more": "och {{COUNT}} fler", "and create a new shared link.": "och skapa en ny delad länk.", "Android": "Android", "API": "API", "API Base URL": "API-bas-URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "API-detaljer för att använda en syn-språkmodell i bildbeskrivningen. Denna parameter utesluter ömsesidigt picture_description_local.", "API Key": "API-nyckel", "API Key created.": "API-nyckel skapad.", "API Key Endpoint Restrictions": "API-nyckel Endpoint-begränsningar", "API keys": "API-nycklar", "API Version": "API-version", "Application DN": "Applikations-DN", "Application DN Password": "Applikations-DN lösenord", "applies to all users with the \"user\" role": "gäller alla användare med \"user\"-rollen", "April": "april", "Archive": "Arkiv", "Archive All Chats": "Arkivera alla chattar", "Archived Chats": "Arkiverade chattar", "archived-chat-export": "arkiverad-chatt-export", "Are you sure you want to clear all memories? This action cannot be undone.": "Är du säker på att du vill rensa alla minnen? Denna åt<PERSON>rd kan inte ång<PERSON>.", "Are you sure you want to delete this channel?": "Är du säker på att du vill radera denna kanal?", "Are you sure you want to delete this message?": "Är du säker på att du vill radera detta meddelande?", "Are you sure you want to unarchive all archived chats?": "Är du säker på att du vill avarkivera alla arkiverade chattar?", "Are you sure?": "<PERSON>r du säker?", "Arena Models": "Arenamodeller", "Artifacts": "Arte<PERSON><PERSON><PERSON>", "Ask": "<PERSON><PERSON><PERSON>", "Ask a question": "Ställ en fråga", "Assistant": "Assistent", "Attach file from knowledge": "Bifoga fil från kuns<PERSON>p", "Attention to detail": "Detaljerad uppmärksamhet", "Attribute for Mail": "Attribut för e-post", "Attribute for Username": "Attribut för anvä<PERSON>rnamn", "Audio": "<PERSON><PERSON><PERSON>", "August": "augusti", "Auth": "Autentisering", "Authenticate": "Autentisera", "Authentication": "Autentisering", "Auto": "Auto", "Auto-Copy Response to Clipboard": "Automatisk kopiering av svar till urklipp", "Auto-playback response": "Automatisk uppspelning av svar", "Autocomplete Generation": "Automatisk komplettering av generering", "Autocomplete Generation Input Max Length": "Maxlängd för inmatning av automatisk komplettering", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 bas-URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 bas-URL krävs.", "Available list": "Tillgänglig lista", "Available Tools": "Tillgängliga verktyg", "available!": "tillgänglig!", "Awful": "Hemsk", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Azure-region", "Back": "Tillbaka", "Bad Response": "<PERSON><PERSON><PERSON><PERSON> svar", "Banners": "Banners", "Base Model (From)": "<PERSON><PERSON><PERSON> (Från)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "<PERSON><PERSON><PERSON>", "Being lazy": "Är lat", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing Search V7 Endpoint", "Bing Search V7 Subscription Key": "Bing Search V7 Prenumerationsnyckel", "Bocha Search API Key": "Bocha Search API-nyckel", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Öka eller straffa specifika tokens för begränsade svar. Biasvärden kommer att klämmas fast mellan -100 och 100 (inklusive). (Standard: ingen)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Både Docling OCR Engine och Language(s) måste anges eller lämnas tomma.", "Brave Search API Key": "API-nyckel för Brave Search", "Bullet List": "", "By {{name}}": "Av {{name}}", "Bypass Embedding and Retrieval": "Kringgå inbäddning och hämtning", "Bypass Web Loader": "Kringgå webbläsare", "Cache Base Model List": "", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Samtalsfunktionen är inte kompatibel med Web Tal-till-text motor", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capabilities": "Kapa<PERSON>ter", "Capture": "Fånga", "Capture Audio": "Fånga ljud", "Certificate Path": "Certifikatväg", "Change Password": "<PERSON><PERSON>", "Channel Name": "<PERSON><PERSON><PERSON><PERSON>", "Channels": "<PERSON><PERSON><PERSON>", "Character": "Tecken", "Character limit for autocomplete generation input": "Teckengräns för inmatning av automatisk komplettering", "Chart new frontiers": "Utforska nya gränser", "Chat": "<PERSON><PERSON>", "Chat Background Image": "Bakgrundsbild för chatt", "Chat Bubble UI": "UI för <PERSON>", "Chat Controls": "<PERSON><PERSON>kon<PERSON>ller", "Chat direction": "Chattriktning", "Chat Overview": "Chattöversikt", "Chat Permissions": "Chattbehörigheter", "Chat Tags Auto-Generation": "Automatisk generering av chatt-taggar", "Chats": "Chattar", "Check Again": "Kontrollera igen", "Check for updates": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>", "Checking for updates...": "<PERSON><PERSON><PERSON> efter uppdateringar...", "Choose a model before saving...": "V<PERSON><PERSON><PERSON> en modell innan du sparar...", "Chunk Overlap": "Överlappning", "Chunk Size": "Chunk-storlek", "Ciphers": "<PERSON><PERSON>", "Citation": "Citat", "Citations": "Citeringar", "Clear memory": "<PERSON><PERSON> minnet", "Clear Memory": "<PERSON><PERSON> minnet", "click here": "k<PERSON>a här", "Click here for filter guides.": "<PERSON><PERSON><PERSON> här för <PERSON>.", "Click here for help.": "<PERSON><PERSON><PERSON> här för hj<PERSON><PERSON>.", "Click here to": "<PERSON><PERSON><PERSON> här för att", "Click here to download user import template file.": "<PERSON><PERSON><PERSON> här för att ladda ner en mall för användarimport", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> här för att läsa mer om faster-whisper och se vilka modeller som finns tillgängliga", "Click here to see available models.": "<PERSON><PERSON><PERSON> här för att se tillgängliga modeller", "Click here to select": "<PERSON><PERSON><PERSON> här för att välja", "Click here to select a csv file.": "<PERSON><PERSON><PERSON> här för att välja en csv-fil.", "Click here to select a py file.": "<PERSON><PERSON><PERSON> här för att välja en python-fil.", "Click here to upload a workflow.json file.": "<PERSON><PERSON><PERSON> här för att ladda upp en workflow.json fil", "click here.": "k<PERSON>a här.", "Click on the user role button to change a user's role.": "<PERSON><PERSON><PERSON> på knappen för användarroll för att ändra en användares roll.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Skrivbehörighet för urklipp nekad. Kontrollera dina webbläsarinställningar för att bevilja nödvändig åtkomst.", "Clone": "Klon", "Clone Chat": "<PERSON><PERSON><PERSON> chatt", "Clone of {{TITLE}}": "Klon av {{TITLE}}", "Close": "Stäng", "Close Configure Connection Modal": "", "Close modal": "Stäng modal", "Close settings modal": "Stäng inställningsmodal", "Code Block": "", "Code execution": "Kodkörning", "Code Execution": "Kodkörning", "Code Execution Engine": "Motor för kodkörning", "Code Execution Timeout": "Timeout fö<PERSON> k<PERSON>", "Code formatted successfully": "Koden formaterades framgångsrikt", "Code Interpreter": "Kodtolk", "Code Interpreter Engine": "Motor för kodtolk", "Code Interpreter Prompt Template": "Prompt<PERSON><PERSON> för k<PERSON>", "Collapse": "Fäll ihop", "Collection": "<PERSON><PERSON>", "Color": "<PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API-nyckel", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL krävs.", "ComfyUI Workflow": "ComfyUI Arbetsflöde", "ComfyUI Workflow Nodes": "ComfyUI Arbetsflödesnoder", "Command": "Kommando", "Comment": "", "Completions": "Slutföranden", "Concurrent Requests": "<PERSON><PERSON><PERSON> an<PERSON>", "Configure": "Konfigurera", "Confirm": "Bekräfta", "Confirm Password": "Bekräfta lösenord", "Confirm your action": "Bekräfta din åtgärd", "Confirm your new password": "Bekräfta ditt nya l<PERSON>ord", "Connect to your own OpenAI compatible API endpoints.": "Anslut till dina egna OpenAI-kompatibla API-endpoints.", "Connect to your own OpenAPI compatible external tool servers.": "Anslut till dina egna OpenAPI-kompatibla externa verktygsservrar.", "Connection failed": "Anslutning misslyck<PERSON>", "Connection successful": "Anslutning lyckades", "Connection Type": "Anslutningstyp", "Connections": "Anslutningar", "Connections saved successfully": "Anslutningar sparades framgångsrikt", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Begränsar ansträngningen för resonemang för resonemangsmodeller. Gäller endast resonemangsmodeller från specifika leverantörer som stöder resonemangsinsats.", "Contact Admin for WebUI Access": "Kontakta administratören för att få åtkomst till WebUI", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction Engine": "Motor för innehållsextrahering", "Continue Response": "Fortsätt svar", "Continue with {{provider}}": "Fortsätt med {{provider}}", "Continue with Email": "Fortsätt med e-post", "Continue with LDAP": "Fortsätt med LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Styr hur meddelandetext delas upp för TTS-förfrågningar. 'Punctuation' delar upp i meningar, 'paragraphs' delar upp i stycken och 'none' behåller meddelandet som en enda sträng.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Kontrollera upprepningen av tokensekvenser i den genererade texten. Ett högre värde (t.ex. 1.5) kommer att straffa upprepningar starkare, medan ett lägre värde (t.ex. 1.1) kommer att vara mer förlåtande. Vid 1 är det inaktiverat.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Kontrollerar balansen mellan sammanhang och mångfald i utdata. Ett lägre värde kommer att resultera i mer fokuserad och sammanhängande text.", "Copied": "<PERSON><PERSON><PERSON>", "Copied link to clipboard": "Kopierad länk till urklipp", "Copied shared chat URL to clipboard!": "<PERSON><PERSON><PERSON> delad chatt-URL till urklipp!", "Copied to clipboard": "Kopierad till urklipp", "Copy": "<PERSON><PERSON><PERSON>", "Copy Formatted Text": "Kopiera formaterad text", "Copy last code block": "<PERSON><PERSON><PERSON> sista kodblock", "Copy last response": "<PERSON><PERSON><PERSON> sista svar", "Copy link": "", "Copy Link": "<PERSON><PERSON><PERSON> länk", "Copy to clipboard": "Kopiera till urklipp", "Copying to clipboard was successful!": "<PERSON><PERSON>ring till urk<PERSON><PERSON> lyck<PERSON>!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS måste vara korrekt konfigurerad av leverantören för att tillåta förfrågningar från Open WebUI.", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "Skapa en kunskapsbas", "Create a model": "Skapa en modell", "Create Account": "Skapa konto", "Create Admin Account": "Skapa administratörskonto", "Create Channel": "Skapa kanal", "Create Folder": "", "Create Group": "Skapa grupp", "Create Knowledge": "Skapa kunskap", "Create new key": "Skapa ny nyckel", "Create new secret key": "Skapa ny hemlig nyckel", "Create Note": "<PERSON><PERSON><PERSON> anteckning", "Create your first note by clicking on the plus button below.": "Skapa din första anteckning genom att klicka på plusknappen nedan.", "Created at": "Skapad", "Created At": "Skapad", "Created by": "Skapad av", "CSV Import": "CSV-import", "Ctrl+Enter to Send": "Ctrl+<PERSON><PERSON> för att skicka", "Current Model": "Aktuell modell", "Current Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom": "Anpassad", "Custom description enabled": "", "Custom Parameter Name": "Anpassat parameternamn", "Custom Parameter Value": "Anpassat parametervärde", "Danger Zone": "Fara", "Dark": "M<PERSON><PERSON>", "Database": "Databas", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "Datalab Marker API-nyckel krävs.", "DD/MM/YYYY": "", "December": "december", "Default": "Standard", "Default (Open AI)": "Standard (Open AI)", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Standardläget fungerar med ett bredare utbud av modeller genom att anropa verktyg en gång före körning. Inbyggt läge utnyttjar modellens inbyggda verktygsanropsfunktioner, men kräver att modellen har stöd för den här funktionen.", "Default Model": "Standardmodell", "Default model updated": "Standardmodell uppdaterad", "Default Models": "Standardmodeller", "Default permissions": "Standardbehörigheter", "Default permissions updated successfully": "Standardbehörigheter uppdaterades framgångsrikt", "Default Prompt Suggestions": "Standardinstruktionsförslag", "Default to 389 or 636 if TLS is enabled": "Standard till 389 eller 636 om TLS är aktiverat", "Default to ALL": "Standard till ALLA", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Standard till segmenterad hämtning för fokuserad och relevant innehållsextrahering, detta rekommenderas för de flesta fall.", "Default User Role": "Standardanvändarroll", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "Ta bort en modell", "Delete All Chats": "Ta bort alla chattar", "Delete All Models": "Ta bort alla modeller", "Delete chat": "<PERSON><PERSON><PERSON> chatt", "Delete Chat": "<PERSON><PERSON><PERSON> chatt", "Delete chat?": "<PERSON><PERSON><PERSON> chatt?", "Delete folder?": "<PERSON><PERSON><PERSON> mapp?", "Delete function?": "Ra<PERSON>a funktion?", "Delete Message": "<PERSON><PERSON><PERSON> medd<PERSON>", "Delete message?": "Radera meddelande?", "Delete note?": "Ra<PERSON>a anteckning?", "Delete prompt?": "Ra<PERSON><PERSON> prompt?", "delete this link": "radera denna länk", "Delete tool?": "Radera verk<PERSON>g?", "Delete User": "<PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Raderad {{deleteModelTag}}", "Deleted {{name}}": "Borttagen {{name}}", "Deleted User": "Raderad användare", "Deployment names are required for Azure OpenAI": "Distributionsnamn krävs för Azure OpenAI", "Describe Pictures in Documents": "Beskriv bilder i dokument", "Describe your knowledge base and objectives": "Beskriv din kunskapsbas och dina mål", "Description": "Beskrivning", "Detect Artifacts Automatically": "Detektera artefakter automatiskt", "Dictate": "Diktera", "Didn't fully follow instructions": "Följde inte instruktionerna", "Direct": "Direkt", "Direct Connections": "<PERSON><PERSON><PERSON>", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Direkta anslutningar tillåter användare att ansluta till sina egna OpenAI-kompatibla API-endpoints.", "Direct Tool Servers": "Direkta verktygsservrar", "Disable Code Interpreter": "", "Disable Image Extraction": "Inaktivera bildextra<PERSON>", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Inaktivera bildextrahering från PDF-filen. Om Använd LLM är aktiverat kommer bilder att automatiskt bildtextas. Standardvärdet är False.", "Disabled": "Inaktiverad", "Discover a function": "<PERSON><PERSON><PERSON><PERSON> en funktion", "Discover a model": "<PERSON><PERSON><PERSON><PERSON> en modell", "Discover a prompt": "Upptäck en instruktion", "Discover a tool": "Upptäck ett verktyg", "Discover how to use Open WebUI and seek support from the community.": "Upptäck hur du använder Open WebUI och sök stöd från communityn.", "Discover wonders": "Upptäck underverk", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON>, ladda ner och utforska anpassade funktioner", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, ladda ner och utforska anpassade instruktioner", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON>, ladda ner och utforska anpassade verktyg", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, ladda ner och utforska modellförinställningar", "Display": "Visa", "Display Emoji in Call": "Visa Emoji under samtal", "Display the username instead of You in the Chat": "Visa användarnamnet istället för du i chatten", "Displays citations in the response": "<PERSON>r citeringa<PERSON> i svaret", "Dive into knowledge": "Dyk in i kunskap", "Do not install functions from sources you do not fully trust.": "Installera inte funktioner från källor du inte litar på.", "Do not install tools from sources you do not fully trust.": "Installera inte verktyg från källor du inte litar på.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling Server URL krävs.", "Document": "Dokument", "Document Intelligence": "Dokumentinformation", "Document Intelligence endpoint and key required.": "Dokumentinformationsslutpunkt och nyckel krävs.", "Documentation": "Dokumentation", "Documents": "Dokument", "does not make any external connections, and your data stays securely on your locally hosted server.": "gör inga externa anslut<PERSON>, och dina data förblir säkra på din lokalt värdade server.", "Domain Filter List": "Domänfilterlista", "Don't have an account?": "Har du inget konto?", "don't install random functions from sources you don't trust.": "installera inte slumpmässiga funktioner från källor du inte litar på.", "don't install random tools from sources you don't trust.": "installera inte slumpmässiga verktyg från källor du inte litar på.", "Don't like the style": "Tycker inte om utseendet", "Done": "<PERSON><PERSON>", "Download": "Ladda ner", "Download as SVG": "Ladda ner som SVG", "Download canceled": "Nedladdning avbruten", "Download Database": "Ladda ner databas", "Drag and drop a file to upload or select a file to view": "<PERSON>a och släpp en fil för att ladda upp eller välj en fil för att visa", "Draw": "<PERSON>", "Drop any files here to upload": "<PERSON><PERSON><PERSON><PERSON> alla filer här för att ladda upp", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "t.ex. '30s', '10m'. Gil<PERSON>ga tidsenheter är 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "t.ex. \"json\" eller ett JSON-schema", "e.g. 60": "t.ex. 60", "e.g. A filter to remove profanity from text": "t.ex. Ett filter för att ta bort svordomar från text", "e.g. en": "t.ex. en", "e.g. My Filter": "t.ex. Mitt filter", "e.g. My Tools": "t.ex. <PERSON> verktyg", "e.g. my_filter": "t.ex. my_filter", "e.g. my_tools": "t.ex. my_tools", "e.g. pdf, docx, txt": "t.ex. pdf, docx, txt", "e.g. Tools for performing various operations": "t.ex. Verktyg för att utföra olika operationer", "e.g., 3, 4, 5 (leave blank for default)": "t.ex., 3, 4, 5 (lämna tomt för standard)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "t.ex., en-US,ja-JP (lämna tomt för automatisk detektering)", "e.g., westus (leave blank for eastus)": "t.ex., westus (lämna tomt för eastus)", "e.g.) en,fr,de": "t.ex.) en,fr,de", "Edit": "Rediger<PERSON>", "Edit Arena Model": "Redigera Arenamodel", "Edit Channel": "<PERSON><PERSON><PERSON> kanal", "Edit Connection": "<PERSON><PERSON><PERSON> an<PERSON>lut<PERSON>", "Edit Default Permissions": "Redigera standardbehörigheter", "Edit Folder": "", "Edit Memory": "<PERSON><PERSON>a minne", "Edit User": "<PERSON>igera an<PERSON>", "Edit User Group": "Redigera användargrupp", "Edited": "", "Editing": "", "Eject": "Mata ut", "ElevenLabs": "ElevenLabs", "Email": "E-post", "Embark on adventures": "Ge dig ut på äventyr", "Embedding": "Inbäddning", "Embedding Batch Size": "Batchstorlek för inbäddning", "Embedding Model": "Inbäddningsmodell", "Embedding Model Engine": "Motor för inbäddningsmodell", "Embedding model set to \"{{embedding_model}}\"": "Inbäddningsmodell inställd på \"{{embedding_model}}\"", "Enable API Key": "Aktivera API-nyckel", "Enable autocomplete generation for chat messages": "Aktivera automatisk komplettering av generering för chat<PERSON>", "Enable Code Execution": "Aktivera kodkörning", "Enable Code Interpreter": "Aktivera kodtolk", "Enable Community Sharing": "Aktivera community-delning", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Aktivera minneslå<PERSON>ning (mlock) för att för<PERSON>dra att modelldata swappas ut ur RAM. Detta alternativ låser modellens arbetsuppsättning av sidor i RAM, vilket säkerställer att de inte swappas ut till disk. Detta kan bidra till att upprätthålla prestanda genom att undvika sidfel och säkerställa snabb dataåtkomst.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Aktivera minnesmappning (mmap) för att ladda modelldata. Detta alternativ tillåter systemet att använda disklagring som en förlängning av RAM genom att behandla diskfiler som om de vore i RAM. Detta kan förbättra modellens prestanda genom att möjliggöra snabbare dataåtkomst. Det kanske dock inte fungerar korrekt med alla system och kan förbruka en betydande mängd diskutrymme.", "Enable Message Rating": "Aktivera meddela<PERSON>betyg", "Enable Mirostat sampling for controlling perplexity.": "Aktivera Mirostat-sampling för att kontrollera perplexitet.", "Enable New Sign Ups": "Aktivera nya registreringar", "Enabled": "Aktiverad", "Endpoint URL": "Endpoint URL", "Enforce Temporary Chat": "<PERSON><PERSON>a fram till<PERSON><PERSON><PERSON><PERSON> chatt", "Enhance": "Förbättra", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Se till att din CSV-fil innehåller fyra kolumner i denna ordning: Name, Email, Password, Role.", "Enter {{role}} message here": "Skriv {{role}} meddela<PERSON> här", "Enter a detail about yourself for your LLMs to recall": "Skriv en detalj om dig själv för att dina LLMs ska komma ihåg", "Enter a title for the pending user info overlay. Leave empty for default.": "Ange en titel för den väntande användarinformationen. Lämna tomt för standard.", "Enter a watermark for the response. Leave empty for none.": "Ange en vattenstämpel för svaret. Lämna tomt för ingen.", "Enter api auth string (e.g. username:password)": "Ange API-autentiseringssträng (t.ex. användarnamn:lösenord)", "Enter Application DN": "Ange Application DN", "Enter Application DN Password": "Ange Application DN-lösenord", "Enter Bing Search V7 Endpoint": "Ange Bing Search V7 Endpoint", "Enter Bing Search V7 Subscription Key": "Ange Bing Search V7-prenumerations<PERSON><PERSON>l", "Enter BM25 Weight": "Ange BM25-vikt", "Enter Bocha Search API Key": "Ange Bocha Search API-nyckel", "Enter Brave Search API Key": "Ange API-nyckel för Brave Search", "Enter certificate path": "Ange certifikatväg", "Enter CFG Scale (e.g. 7.0)": "Ange CFG-skala (t.ex. 7.0)", "Enter Chunk Overlap": "<PERSON><PERSON>", "Enter Chunk Size": "<PERSON><PERSON>", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "<PERSON><PERSON> komma-separerade \"token:bias_value\"-par (exempel: 5432:100, 413:-100)", "Enter Config in JSON format": "Ange konfiguration i JSON-format", "Enter content for the pending user info overlay. Leave empty for default.": "Ange innehåll för den väntande användarinformationen. Lämna tomt för standard.", "Enter Datalab Marker API Key": "Ange Datalab Marker API-nyckel", "Enter description": "<PERSON><PERSON>", "Enter Docling OCR Engine": "<PERSON><PERSON> OCR-motor", "Enter Docling OCR Language(s)": "<PERSON><PERSON> OCR-spr<PERSON>k", "Enter Docling Server URL": "Ange Docling Server URL", "Enter Document Intelligence Endpoint": "Ange Document Intelligence Endpoint", "Enter Document Intelligence Key": "Ange Document Intelligence-nyckel", "Enter domains separated by commas (e.g., example.com,site.org)": "<PERSON><PERSON> domäner separerade med kommatecken (t.ex. example.com,site.org)", "Enter Exa API Key": "Ange Exa API-nyckel", "Enter External Document Loader API Key": "Ange API-nyckel för extern dokumentinläsare", "Enter External Document Loader URL": "Ange URL för extern dokumentinläsare", "Enter External Web Loader API Key": "Ange API-nyckel för extern webbinläsare", "Enter External Web Loader URL": "Ange URL för extern webbinläsare", "Enter External Web Search API Key": "Ange API-nyckel för extern webbsökning", "Enter External Web Search URL": "Ange URL för extern webbsökning", "Enter Firecrawl API Base URL": "Ange Firecrawl API Base URL", "Enter Firecrawl API Key": "Ange Firecrawl API-nyckel", "Enter folder name": "", "Enter Github Raw URL": "<PERSON><PERSON> Raw URL", "Enter Google PSE API Key": "Ange Google PSE API-nyckel", "Enter Google PSE Engine Id": "Ange Google PSE Engine Id", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON> (t.ex. 512x512)", "Enter Jina API Key": "Ange Jina API-nyckel", "Enter Jupyter Password": "<PERSON><PERSON>", "Enter Jupyter Token": "<PERSON><PERSON>-token", "Enter Jupyter URL": "<PERSON><PERSON> URL", "Enter Kagi Search API Key": "Ange Kagi Search API-nyckel", "Enter Key Behavior": "<PERSON><PERSON> n<PERSON>ckel<PERSON>de", "Enter language codes": "Skriv språkkoder", "Enter Mistral API Key": "Ange Mistral API-nyckel", "Enter Model ID": "Ange modell-ID", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON> model<PERSON>gg (t.ex. {{modelTag}})", "Enter Mojeek Search API Key": "Ange Mojeek Search API-nyckel", "Enter name": "<PERSON><PERSON> namn", "Enter New Password": "Ange n<PERSON>t lösenord", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON> antal steg (t.ex. 50)", "Enter Perplexity API Key": "Ange Perplexity API-nyckel", "Enter Playwright Timeout": "<PERSON><PERSON>-timeout", "Enter Playwright WebSocket URL": "<PERSON><PERSON> Playwright WebSocket URL", "Enter proxy URL (e.g. **************************:port)": "<PERSON><PERSON> proxy-URL (t.ex. **************************:port)", "Enter reasoning effort": "<PERSON><PERSON> reson<PERSON>", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON> (t.ex. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON> (t.ex. Karras)", "Enter Score": "<PERSON><PERSON> bet<PERSON>g", "Enter SearchApi API Key": "Ange SearchApi API-nyckel", "Enter SearchApi Engine": "Ange SearchApi Engine", "Enter Searxng Query URL": "Ange Searxng Query URL", "Enter Seed": "<PERSON><PERSON>", "Enter SerpApi API Key": "Ange SerpApi API-nyckel", "Enter SerpApi Engine": "Ange SerpApi Engine", "Enter Serper API Key": "<PERSON><PERSON>-nyckel", "Enter Serply API Key": "Ange Serply API-nyckel", "Enter Serpstack API Key": "Ange <PERSON>pstack API-nyckel", "Enter server host": "<PERSON><PERSON>", "Enter server label": "<PERSON><PERSON> server<PERSON>", "Enter server port": "Ange serverport", "Enter Sougou Search API sID": "Ange Sougou Search API sID", "Enter Sougou Search API SK": "Ange Sougou Search API SK", "Enter stop sequence": "<PERSON><PERSON>", "Enter system prompt": "<PERSON><PERSON> systemprompt", "Enter system prompt here": "<PERSON><PERSON><PERSON>rompt här", "Enter Tavily API Key": "<PERSON><PERSON>-ny<PERSON>l", "Enter Tavily Extract Depth": "<PERSON><PERSON> Extract De<PERSON>h", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Ange den offentliga URL:en för din WebUI. Denna URL kommer att användas för att generera länkar i notifikationerna.", "Enter the URL of the function to import": "Ange URL:en för funktionen att importera", "Enter the URL to import": "Ange URL:en att importera", "Enter Tika Server URL": "<PERSON><PERSON> Server URL", "Enter timeout in seconds": "Ange timeout i sekunder", "Enter to Send": "<PERSON><PERSON> för att skicka", "Enter Top K": "Ange Top K", "Enter Top K Reranker": "<PERSON><PERSON> K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON>e URL (t.ex. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Ange URL (t.ex. http://localhost:11434)", "Enter Yacy Password": "<PERSON><PERSON>", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "<PERSON><PERSON>RL (t.ex. http://yacy.example.com:8090)", "Enter Yacy Username": "<PERSON><PERSON>an<PERSON><PERSON><PERSON><PERSON>", "Enter your current password": "<PERSON><PERSON> ditt nuvarande l<PERSON>", "Enter Your Email": "Ange din e-post", "Enter Your Full Name": "<PERSON><PERSON> ditt fullständiga namn", "Enter your message": "Sk<PERSON>v ditt meddelande", "Enter your name": "Skriv ditt namn", "Enter Your Name": "Skriv ditt namn", "Enter your new password": "<PERSON><PERSON> ditt nya l<PERSON>", "Enter Your Password": "<PERSON><PERSON> l<PERSON>", "Enter Your Role": "Ange din roll", "Enter Your Username": "<PERSON><PERSON> ditt användarnamn", "Enter your webhook URL": "<PERSON><PERSON> din webhook-URL", "Error": "<PERSON><PERSON>", "ERROR": "FEL", "Error accessing Google Drive: {{error}}": "Fel vid åtkomst till Google Drive: {{error}}", "Error accessing media devices.": "Fel vid åtkomst till mediaenheter.", "Error starting recording.": "Fel vid start av inspelning.", "Error unloading model: {{error}}": "Fel vid avlastning av modell: {{error}}", "Error uploading file: {{error}}": "Fel vid uppladdning av fil: {{error}}", "Evaluations": "Utvärderingar", "Everyone": "", "Exa API Key": "Exa API-nyckel", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Exempel: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Exempel: ALLA", "Example: mail": "Exempel: mail", "Example: ou=users,dc=foo,dc=example": "Exempel: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Exempel: sAMAccountName eller uid eller userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Du har överskridit antalet platser i din licens. Kontakta support för att öka antalet platser.", "Exclude": "Exkludera", "Execute code for analysis": "<PERSON><PERSON><PERSON> kod för analys", "Executing **{{NAME}}**...": "<PERSON><PERSON><PERSON> **{{NAME}}**...", "Expand": "Expandera", "Experimental": "Experimentell", "Explain": "Förklara", "Explore the cosmos": "Utforska kosmos", "Export": "Export", "Export All Archived Chats": "Exportera alla arkiverade chattar", "Export All Chats (All Users)": "Exportera alla chattar (alla användare)", "Export chat (.json)": "<PERSON><PERSON>a chatt (.json)", "Export Chats": "Exportera chattar", "Export Config to JSON File": "Exportera konfiguration till JSON-fil", "Export Functions": "Exportera funktioner", "Export Models": "Exportera modeller", "Export Presets": "Exportera förinställningar", "Export Prompt Suggestions": "Exportera promptförslag", "Export Prompts": "Exportera instruktioner", "Export to CSV": "Exportera till CSV", "Export Tools": "Exportera verktyg", "External": "Extern", "External Document Loader URL required.": "Extern dokumentinläsare URL krävs.", "External Task Model": "Extern uppgiftsmodell", "External Web Loader API Key": "Extern webbinläsare API-nyckel", "External Web Loader URL": "Extern webbinläsare URL", "External Web Search API Key": "Extern webbsökning API-nyckel", "External Web Search URL": "Extern webbsökning URL", "Fade Effect for Streaming Text": "", "Failed to add file.": "Missly<PERSON>ades med att lägga till fil.", "Failed to connect to {{URL}} OpenAPI tool server": "<PERSON><PERSON><PERSON><PERSON> med att ansluta till {{URL}} OpenAPI-verktygsserver", "Failed to copy link": "Misslyckades med att kopiera länk", "Failed to create API Key.": "<PERSON><PERSON><PERSON><PERSON> med att skapa API-nyckel.", "Failed to delete note": "<PERSON><PERSON><PERSON><PERSON> med att ta bort anteckning", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "Misslyckades med att hämta modeller", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "Missly<PERSON><PERSON> med att läsa in filinnehåll.", "Failed to read clipboard contents": "Misslyckades med att läsa urklippsinnehåll", "Failed to save connections": "<PERSON><PERSON><PERSON><PERSON> med att spara anslutningar", "Failed to save models configuration": "Misslyckades med att spara modellkonfiguration", "Failed to update settings": "Misslyckades med att uppdatera inställningarna", "Failed to upload file.": "<PERSON><PERSON><PERSON>ades med att ladda upp fil.", "Features": "<PERSON><PERSON><PERSON>", "Features Permissions": "Funktionsbehörigheter", "February": "februari", "Feedback Details": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Feedback History": "Feedbackhistorik", "Feedbacks": "Återkopplingar", "Feel free to add specific details": "Tveka inte att lägga till specifika detaljer", "File": "Fil", "File added successfully.": "Filen har lagts till.", "File content updated successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> har uppdaterats.", "File Mode": "Fil-läge", "File not found.": "Fil hittades inte.", "File removed successfully.": "Filen har tagits bort.", "File size should not exceed {{maxSize}} MB.": "Filstorleken får inte överstiga {{maxSize}} MB.", "File Upload": "Filuppladdning", "File uploaded successfully": "Filen har laddats upp", "Files": "Filer", "Filter": "", "Filter is now globally disabled": "<PERSON>lter är nu globalt inaktiverat", "Filter is now globally enabled": "Filter är nu globalt aktiverat", "Filters": "Filter", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingeravtrycksmanipulering upptäckt: Kan inte använda initialer som avatar. Återställning till standardprofilbild.", "Firecrawl API Base URL": "Firecrawl API Base URL", "Firecrawl API Key": "Firecrawl API-nyckel", "Fluidly stream large external response chunks": "Strömma flytande stora externa svarschunks", "Focus chat input": "Fokusera på chattfältet", "Folder deleted successfully": "<PERSON><PERSON> har tagits bort", "Folder Name": "", "Folder name cannot be empty.": "Mappnamnet får inte vara tomt.", "Folder name updated successfully": "Mappnamnet har uppdaterats", "Folder updated successfully": "", "Follow up": "<PERSON><PERSON><PERSON><PERSON> upp", "Follow Up Generation": "Generering av uppföljning", "Follow Up Generation Prompt": "Prompt för generering av uppfö<PERSON>jning", "Follow-Up Auto-Generation": "Automatisk generering av uppföljning", "Followed instructions perfectly": "Följde instruktionerna perfekt", "Force OCR": "Tvinga OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "Tvinga OCR på alla sidor i PDF:en. <PERSON><PERSON> kan leda till sämre resultat om du har bra text i dina PDF:er. Standardvärdet är False.", "Forge new paths": "Skapa nya vägar", "Form": "Formulär", "Format your variables using brackets like this:": "Formatera dina variabler med hakparenteser så här:", "Forwards system user session credentials to authenticate": "Vidarebefordrar systemanvändarsessionens autentiseringsuppgifter för att autentisera", "Full Context Mode": "Fullständigt kontextläge", "Function": "Funktion", "Function Calling": "Funktionsanrop", "Function created successfully": "Funktionen har skapats", "Function deleted successfully": "Funktionen har tagits bort", "Function Description": "Funktionsbeskrivning", "Function ID": "Funktions-ID", "Function imported successfully": "Funktionen har importerats", "Function is now globally disabled": "Funktionen är nu globalt inaktiverad", "Function is now globally enabled": "Funktionen är nu globalt aktiverad", "Function Name": "Funktionsnamn", "Function updated successfully": "Funk<PERSON><PERSON> har uppdaterats", "Functions": "<PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution.": "Funktioner <PERSON><PERSON><PERSON> godtycklig kodkörning.", "Functions imported successfully": "Funktioner har importerats", "Gemini": "Gemini", "Gemini API Config": "Gemini API-konfiguration", "Gemini API Key is required.": "Gemini API-nyckel krävs.", "General": "Allmän", "Generate": "<PERSON><PERSON>", "Generate an image": "Generera en bild", "Generate Image": "Generera bild", "Generate prompt pair": "Generera promptpar", "Generating search query": "<PERSON><PERSON><PERSON>", "Generating...": "Genererar...", "Get information on {{name}} in the UI": "", "Get started": "<PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "Kom igång med {{WEBUI_NAME}}", "Global": "Global", "Good Response": "<PERSON><PERSON> <PERSON>var", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API-nyckel", "Google PSE Engine Id": "Google PSE Engine Id", "Group created successfully": "Gruppen har skapats", "Group deleted successfully": "Gruppen har tagits bort", "Group Description": "Gruppbeskrivning", "Group Name": "Gruppnamn", "Group updated successfully": "Gruppen har uppdaterats", "Groups": "Grupper", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Haptisk återkoppling", "Hello, {{name}}": "Hej, {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Hjälp oss att skapa den bästa community-topplistan genom att dela din feedbackhistorik!", "Hex Color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hex Color - Leave empty for default color": "Hexfärg - Lämna tomt för standardfärg", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Hide from Sidebar": "<PERSON><PERSON><PERSON><PERSON> frå<PERSON> si<PERSON>", "Hide Model": "<PERSON><PERSON><PERSON><PERSON> modell", "High Contrast Mode": "Högkontrastläge", "Home": "<PERSON><PERSON>", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "Hur kan jag hjälpa dig idag?", "How would you rate this response?": "Hur skulle du betygsätta detta svar?", "HTML": "HTML", "Hybrid Search": "Hybrid sökning", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Jag bekräftar att jag har läst och förstår innebörden av min handling. Jag är medveten om riskerna med att köra godtycklig kod och jag har verifierat källans trovärdighet.", "ID": "ID", "iframe Sandbox Allow Forms": "iframe Sandbox Tillåt formulär", "iframe Sandbox Allow Same Origin": "iframe Sandbox Tillåt samma ursprung", "Ignite curiosity": "Väck nyfikenhet", "Image": "Bild", "Image Compression": "Bildkomprimering", "Image Compression Height": "Bildkomprimeringshöjd", "Image Compression Width": "Bildkomprimeringsbredd", "Image Generation": "Bildgenerering", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (experimentell)", "Image Generation Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Image Max Compression Size": "Maximal bildkomprimeringsstorlek", "Image Max Compression Size height": "Maximal bildkomprimeringsstorlek höjd", "Image Max Compression Size width": "Maximal bildkomprimeringsstorlek bredd", "Image Prompt Generation": "Generering av bildprompt", "Image Prompt Generation Prompt": "Prompt för generering av bildprompt", "Image Settings": "Bildinställningar", "Images": "Bilder", "Import": "Importera", "Import Chats": "Importera chattar", "Import Config from JSON File": "Importera konfiguration från JSON-fil", "Import From Link": "Importera från länk", "Import Functions": "Importera funktioner", "Import Models": "Importera modeller", "Import Notes": "Importera anteckningar", "Import Presets": "Importera förinställningar", "Import Prompt Suggestions": "Importera promptförslag", "Import Prompts": "Importera instruktioner", "Import Tools": "Importera verktyg", "Include": "Inkludera", "Include `--api-auth` flag when running stable-diffusion-webui": "Ink<PERSON>ra flaggan `--api-auth` när du kör stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Ink<PERSON>ra flaggan `--api` när du kör stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "<PERSON>å<PERSON><PERSON> hur snabbt algoritmen svarar på feedback från den genererade texten. En lägre inlärningshastighet resulterar i långsammare justeringar, medan en högre inlärningshastighet gör algoritmen mer responsiv.", "Info": "Information", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Injicera hela innehållet som kontext för omfattande bearbetning, detta rekommenderas för komplexa frågor.", "Input commands": "Indatakommandon", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Installera fr<PERSON><PERSON>-URL", "Instant Auto-Send After Voice Transcription": "Skicka automatiskt efter rösttranskribering", "Integration": "Integration", "Interface": "Gränssnitt", "Invalid file content": "<PERSON><PERSON><PERSON><PERSON><PERSON> fi<PERSON>", "Invalid file format.": "Ogiltigt filformat.", "Invalid JSON file": "Ogiltig JSON-fil", "Invalid Tag": "Ogi<PERSON><PERSON> tagg", "is typing...": "skriver...", "Italic": "", "January": "januari", "Jina API Key": "Jina API-nyckel", "join our Discord for help.": "gå med i vår Discord för hjälp.", "JSON": "JSON", "JSON Preview": "Förhandsversion av JSON", "July": "juli", "June": "juni", "Jupyter Auth": "<PERSON><PERSON><PERSON>-autentisering", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT-utgångsdatum", "JWT Token": "JWT-token", "Kagi Search API Key": "Kagi Search API-nyckel", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "Behåll i sidofältet", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Tangentbordsgenvägar", "Knowledge": "Kunskap", "Knowledge Access": "Kunskapsåtkomst", "Knowledge Base": "", "Knowledge created successfully.": "Kunskapen har skapats.", "Knowledge deleted successfully.": "Kunskapen har tagits bort.", "Knowledge Public Sharing": "Offentlig delning av kunskap", "Knowledge reset successfully.": "Kunskapen har å<PERSON>tällts.", "Knowledge updated successfully": "Kunskapen har uppdaterats", "Kokoro.js (Browser)": "Kokoro.js (webbläsare)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "Etikett", "Landing Page Mode": "<PERSON>äge för <PERSON>ning<PERSON>", "Language": "Språk", "Language Locales": "Språklokaler", "Languages": "Språk", "Last Active": "Senast aktiv", "Last Modified": "<PERSON><PERSON>", "Last reply": "<PERSON><PERSON><PERSON> svar", "LDAP": "LDAP", "LDAP server updated": "LDAP-servern har uppdaterats", "Leaderboard": "Topplista", "Learn more about OpenAPI tool servers.": "<PERSON><PERSON><PERSON> mer om OpenAPI-verktygsservrar.", "Leave empty for no compression": "<PERSON><PERSON><PERSON><PERSON> tomt för ingen komprimering", "Leave empty for unlimited": "Lämna tomt för obegränsat", "Leave empty to include all models from \"{{url}}\" endpoint": "<PERSON><PERSON><PERSON><PERSON> tomt för att inkludera alla modeller från \"{{url}}\"-slutpunkten", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "<PERSON><PERSON><PERSON><PERSON> tomt för att inkludera alla modeller från \"{{url}}/api/tags\"-slutpunkten", "Leave empty to include all models from \"{{url}}/models\" endpoint": "<PERSON><PERSON><PERSON><PERSON> tomt för att inkludera alla modeller från \"{{url}}/models\"-slutpunkten", "Leave empty to include all models or select specific models": "<PERSON><PERSON><PERSON><PERSON> tomt för att inkludera alla modeller eller välj specifika modeller", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON><PERSON><PERSON> tomt för att använda standardprompten, eller ange en anpassad prompt", "Leave model field empty to use the default model.": "Lämna modellfältet tomt för att använda standardmodellen.", "License": "Licens", "Lift List": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Lyssnar...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLM:er kan göra misstag. Granska viktig information.", "Loader": "Inläsare", "Loading Kokoro.js...": "<PERSON><PERSON><PERSON> in Kokoro.js...", "Local": "<PERSON><PERSON>", "Local Task Model": "Lokal uppgiftsmodell", "Location access not allowed": "Åtkomst till platsen är inte tillåten", "Lost": "Förlorad", "LTR": "Vänster till höger", "Made by Open WebUI Community": "Skapad av OpenWebUI Community", "Make password visible in the user interface": "G<PERSON><PERSON> l<PERSON>ordet synligt i användargränssnittet", "Make sure to enclose them with": "Se till att bifoga dem med", "Make sure to export a workflow.json file as API format from ComfyUI.": "Se till att exportera en workflow.json-fil som API-format från ComfyUI.", "Manage": "Hantera", "Manage Direct Connections": "Hantera direkta anslutningar", "Manage Models": "Hantera modeller", "Manage Ollama": "<PERSON><PERSON>", "Manage Ollama API Connections": "Hantera Ollama API-anslutningar", "Manage OpenAI API Connections": "Hantera OpenAI API-anslutningar", "Manage Pipelines": "<PERSON><PERSON>", "Manage Tool Servers": "Hantera verktygsservrar", "March": "mars", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "", "Max Speakers": "<PERSON> antal talare", "Max Upload Count": "<PERSON> antal upp<PERSON><PERSON><PERSON>r", "Max Upload Size": "<PERSON>", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Högst 3 modeller kan laddas ner samtidigt. Vänligen försök igen senare.", "May": "maj", "Memories accessible by LLMs will be shown here.": "Minnen som är tillgängliga för LLM:er visas här.", "Memory": "<PERSON><PERSON>", "Memory added successfully": "Minnet har lagts till", "Memory cleared successfully": "Minnet har rensats", "Memory deleted successfully": "Minnet har tagits bort", "Memory updated successfully": "Minnet har uppdaterats", "Merge Responses": "Samman<PERSON><PERSON> svar", "Merged Response": "Sammanslaget svar", "Message rating should be enabled to use this feature": "Meddelandebetyg måste vara aktiverat för att använda den här funktionen", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Meddelanden du skickar efter att du har skapat din länk kommer inte att delas. Användare med URL:en kommer att kunna se den delade chatten.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (personligt)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (arbete/skola)", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "Mistral OCR API-nyckel krävs.", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Modellen '{{modelName}}' har laddats ner.", "Model '{{modelTag}}' is already in queue for downloading.": "Modellen '{{modelTag}}' är redan i kö för nedl<PERSON>dning.", "Model {{modelId}} not found": "Modell {{modelId}} hittades inte", "Model {{modelName}} is not vision capable": "Modellen {{modelName}} kan inte se", "Model {{name}} is now {{status}}": "<PERSON>len {{name}} är nu {{status}}", "Model {{name}} is now hidden": "<PERSON><PERSON> {{name}} är nu dold", "Model {{name}} is now visible": "<PERSON><PERSON> {{name}} är nu synlig", "Model accepts file inputs": "<PERSON>len accepterar <PERSON>", "Model accepts image inputs": "Modellen accepterar bi<PERSON>", "Model can execute code and perform calculations": "Modellen kan köra kod och utföra beräkningar", "Model can generate images based on text prompts": "<PERSON><PERSON> kan generera bilder baserat på textprompter", "Model can search the web for information": "<PERSON>len kan söka på webben efter information", "Model created successfully!": "<PERSON>len har skapats!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modellens filsystemväg hittades. Modellens kortnamn krävs för uppdate<PERSON>, kan inte fortsätta.", "Model Filtering": "Modellfiltrering", "Model ID": "Modell-ID", "Model IDs": "Modell-ID:n", "Model Name": "Modellnamn", "Model not selected": "Modell inte vald", "Model Params": "Modellparametrar", "Model Permissions": "Modellbehörigheter", "Model unloaded successfully": "<PERSON><PERSON> har avlastats", "Model updated successfully": "<PERSON><PERSON> har upp<PERSON><PERSON>s", "Model(s) do not support file upload": "Modellen/modellerna stöder inte filuppladdning", "Modelfile Content": "Model<PERSON><PERSON>s innehåll", "Models": "Modeller", "Models Access": "Modellåtkomst", "Models configuration saved successfully": "Modellkonfigurationen sparades", "Models Public Sharing": "Offentlig delning av modeller", "Mojeek Search API Key": "Mojeek Sök API-nyckel", "more": "mer", "More": "<PERSON><PERSON>", "Name": "<PERSON><PERSON>", "Name your knowledge base": "Namnge din kunskapsbas", "Native": "Inbyggd", "New Chat": "<PERSON><PERSON> chatt", "New Folder": "Ny mapp", "New Function": "<PERSON><PERSON> <PERSON>tion", "New Note": "<PERSON><PERSON> anteckning", "New Password": "Nytt lösenord", "New Tool": "Nytt verktyg", "new-channel": "ny-kanal", "Next message": "Nästa meddelande", "No chats found": "", "No chats found for this user.": "Inga chattar hittades för den här användaren.", "No chats found.": "Inga chattar hittades.", "No content": "<PERSON><PERSON> inn<PERSON>", "No content found": "Inget innehåll hittades", "No content found in file.": "Inget innehåll hittades i filen.", "No content to speak": "Inget innehåll att tala", "No distance available": "Inget avstånd tillg<PERSON>gt", "No feedbacks found": "Ingen feedback hittades", "No file selected": "Ingen fil vald", "No groups with access, add a group to grant access": "Inga grupper med åtkomst, lägg till en grupp för att ge åtkomst", "No HTML, CSS, or JavaScript content found.": "Inget HTML-, CSS- eller JavaScript-innehåll hittades.", "No inference engine with management support found": "Ingen inferensmotor med stöd för hantering hittades", "No knowledge found": "Ingen kunskap hittades", "No memories to clear": "<PERSON>ga minnen att rensa", "No model IDs": "Inga modell-ID:n", "No models found": "Inga modeller hittades", "No models selected": "Inga modeller valda", "No Notes": "Inga anteckningar", "No results found": "Inga resultat hittades", "No search query generated": "<PERSON><PERSON> sökfrå<PERSON> genererad", "No source available": "Ingen tillgänglig källa", "No users were found.": "Inga användare hittades.", "No valves to update": "Inga ventiler att uppdatera", "None": "Ingen", "Not factually correct": "Inte faktiskt korrekt", "Not helpful": "Inte hjälpsam", "Note deleted successfully": "Anteckningen raderades", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Obs: Om du anger en tröskel kommer sökningen endast att returnera dokument med ett betyg som är större än eller lika med tröskeln.", "Notes": "Anteckningar", "Notification Sound": "Aviseringsljud", "Notification Webhook": "Aviserings-Webhook", "Notifications": "Notifikationer", "November": "november", "OAuth ID": "OAuth ID", "October": "oktober", "Off": "Av", "Okay, Let's Go!": "<PERSON><PERSON>, nu kör vi!", "OLED Dark": "<PERSON><PERSON><PERSON> (OLED)", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API-inställningar uppdaterade", "Ollama Version": "Ollama-version", "On": "På", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "Endast aktivt när inställningen \"Klistra in stor text som fil\" är aktiverad.", "Only active when the chat input is in focus and an LLM is generating a response.": "Endast aktivt när chattinmatningen är i fokus och en LLM genererar ett svar.", "Only alphanumeric characters and hyphens are allowed": "Endast alfanumeriska tecken och bindestreck är tillåtna", "Only alphanumeric characters and hyphens are allowed in the command string.": "Endast alfanumeriska tecken och bindestreck är tillåtna i kommandosträngen.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Endast samlingar kan redigeras, skapa en ny kunskapsbas för att redigera/lägga till dokument.", "Only markdown files are allowed": "Endast markdown-filer <PERSON><PERSON> <PERSON><PERSON>", "Only select users and groups with permission can access": "Endast valda användare och grupper med behörighet kan komma åt", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hoppsan! Det ser ut som om URL:en är ogiltig. Dubbelkolla gärna och försök igen.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hoppsan! Det finns fortfarande filer som laddas upp. Vä<PERSON> tills uppladdningen är klar.", "Oops! There was an error in the previous response.": "Hoppsan! Det uppstod ett fel i föregående svar.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hoppsan! Du använder en ej stödd metod (endast frontend). Vänligen servera WebUI från backend.", "Open file": "Öppna fil", "Open in full screen": "Öppna i helskärm", "Open modal to configure connection": "Öppna modal för att konfigurera anslutning", "Open new chat": "Öppna ny chatt", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI kan använda verktyg från alla OpenAPI-servrar.", "Open WebUI uses faster-whisper internally.": "Open WebUI använder faster-whisper internt.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI använder SpeechT5 och CMU Arctic högtalarinbäddningar.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI-versionen (v{{OPEN_WEBUI_VERSION}}) är lägre än den version som krävs (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API-konfig", "OpenAI API Key is required.": "OpenAI API-nyckel krävs.", "OpenAI API settings updated": "OpenAI API-inställningar uppdaterade", "OpenAI URL/Key required.": "OpenAI-URL/nyckel krävs.", "openapi.json URL or Path": "openapi.json URL eller sökväg", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "Alternativ för att köra en lokal syn-språkmodell i bildbeskrivningen. Parametrarna hänvisar till en modell som finns på Hugging Face. Denna parameter utesluter picture_description_api.", "or": "eller", "Ordered List": "", "Organize your users": "Organisera dina användare", "Other": "<PERSON><PERSON>", "OUTPUT": "UTDATA", "Output format": "Utdataformat", "Output Format": "Utdataformat", "Overview": "Översikt", "page": "sida", "Paginate": "Sidnumrera", "Parameters": "Parametrar", "Password": "L<PERSON>senord", "Paste Large Text as File": "<PERSON><PERSON>ra in stor text som fil", "PDF document (.pdf)": "PDF-dokument (.pdf)", "PDF Extract Images (OCR)": "PDF <PERSON><PERSON><PERSON> bilder (OCR)", "pending": "väntande", "Pending": "Väntande", "Pending User Overlay Content": "Väntande användaröverlagringsinnehåll", "Pending User Overlay Title": "Väntande användaröverlagringstitel", "Permission denied when accessing media devices": "Nekad behörighet vid åtkomst till mediaenheter", "Permission denied when accessing microphone": "Nekad behörighet vid åtkomst till mikrofon", "Permission denied when accessing microphone: {{error}}": "Tillstånd nekades vid åtkomst till mikrofon: {{error}}", "Permissions": "Behörigheter", "Perplexity API Key": "Perplexity API-nyckel", "Perplexity Model": "Perplexity-modell", "Perplexity Search Context Usage": "Perplexity Sök Kontextanvändning", "Personalization": "Personalisering", "Picture Description API Config": "Bildbeskrivning API-konfig", "Picture Description Local Config": "Bildbeskrivning Lokal konfig", "Picture Description Mode": "Bildbeskrivningsläge", "Pin": "<PERSON><PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON>", "Pioneer insights": "Pionjärinsikter", "Pipe": "", "Pipeline deleted successfully": "Rörledningen har tagits bort", "Pipeline downloaded successfully": "Rörledningen har laddats ner", "Pipelines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pipelines Not Detected": "Inga rörledningar hittades", "Pipelines Valves": "<PERSON><PERSON><PERSON> f<PERSON><PERSON>", "Plain text (.md)": "Ren text (.md)", "Plain text (.txt)": "Text (.txt)", "Playground": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Playwright Timeout (ms)": "Playwright Timeout (ms)", "Playwright WebSocket URL": "Playwright WebSocket URL", "Please carefully review the following warnings:": "Vänligen granska följande varningar noggrant:", "Please do not close the settings page while loading the model.": "Stäng inte inställningssidan medan modellen läses in.", "Please enter a prompt": "Vänligen ange en uppmaning", "Please enter a valid path": "Vänligen ange en giltig sökväg", "Please enter a valid URL": "Vänligen ange en giltig URL", "Please fill in all fields.": "Vänligen fyll i alla fält.", "Please select a model first.": "Vänligen välj en modell först.", "Please select a model.": "Vänligen välj en modell.", "Please select a reason": "Vänligen välj en anledning", "Port": "Port", "Positive attitude": "Positivt inställning", "Prefix ID": "Prefix ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix ID används för att undvika konflikter med andra anslutningar genom att lägga till ett prefix till modell-ID:n - lämna tomt för att inaktivera", "Prevent file creation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Preview": "Förhandsgranska", "Previous 30 days": "Föregående 30 dagar", "Previous 7 days": "Föregående 7 dagar", "Previous message": "Föregående meddelande", "Private": "Privat", "Profile Image": "Profilbild", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Instruktion (t.ex. Berätta en kuriosa om Romerska Imperiet)", "Prompt Autocompletion": "Automatisk komplettering av prompter", "Prompt Content": "Instruktionens innehåll", "Prompt created successfully": "Prompt skapad", "Prompt suggestions": "Instruktionsförslag", "Prompt updated successfully": "Prompt uppdaterad", "Prompts": "Instruktioner", "Prompts Access": "Promptåtkomst", "Prompts Public Sharing": "Offentlig delning av prompter", "Public": "Offentlig", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON>dda ner \"{{searchValue}}\" från <PERSON>.com", "Pull a model from Ollama.com": "Ladda ner en modell från Ollama.com", "Query Generation Prompt": "Prompt för fr<PERSON>generering", "RAG Template": "RAG-mall", "Rating": "Betyg", "Re-rank models by topic similarity": "Ranka om modeller efter ämneslikhet", "Read": "<PERSON><PERSON><PERSON>", "Read Aloud": "<PERSON><PERSON><PERSON>", "Reason": "Anledning", "Reasoning Effort": "Resonemangsinsats", "Record": "Spela in", "Record voice": "Spela in röst", "Redirecting you to Open WebUI Community": "Omdirigerar dig till OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Minskar sannolikheten för att generera nonsens. Ett högre värde (t.ex. 100) ger mer varierande svar, medan ett lägre värde (t.ex. 10) är mer konservativt.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Referera till dig själv som \"Användare\" (t.ex. \"Användaren lär sig <PERSON>ka\")", "References from": "Refer<PERSON><PERSON> från", "Refused when it shouldn't have": "Avvisades när det inte borde ha gjort det", "Regenerate": "Regenerera", "Reindex": "Indexera om", "Reindex Knowledge Base Vectors": "Indexera om kunskapsbasvektorer", "Release Notes": "Versionsinformation", "Releases": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Relevance": "<PERSON><PERSON><PERSON>", "Relevance Threshold": "Relevanstr<PERSON><PERSON>l", "Remember Dismissal": "", "Remove": "<PERSON> bort", "Remove {{MODELID}} from list.": "<PERSON> bort {{MODELID}} f<PERSON><PERSON><PERSON> listan.", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Ta bort modell", "Remove this tag from list": "Ta bort denna tagg från listan", "Rename": "Byt namn", "Reorder Models": "Omordna modeller", "Reply in Thread": "<PERSON>var<PERSON> i tråd", "Reranking Engine": "<PERSON><PERSON><PERSON>ning<PERSON><PERSON>", "Reranking Model": "Reranking modell", "Reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reset All Models": "<PERSON><PERSON>täll alla modeller", "Reset Upload Directory": "Återställ uppladdningskatalog", "Reset Vector Storage/Knowledge": "Återställ vektorlagring/kunskap", "Reset view": "Återställ vy", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Svarsaviseringar kan inte aktiveras eftersom webbplatsens behörigheter har nekats. Besök dina webbläsarinställningar för att ge nödvändig åtkomst.", "Response splitting": "Svarsdelning", "Response Watermark": "Svarsvattenstämpel", "Result": "Resultat", "Retrieval": "Hämtning", "Retrieval Query Generation": "Generering av hämtningsfrågor", "Rich Text Input for Chat": "Rich Text-in<PERSON><PERSON> för chatt", "RK": "RK", "Role": "Roll", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON>", "Save": "Spara", "Save & Create": "Spara och skapa", "Save & Update": "Spara och uppdatera", "Save As Copy": "Spara som kopia", "Save Tag": "Spara tagg", "Saved": "Sparad", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Att spara chatloggar direkt till din webbläsares lagring stöds inte längre. Ta en stund och ladda ner och radera dina chattloggar genom att klicka på knappen nedan. Oroa dig inte, du kan enkelt importera dina chattloggar till backend genom", "Scroll On Branch Change": "Scrolla vid grenbyte", "Search": "<PERSON>ö<PERSON>", "Search a model": "<PERSON><PERSON><PERSON> efter en modell", "Search Base": "Sökbas", "Search Chats": "Sök i chattar", "Search Collection": "<PERSON><PERSON><PERSON>", "Search Filters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search for tags": "s<PERSON><PERSON> efter taggar", "Search Functions": "<PERSON><PERSON><PERSON>", "Search Knowledge": "Sök k<PERSON>", "Search Models": "<PERSON><PERSON><PERSON> modeller", "Search Notes": "", "Search options": "Sökalternativ", "Search Prompts": "Sök instruktioner", "Search Result Count": "<PERSON><PERSON>", "Search the internet": "Sök på internet", "Search Tools": "Sökverktyg", "SearchApi API Key": "SearchApi API-nyckel", "SearchApi Engine": "SearchApi-motor", "Searched {{count}} sites": "<PERSON><PERSON><PERSON><PERSON> på {{count}} webbplatser", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> kunskap efter \"{{searchQuery}}\"", "Searching the web...": "Söker på webben...", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "Se readme.md för instruktioner", "See what's new": "Se vad som är nytt", "Seed": "Seed", "Select a base model": "Välj en basmodell", "Select a conversation to preview": "", "Select a engine": "Välj en motor", "Select a function": "Välj en funktion", "Select a group": "Välj en grupp", "Select a model": "<PERSON><PERSON><PERSON><PERSON> en modell", "Select a pipeline": "Välj en rörledning", "Select a pipeline url": "Välj en URL för rörledningen", "Select a tool": "Välj ett verktyg", "Select an auth method": "Välj en autentiseringsmetod", "Select an Ollama instance": "Välj en Ollama-instans", "Select Engine": "Välj motor", "Select Knowledge": "V<PERSON><PERSON>j <PERSON>", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> endast en modell att ringa", "Selected model(s) do not support image inputs": "Valda modeller stöder inte bildinmatningar", "Semantic distance to query": "Semantiskt avstånd till fråga", "Send": "<PERSON><PERSON><PERSON>", "Send a Message": "Skicka ett meddelande", "Send message": "<PERSON><PERSON><PERSON> medd<PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Skickar `stream_options: { include_usage: true }` i begäran.\nLeverantörer som stöds returnerar information om tokenanvändning i svaret när det är inställt.", "September": "september", "SerpApi API Key": "SerpApi API-nyckel", "SerpApi Engine": "SerpApi-motor", "Serper API Key": "Serper API-nyckel", "Serply API Key": "Serply API-nyckel", "Serpstack API Key": "Serpstack API-nyckel", "Server connection verified": "Serveranslutning verifierad", "Set as default": "Ange som standard", "Set CFG Scale": "Ställ in CFG-skala", "Set Default Model": "<PERSON><PERSON>", "Set embedding model": "<PERSON><PERSON><PERSON> in embedding modell", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON> in embedding modell (t.ex. {{model}})", "Set Image Size": "<PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON> in reranking modell (t.ex. {{model}})", "Set Sampler": "Ställ in Sampler", "Set Scheduler": "<PERSON><PERSON>ll in Scheduler", "Set Steps": "<PERSON><PERSON> steg", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Ange antalet lager som ska avlastas till GPU. Att öka detta värde kan avsevärt förbättra prestandan för modeller som är optimerade för GPU-acceleration, men kan ocks<PERSON> förbruka mer ström och GPU-resurser.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Ange antalet arbetstrådar som används för beräkning. Detta alternativ styr hur många trådar som används för att bearbeta inkommande förfrågningar samtidigt. Att öka detta värde kan förbättra prestandan under <PERSON><PERSON><PERSON>, men kan också förbruka mer CPU-resurser.", "Set Voice": "<PERSON><PERSON>", "Set whisper model": "<PERSON><PERSON> v<PERSON>", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Anger en platt bias mot tokens som har dykt upp minst en gång. Ett högre värde (t.ex. 1,5) kommer att straffa upprepningar hårdare, medan ett lägre värde (t.ex. 0,9) kommer att vara mer förlåtande. Vid 0 är det inaktiverat.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Anger en skalningsbias mot tokens för att straffa upprepningar, baserat på hur många gånger de har dykt upp. Ett högre värde (t.ex. 1,5) kommer att straffa upprepningar hårdare, medan ett lägre värde (t.ex. 0,9) kommer att vara mer förlåtande. Vid 0 är det inaktiverat.", "Sets how far back for the model to look back to prevent repetition.": "Anger hur långt tillbaka modellen ska se tillbaka för att förhindra upprepning.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Anger det slumpmässiga numret som ska användas för generering. <PERSON><PERSON> du ställer in detta på ett specifikt nummer kommer modellen att generera samma text för samma uppmaning.", "Sets the size of the context window used to generate the next token.": "Anger storleken på kontextfönstret som används för att generera nästa token.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Anger de stoppsekvenser som ska användas. <PERSON>är detta mönster påträffas kommer LLM att sluta generera text och returnera. Flera stoppsekvenser kan ställas in genom att ange flera separata stoppparametrar i en modelfil.", "Settings": "Inställningar", "Settings saved successfully!": "Inställningar sparades framgångsrikt!", "Share": "Dela", "Share Chat": "<PERSON><PERSON> chatt", "Share to Open WebUI Community": "Dela till OpenWebUI Community", "Sharing Permissions": "Delningsbehörigheter", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "Genvägar med en asterisk (*) är situationsbundna och endast aktiva under specifika förhållanden.", "Show": "Visa", "Show \"What's New\" modal on login": "Visa \"Vad är nytt\"-modalen vid inloggning", "Show Admin Details in Account Pending Overlay": "Visa administratörsinformation till väntande konton", "Show All": "Visa alla", "Show image preview": "", "Show Less": "Visa mindre", "Show Model": "Visa modell", "Show shortcuts": "Visa genvägar", "Show your support!": "Visa ditt stöd!", "Showcased creativity": "Visade kreativitet", "Sign in": "Logga in", "Sign in to {{WEBUI_NAME}}": "Logga in på {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Logga in på {{WEBUI_NAME}} med LDAP", "Sign Out": "Logga ut", "Sign up": "Registrera dig", "Sign up to {{WEBUI_NAME}}": "Registrera dig på {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "Förbättrar noggrannheten avsevärt genom att använda en LLM för att förbättra tabeller, formulär, inline-matematik och layoutdetektering. Ökar latensen. Standardvärdet är True.", "Signing in to {{WEBUI_NAME}}": "Loggar in på {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "Hoppa över cache", "Skip the cache and re-run the inference. Defaults to False.": "Hoppa över cacheminnet och kör inferensen igen. Standardvärdet är False.", "Sougou Search API sID": "Sougou Sök API sID", "Sougou Search API SK": "Sougou Sök API SK", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "Uppspelningshastighet för tal", "Speech recognition error: {{error}}": "Fel vid taligenkänning: {{error}}", "Speech-to-Text": "Tal-till-text", "Speech-to-Text Engine": "Tal-till-text-motor", "Stop": "Stopp", "Stop Generating": "<PERSON>luta generera", "Stop Sequence": "Stoppsekvens", "Stream Chat Response": "<PERSON><PERSON><PERSON>", "Strikethrough": "", "Strip Existing OCR": "Ta bort befintlig OCR", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Ta bort befintlig OCR-text från PDF:en och kör OCR igen. Ignoreras om Force OCR är aktiverat. Standardvärdet är False.", "STT Model": "Tal-till-text-modell", "STT Settings": "Tal-till-text-inställningar", "Stylized PDF Export": "Stiliserad PDF-export", "Subtitle (e.g. about the Roman Empire)": "Undertext (t.ex. om Romerska Imperiet)", "Success": "Framgång", "Successfully updated.": "Uppdaterades framgångsrikt.", "Suggested": "Föreslagen", "Support": "<PERSON><PERSON><PERSON>", "Support this plugin:": "<PERSON><PERSON><PERSON> denna plugin", "Supported MIME Types": "MIME-typer som stöds", "Sync directory": "Synkronisera katalog", "System": "System", "System Instructions": "Systeminstruktioner", "System Prompt": "Systeminstruktion", "Tags": "Taggar", "Tags Generation": "Tagggenerering", "Tags Generation Prompt": "Prompt för <PERSON>", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Svansfri sampling används för att minska effekten av mindre sannolika tokens från utdata. Ett högre värde (t.ex. 2,0) kommer att minska effekten mer, medan ett värde på 1,0 inaktiverar denna inställning.", "Talk to model": "Prata med modellen", "Tap to interrupt": "<PERSON><PERSON> för att avbryta", "Task List": "", "Task Model": "Uppgiftsmodell", "Tasks": "Uppgifter", "Tavily API Key": "Tavily API-nyckel", "Tavily Extract Depth": "<PERSON><PERSON>", "Tell us more:": "<PERSON><PERSON><PERSON><PERSON> mer:", "Temperature": "Temperatur", "Temporary Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> chatt", "Text Splitter": "Textdelare", "Text-to-Speech": "Text-till-tal", "Text-to-Speech Engine": "Text-till-tal-motor", "Thanks for your feedback!": "Tack för din feedback!", "The Application Account DN you bind with for search": "Applikationskontots DN du binder med för sökning", "The base to search for users": "Basen för att söka efter användare", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "Batchstorleken avgör hur många textförfrågningar som bearbetas tillsammans samtidigt. En högre batchstorlek kan öka modellens prestanda och hastighet, men det kräver också mer minne.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Utvecklarna bakom denna plugin är passionerade volontärer från communityn. Om du tycker att denna plugin är hj<PERSON>lpsam, vänligen överväg att bidra till dess utveckling.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Utvärderingens topplista är baserad på Elo-betygssystemet och uppdateras i realtid", "The format to return a response in. Format can be json or a JSON schema.": "Formatet för att returnera ett svar i. Formatet kan vara json eller ett JSON-schema.", "The height in pixels to compress images to. Leave empty for no compression.": "Höjden i pixlar för att komprimera bilder till. Lämna tomt för ingen komprimering.", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "Språket för ljudinmatningen. Att ange ingångsspråket i ISO-639-1-format (t.ex. en) förbättrar noggrannheten och latensen. Lämna tomt för att automatiskt identifiera språket.", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP-attributet som mappar till e-postmeddelandet som användarna använder för att logga in.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP-attributet som mappar till användarnamnet som användarna använder för att logga in.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Topplistan är för nä<PERSON>rand<PERSON> i beta, och vi kan justera betygsberäkningarna när vi förfinar algoritmen.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Den maximala filstorleken i MB. Om filstorleken överskrider denna gräns kommer filen inte att laddas upp.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Det maximala antalet filer som kan användas samtidigt i chatten. Om antalet filer överskrider denna gräns kommer filerna inte att laddas upp.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Utdataformatet för texten. Kan vara 'json', 'markdown' eller 'html'. Standardvärdet är 'markdown'.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Betyget ska vara ett värde mellan 0.0 (0%) och 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Modellens temperatur. Att öka temperaturen gör att modellen svarar mer kreativt.", "The width in pixels to compress images to. Leave empty for no compression.": "Bredden i pixlar för att komprimera bilder till. Lämna tomt för ingen komprimering.", "Theme": "<PERSON><PERSON>", "Thinking...": "Tänker...", "This action cannot be undone. Do you wish to continue?": "<PERSON>na <PERSON>rd kan inte ång<PERSON>. Vill du fortsätta?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Den här kanalen skapades den {{createdAt}}. Detta är den allra första början av kanalen {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Den här chatten visas inte i historiken och dina meddelanden sparas inte.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> s<PERSON>äller att dina värdefulla samtal sparas säkert till din backend-databas. Tack!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Detta är en experimentell funktion som kanske inte fungerar som förväntat och som kan komma att ändras när som helst.", "This model is not publicly available. Please select another model.": "Den här modellen är inte tillgänglig för allmänheten. Välj en annan modell.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "Det här alternativet styr hur länge modellen ska vara inläst i minnet efter begäran (standard: 5m)", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Det här alternativet styr hur många tokens som bevaras när kontexten uppdateras. Om det till exempel är inställt på 2 behålls de två sista tokens i samtalskontexten. Att bevara kontexten kan bidra till att upprätthålla kontinuiteten i ett samtal, men det kan minska förmågan att svara på nya ämnen.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "Det här alternativet aktiverar eller inaktiverar användningen av resonemangsfunktionen i Ollama, vilket gör att modellen kan tänka efter innan den genererar ett svar. När det är aktiverat kan modellen ta en stund att bearbeta samtalskontexten och generera ett mer genomtänkt svar.", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Det här alternativet anger det maximala antalet tokens som modellen kan generera i sitt svar. Om du ökar den här gränsen kan modellen ge längre svar, men det kan också öka sannolikheten för att det genereras innehåll som inte är till hj<PERSON><PERSON><PERSON> eller irrelevant.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Detta alternativ tar bort alla befintliga filer i samlingen och ersätter dem med nyligen uppladdade filer.", "This response was generated by \"{{model}}\"": "<PERSON> här svaret genererades av \"{{model}}\"", "This will delete": "<PERSON><PERSON> kommer att radera", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON> kommer att radera <strong>{{NAME}}</strong> och <strong>allt dess innehåll</strong>.", "This will delete all models including custom models": "<PERSON><PERSON> kommer att radera alla modeller inklusive anpassade modeller", "This will delete all models including custom models and cannot be undone.": "<PERSON><PERSON> kommer att radera alla modeller inklusive anpassade modeller och kan inte ångras.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON>ta kommer att återställa kunskapsbasen och synkronisera alla filer. Vill du fortsätta?", "Thorough explanation": "<PERSON><PERSON><PERSON><PERSON>", "Thought for {{DURATION}}": "Tänkte i {{DURATION}}", "Thought for {{DURATION}} seconds": "Tänkte i {{DURATION}} sekunder", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika Server URL krävs.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tips: Upp<PERSON>ra fler variabler genom att trycka på tabb-tangenten i chattinmatningen efter varje ersättning.", "Title": "Titel", "Title (e.g. Tell me a fun fact)": "Titel (t.ex. Berätta en kuriosa)", "Title Auto-Generation": "Automatisk generering av titel", "Title cannot be an empty string.": "Titeln får inte vara en tom sträng.", "Title Generation": "T<PERSON><PERSON>ner<PERSON>", "Title Generation Prompt": "Instruktion för tite<PERSON>", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON><PERSON><PERSON> att komma åt de tillgängliga modellnamnen för nedladdning,", "To access the GGUF models available for downloading,": "<PERSON><PERSON><PERSON> att komma åt de GGUF-modellerna som finns tillgängliga för nedladdning,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON><PERSON><PERSON> att få tillgång till WebUI, kontakta administratören. Administratörer kan hantera behörigheter från administrationspanelen.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "<PERSON><PERSON><PERSON> att bifoga kunskapsbas här, lägg till dem i arbetsytan \"Kunskap\" först.", "To learn more about available endpoints, visit our documentation.": "<PERSON><PERSON><PERSON> att lära dig mer om tillgängliga endpoints, besök vår dokumentation", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "<PERSON><PERSON><PERSON> att skydda din integritet delas endast betyg, modell-ID:n, taggar och metadata från din feedback - dina chattloggar förblir privata och skickas inte med.", "To select actions here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON><PERSON> att välja å<PERSON>gä<PERSON> här, lägg till dem i arbetsytan \"Funktioner\" först.", "To select filters here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON><PERSON> att välja filter här, lägg till dem i arbetsytan \"Funktioner\" först.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Om du vill välja verktygslådor här måste du först lägga till dem i arbetsytan \"Verktyg\".", "Toast notifications for new updates": "Toast-aviseringar för nya uppdateringar", "Today": "<PERSON><PERSON>", "Toggle search": "<PERSON><PERSON><PERSON><PERSON>", "Toggle settings": "Växla inställningar", "Toggle sidebar": "<PERSON><PERSON><PERSON><PERSON>", "Toggle whether current connection is active.": "Växla om aktuell anslutning är aktiv.", "Token": "Token", "Too verbose": "<PERSON><PERSON><PERSON><PERSON>", "Tool created successfully": "Verktyget skapades framgångsrikt", "Tool deleted successfully": "Verktyg borttaget framgångsrikt", "Tool Description": "Beskrivning av verktyget", "Tool ID": "Verktygs-ID", "Tool imported successfully": "Verktyget importerades framgångsrikt", "Tool Name": "Verktygets namn", "Tool Servers": "Verktygsservrar", "Tool updated successfully": "Verktyget uppdaterades framgångsrikt", "Tools": "Verktyg", "Tools Access": "Verktygsåtkomst", "Tools are a function calling system with arbitrary code execution": "Verktyg är ett funktionsanropssystem med godtycklig kodkörning", "Tools Function Calling Prompt": "Prompt för anrop av verktygsfunktion:", "Tools have a function calling system that allows arbitrary code execution.": "Verktyg har ett funktionsanropssystem som tillåter godtycklig kodkörning", "Tools Public Sharing": "Offentlig delning av verktyg", "Top K": "Topp <PERSON>", "Top K Reranker": "<PERSON>p <PERSON>", "Transformers": "Transformatorer", "Trouble accessing Ollama?": "Problem med att komma åt Ollama?", "Trust Proxy Environment": "Trust Proxy-miljö", "TTS Model": "Text-till-tal-modell", "TTS Settings": "Text-till-tal-inställningar", "TTS Voice": "Text-till-tal-r<PERSON>st", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON> Hugging Face Resolve (Download) URL", "Uh-oh! There was an issue with the response.": "Oj då! Det uppstod ett problem med svaret", "UI": "UI", "Unarchive All": "Avarkivera alla", "Unarchive All Archived Chats": "Avarkivera alla arkiverade chattar", "Unarchive Chat": "<PERSON><PERSON><PERSON><PERSON> chatt", "Underline": "", "Unloads {{FROM_NOW}}": "Avlastar {{FROM_NOW}}", "Unlock mysteries": "<PERSON><PERSON><PERSON> upp mysterier", "Unpin": "Ta bort fästning", "Unravel secrets": "Avslöja <PERSON>", "Unsupported file type.": "", "Untagged": "Otaggad", "Untitled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update": "Uppdatera", "Update and Copy Link": "Uppdatera och kopiera länk", "Update for the latest features and improvements.": "Uppdatera för att få de senaste funktionerna och förbättringarna.", "Update password": "Uppdatera lösenord", "Updated": "Uppdaterad", "Updated at": "Uppdaterad vid", "Updated At": "Uppdaterad vid", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Uppgradera till en licensierad plan för utökade funktioner, inklusive anpassade teman och varumärken, och dedikerad support.", "Upload": "Ladda upp", "Upload a GGUF model": "Ladda upp en GGUF-modell", "Upload Audio": "<PERSON>dda upp ljud", "Upload directory": "Uppladdningskatalog", "Upload files": "Ladda upp filer", "Upload Files": "Ladda upp filer", "Upload Pipeline": "Ladda upp rörledning", "Upload Progress": "Uppladdningsframsteg", "URL": "URL", "URL Mode": "URL-läge", "Usage": "Anv<PERSON><PERSON><PERSON>", "Use '#' in the prompt input to load and include your knowledge.": "Använd '#' i prompten för att läsa in och inkludera din kunskap", "Use Gravatar": "<PERSON><PERSON><PERSON><PERSON> Gravatar", "Use groups to group your users and assign permissions.": "Använd grupper för att gruppera dina användare och tilldela behörigheter.", "Use Initials": "<PERSON><PERSON><PERSON><PERSON> <PERSON>er", "Use LLM": "Använd LLM", "Use no proxy to fetch page contents.": "<PERSON><PERSON><PERSON>nd ingen proxy för att hämta si<PERSON>.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Använd proxy som anges av miljövariablerna http_proxy och https_proxy för att hämta sidin<PERSON>ll.", "user": "anvä<PERSON><PERSON>", "User": "Användare", "User location successfully retrieved.": "Användarens plats har hämtats", "User menu": "", "User Webhooks": "Användar-webhooks", "Username": "Användarnamn", "Users": "Användare", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Använda standardmodellen för arenan med alla modeller. Klicka på plusknappen för att lägga till anpassade modeller", "Valid time units:": "Giltiga tidsenheter:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "Ventiler uppdaterade", "Valves updated successfully": "Ventiler uppdaterade", "variable": "variabel", "Verify Connection": "Verifiera anslutning", "Verify SSL Certificate": "Verifiera SSL-certifikat", "Version": "Version", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} av {{totalVersions}}", "View Replies": "<PERSON> svar", "View Result from **{{NAME}}**": "Visa resultat från **{{NAME}}**", "Visibility": "Synlighet", "Vision": "Syn", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Voice mode": "Röstläge", "Warning": "Varning", "Warning:": "Varning:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Varning för detta: Om du aktiverar detta kan användare ladda upp godtycklig kod på servern.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Varning: <PERSON><PERSON> du uppdaterar eller ä<PERSON>r din embedding modell måste du importera alla dokument igen.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Varning: Jupyter-exekvering möjliggör godtycklig kodkörning, vilket innebär allvarliga säkerhetsrisker - fortsätt med extrem försiktighet", "Web": "<PERSON>", "Web API": "Webb-API", "Web Loader Engine": "Webbladdarmotor", "Web Search": "Webbsökning", "Web Search Engine": "Sökmotor", "Web Search in Chat": "Webbsökning i chatten", "Web Search Query Generation": "Generering av webbsökningsfrågor", "Webhook URL": "Webhook-URL", "WebUI Settings": "WebUI-inställningar", "WebUI URL": "WebUI-URL", "WebUI will make requests to \"{{url}}\"": "WebUI kommer att göra förfrågningar till \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI kommer att göra förfrågningar till \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI kommer att göra förfrågningar till \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "Vikt av BM25-hämtning", "What are you trying to achieve?": "Vad försöker du uppnå?", "What are you working on?": "Var arbetar du med?", "What's New in": "Vad är nytt i", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON>r det här läget är aktiverat svarar modellen på varje chattmeddelande i realtid och genererar ett svar så snart användaren skickar ett meddelande. Det här läget är användbart för livechattar, men kan påverka prestandan på långsammare maskinvara.", "wherever you are": "var du än befinner dig", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Om utdata ska sidnumreras. Varje sida kommer att separeras av en horisontell linje och sidnummer. Standardvärdet är False.", "Whisper (Local)": "Whisper (lokal)", "Why?": "Varför?", "Widescreen Mode": "Bredbildsläge", "Won": "<PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "<PERSON>gerar tillsammans med top-k. <PERSON><PERSON> högre värde (t.ex. 0,95) leder till mer varierande text, medan ett lägre värde (t.ex. 0,5) genererar mer fokuserad och konservativ text.", "Workspace": "A<PERSON><PERSON><PERSON><PERSON>", "Workspace Permissions": "Arbetsytebehörigheter", "Write": "Skriv", "Write a prompt suggestion (e.g. Who are you?)": "Skriv ett instruktionsförslag (t.ex. Vem är du?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Skriv en sammanfattning på 50 ord som sammanfattar [ämne eller ny<PERSON>].", "Write something...": "Skriv någonting...", "Yacy Instance URL": "Yacy-instans-URL", "Yacy Password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Yacy Username": "Yacy-användarnamn", "Yesterday": "Igår", "You": "Dig", "You are currently using a trial license. Please contact support to upgrade your license.": "Du använder för närvarande en testlicens. Kontakta support för att uppgradera din licens.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Du kan endast chatta med maximalt {{maxCount}} fil(er) på samma gång", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Du kan anpassa dina interaktioner med stora språkmodeller genom att lägga till minnen via knappen '<PERSON><PERSON>' nedan, så att de blir mer användbara och skräddarsydda för dig.", "You cannot upload an empty file.": "Du kan inte ladda upp en tom fil.", "You do not have permission to upload files.": "Du har inte behörighet att ladda upp filer.", "You have no archived conversations.": "Du har inga arkiverade samtal.", "You have shared this chat": "Du har delat denna chatt", "You're a helpful assistant.": "Du är en hjälpsam assistent.", "You're now logged in.": "Du är nu inloggad.", "Your account status is currently pending activation.": "Ditt konto väntar på att bli aktiverat", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON> ditt bidrag går direkt till pluginutvecklaren; Open WebUI tar ingen procentandel. Däremot kan den valda finansieringsplattformen ha egna avgifter.", "Youtube": "Youtube", "Youtube Language": "Youtube-språk", "Youtube Proxy URL": "Youtube Proxy URL"}