{"-1 for no limit, or a positive integer for a specific limit": "-1 per nessun limite, o un numero intero positivo per un limite specifico", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' o '-1' per nessuna scadenza.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(p.e. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(p.e. `sh webui.sh --api`)", "(latest)": "(ultima)", "(leave blank for to use commercial endpoint)": "(lascia vuoto per utilizzare l'endpoint commerciale)", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ modelli }}", "{{COUNT}} Available Tools": "{{COUNT}} Strumenti Disponibili", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} righe nascoste", "{{COUNT}} Replies": "{{COUNT}} Risposte", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}} Chat", "{{webUIName}} Backend Required": "{{webUIName}} Richiesta Backend", "*Prompt node ID(s) are required for image generation": "*ID nodo prompt sono necessari per la generazione di immagini", "A new version (v{{LATEST_VERSION}}) is now available.": "Una nuova versione (v{{LATEST_VERSION}}) è ora disponibile.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un task model viene utilizzato durante l'esecuzione di attività come la generazione di titoli per chat e query di ricerca Web", "a user": "un utente", "About": "Informazioni", "Accept autocomplete generation / Jump to prompt variable": "Accetta l'auto generazione di completamento / Passa alla variabile del prompt", "Access": "Accesso", "Access Control": "Controllo accessi", "Accessible to all users": "Accessibile a tutti gli utenti", "Account": "Account", "Account Activation Pending": "Account in attesa di attivazione", "Accurate information": "Informazioni accurate", "Action": "", "Actions": "Azioni", "Activate": "<PERSON><PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Attiva questo comando digitando \"/{{COMMAND}}\" nell'input della chat.", "Active Users": "<PERSON><PERSON><PERSON> attivi", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Aggiungi un ID modello", "Add a short description about what this model does": "Aggiungi una breve descrizione di quello che fa questo modello", "Add a tag": "Aggiungi un tag", "Add Arena Model": "Aggiungi modello Arena", "Add Connection": "Aggiungi connessione", "Add Content": "Aggiungi contenuto", "Add content here": "Aggiungi un contenuto qui", "Add Custom Parameter": "Aggiungi Parametri <PERSON>ti", "Add custom prompt": "Aggiungi un prompt personalizzato", "Add Files": "Aggiungi dei file", "Add Group": "Aggiungi un gruppo", "Add Memory": "Aggiungi memoria", "Add Model": "Aggiungi un modello", "Add Reaction": "Aggiungi una reazione", "Add Tag": "Aggiungi un tag", "Add Tags": "Aggiungi dei tag", "Add text content": "Aggiungi contenuto di testo", "Add User": "Aggiungi utente", "Add User Group": "Aggiungi gruppo utente", "Adjusting these settings will apply changes universally to all users.": "La modifica di queste impostazioni applicherà le modifiche universalmente a tutti gli utenti.", "admin": "amministratore", "Admin": "Amministratore", "Admin Panel": "Pannello di amministrazione", "Admin Settings": "Impostazioni amministratore", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Gli amministratori hanno accesso a tutti gli strumenti in qualsiasi momento; gli utenti necessitano di strumenti assegnati per ogni modello nello spazio di lavoro.", "Advanced Parameters": "<PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON>", "AI": "", "All": "<PERSON><PERSON>", "All Documents": "Tutti i documenti", "All models deleted successfully": "<PERSON><PERSON> i modelli eliminati con successo", "Allow Call": "Con<PERSON>i chiamata", "Allow Chat Controls": "Con<PERSON>i controlli chat", "Allow Chat Delete": "Consenti eliminazione chat", "Allow Chat Deletion": "Consenti l'eliminazione della chat", "Allow Chat Edit": "Con<PERSON>i modifica chat", "Allow Chat Export": "Consenti esportazione chat", "Allow Chat Share": "Consenti condivisione chat", "Allow Chat System Prompt": "", "Allow File Upload": "Consenti caricamento file", "Allow Multiple Models in Chat": "<PERSON><PERSON>i più <PERSON>li in chat", "Allow non-local voices": "Consenti voci non locali", "Allow Speech to Text": "Consenti trascrizione vocale", "Allow Temporary Chat": "Consenti chat temporanea", "Allow Text to Speech": "Consenti sintesi vocale", "Allow User Location": "Consenti posizione utente", "Allow Voice Interruption in Call": "Consenti interruzione vocale in chiamata", "Allowed Endpoints": "Endpoint consentiti", "Allowed File Extensions": "Estensioni file permesse", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Le estensioni file permesse per il caricamento. Separa le varie estensioni con una virgola. <PERSON><PERSON> vuoto per tutti i tipi di file.", "Already have an account?": "Hai già un account?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternativa al top_p e mira a garantire un equilibrio tra qualità e varietà. Il parametro p rappresenta la probabilità minima affinché un token venga considerato, rispetto alla probabilità del token più probabile. Ad esempio, con p=0.05 e il token più probabile con una probabilità di 0.9, i logits con un valore inferiore a 0.045 vengono filtrati.", "Always": "Sempre", "Always Collapse Code Blocks": "Rid<PERSON>i sempre i blocchi di codice", "Always Expand Details": "Espandi sempre i dettagli", "Always Play Notification Sound": "Riproduci sempre il suono di notifica", "Amazing": "Fantastico", "an assistant": "un assistente", "Analyzed": "<PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "<PERSON><PERSON><PERSON> in corso...", "and": "e", "and {{COUNT}} more": "e {{COUNT}} altro", "and create a new shared link.": "e crea un nuovo link condiviso.", "Android": "Android", "API": "API", "API Base URL": "URL base per API", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "Dettagli API per utilizzare un modello di tipo vision-language nella descrizione della immagine. Questo parametro è esclusivo per picture_description_local", "API Key": "Chiave API", "API Key created.": "Chiave API creata.", "API Key Endpoint Restrictions": "Restrizioni Endpoint Chiave API", "API keys": "Chiavi API", "API Version": "Versione API", "Application DN": "DN dell'applicazione", "Application DN Password": "Password DN dell'applicazione", "applies to all users with the \"user\" role": "applica a tutti gli utenti con il ruolo \"utente\"", "April": "<PERSON>e", "Archive": "Archivio", "Archive All Chats": "<PERSON><PERSON><PERSON> tutte le chat", "Archived Chats": "Chat archiviate", "archived-chat-export": "chat-archiviata-esportazione", "Are you sure you want to clear all memories? This action cannot be undone.": "Sei sicuro di voler cancellare tutte le memorie? Questa operazione non può essere annullata.", "Are you sure you want to delete this channel?": "Sei sicuro di voler eliminare questo canale?", "Are you sure you want to delete this message?": "Sei sicuro di voler eliminare questo messaggio?", "Are you sure you want to unarchive all archived chats?": "Sei sicuro di voler disarchiviare tutte le chat archiviate?", "Are you sure?": "Sei sicuro?", "Arena Models": "Modelli Arena", "Artifacts": "<PERSON><PERSON><PERSON>", "Ask": "<PERSON><PERSON><PERSON>", "Ask a question": "Fai una domanda", "Assistant": "<PERSON><PERSON><PERSON>", "Attach file from knowledge": "Allega file dalla conoscenza", "Attention to detail": "Attenzione ai dettagli", "Attribute for Mail": "Attributo per la posta", "Attribute for Username": "Attributo per il nome utente", "Audio": "Audio", "August": "Agosto", "Auth": "Autenticazione", "Authenticate": "Autentica", "Authentication": "Autenticazione", "Auto": "Automatico", "Auto-Copy Response to Clipboard": "Copia automatica della risposta negli appunti", "Auto-playback response": "Riproduzione automatica della risposta", "Autocomplete Generation": "Generazione dell'autocompletamento", "Autocomplete Generation Input Max Length": "Lunghezza massima input generazione dell'autocompletamento", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "Stringa autenticazione AUTOMATIC1111 Api", "AUTOMATIC1111 Base URL": "URL base AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "L'URL base AUTOMATIC1111 è obbligatorio.", "Available list": "Elenco disponibile", "Available Tools": "Strumenti disponibili", "available!": "disponibile!", "Awful": "Terribile", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Regione di Azure", "Back": "Indietro", "Bad Response": "Risposta non valida", "Banners": "Banner", "Base Model (From)": "Modello base (da)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "prima", "Being lazy": "Faccio il pigro", "Beta": "Beta", "Bing Search V7 Endpoint": "Endpoint di Bing Search V7", "Bing Search V7 Subscription Key": "Chiave di Iscrizione Bing Search V7", "Bocha Search API Key": "Chiave API di Bocha Search", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Potenziare o penalizzare token specifici per risposte vincolate. I valori di bias saranno limitati tra -100 e 100 (incluso). (Predefinito: nessuno)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Entrambi il Docling OCR Engine e la lingua(e) devono essere forniti o entrambi lasciati vuoti.", "Brave Search API Key": "Chiave API di ricerca Brave", "Bullet List": "", "By {{name}}": "Di {{name}}", "Bypass Embedding and Retrieval": "Bypass embedding e recupero", "Bypass Web Loader": "Bypassa il Web Loader", "Cache Base Model List": "", "Calendar": "Calendario", "Call": "<PERSON><PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "La funzione di chiamata non è supportata quando si utilizza il motore Web STT", "Camera": "Fotocamera", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Funzionalità", "Capture": "Cattura", "Capture Audio": "Cattura audio", "Certificate Path": "<PERSON><PERSON><PERSON> certificato", "Change Password": "Cambia password", "Channel Name": "Nome canale", "Channels": "Canali", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Limite di caratteri per l'input di generazione dell'autocompletamento", "Chart new frontiers": "Traccia nuove frontiere", "Chat": "Cha<PERSON>", "Chat Background Image": "Immagine di sfondo chat", "Chat Bubble UI": "UI bubble chat", "Chat Controls": "Controlli chat", "Chat direction": "<PERSON><PERSON><PERSON> chat", "Chat Overview": "Panoramica chat", "Chat Permissions": "<PERSON><PERSON><PERSON> chat", "Chat Tags Auto-Generation": "Generazione automatica dei tag chat", "Chats": "Cha<PERSON>", "Check Again": "Controlla di nuovo", "Check for updates": "Controlla aggiornamenti", "Checking for updates...": "Controllo aggiornamenti...", "Choose a model before saving...": "Scegli un modello prima di salvare...", "Chunk Overlap": "Sovrapposizione chunk", "Chunk Size": "Dimensione chunk", "Ciphers": "<PERSON><PERSON><PERSON><PERSON>", "Citation": "Citazione", "Citations": "Citazioni", "Clear memory": "Cancella memoria", "Clear Memory": "Cancella memoria", "click here": "clicca qui", "Click here for filter guides.": "Clicca qui per le guide ai filtri.", "Click here for help.": "Clicca qui per aiuto.", "Click here to": "Clicca qui per", "Click here to download user import template file.": "Clicca qui per scaricare il file modello di importazione utente.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> qui per saperne di più su faster-whisper e vedere i modelli disponibili.", "Click here to see available models.": "Clicca qui per vedere i modelli disponibili.", "Click here to select": "<PERSON>lic<PERSON> qui per selezionare", "Click here to select a csv file.": "Clicca qui per selezionare un file csv.", "Click here to select a py file.": "<PERSON>licca qui per selezionare un file py.", "Click here to upload a workflow.json file.": "Clicca qui per caricare un file workflow.json.", "click here.": "clicca qui.", "Click on the user role button to change a user's role.": "Clicca sul pulsante del ruolo utente per modificare il ruolo di un utente.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Autorizzazione di scrittura negli appunti negata. Controlla le impostazioni del browser per concedere l'accesso necessario.", "Clone": "<PERSON><PERSON>", "Clone Chat": "CLona Chat", "Clone of {{TITLE}}": "<PERSON><PERSON> di {{TITLE}}", "Close": "<PERSON><PERSON>", "Close Configure Connection Modal": "", "Close modal": "<PERSON><PERSON> modale", "Close settings modal": "", "Code Block": "", "Code execution": "Esecuzione codice", "Code Execution": "Esecuzione codice", "Code Execution Engine": "Motore di esecuzione codice", "Code Execution Timeout": "Timeout esecuzione codice", "Code formatted successfully": "Codice formattato con successo", "Code Interpreter": "Interprete codice", "Code Interpreter Engine": "Motore interprete codice", "Code Interpreter Prompt Template": "Template di prompt per interprete codice", "Collapse": "<PERSON><PERSON><PERSON><PERSON>", "Collection": "Collezione", "Color": "Colore", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Chiave API ComfyUI", "ComfyUI Base URL": "URL base ComfyUI", "ComfyUI Base URL is required.": "L'URL base ComfyUI è obbligatorio.", "ComfyUI Workflow": "Flusso di lavoro ComfyUI", "ComfyUI Workflow Nodes": "Nodi flusso di lavoro ComfyUI", "Command": "Comand<PERSON>", "Comment": "", "Completions": "Completamenti", "Concurrent Requests": "Richieste simultanee", "Configure": "Configura", "Confirm": "Conferma", "Confirm Password": "Conferma password", "Confirm your action": "Conferma la tua azione", "Confirm your new password": "Conferma la tua nuova password", "Connect to your own OpenAI compatible API endpoints.": "Connettiti ai tuoi endpoint API compatibili con OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "Connettiti ai tuoi server di tool esterni compatibili con OpenAPI.", "Connection failed": "Connessione fallita", "Connection successful": "Connessione riuscita", "Connection Type": "Tipo Connessione", "Connections": "<PERSON><PERSON><PERSON><PERSON>", "Connections saved successfully": "Connessioni salvate con successo", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "<PERSON>ita lo sforzo di ragionamento per i modelli di ragionamento. Applicabile solo a modelli di ragionamento di fornitori specifici che supportano lo sforzo di ragionamento.", "Contact Admin for WebUI Access": "Contatta l'amministratore per l'accesso al servizio WebUI", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction Engine": "Motore di estrazione contenuti", "Continue Response": "Continua risposta", "Continue with {{provider}}": "Continua con {{provider}}", "Continue with Email": "Continua con email", "Continue with LDAP": "Continua con LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlla come il testo del messaggio viene suddiviso per le richieste TTS. 'Punteggiatura' divide in frasi, 'paragrafi' divide in paragrafi e 'nessuno' mantiene il messaggio come una singola stringa.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Controlla la ripetizione delle sequenze di token nel testo generato. Un valore più alto (ad esempio, 1.5) penalizzerà le ripetizioni in modo più forte, mentre un valore più basso (ad esempio, 1.1) sarà più indulgente. A 1, è disabilitato.", "Controls": "<PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Controlla l'equilibrio tra coerenza e diversità dell'output. Un valore più basso risulterà in un testo più focalizzato e coerente.", "Copied": "Copiato", "Copied link to clipboard": "Link copiato negli appunti", "Copied shared chat URL to clipboard!": "URL della chat condivisa copiato negli appunti!", "Copied to clipboard": "Copiato negli appunti", "Copy": "Copia", "Copy Formatted Text": "Copia testo formattato", "Copy last code block": "Copia ultimo blocco di codice", "Copy last response": "Copia ultima risposta", "Copy link": "", "Copy Link": "Copia link", "Copy to clipboard": "Copia negli appunti", "Copying to clipboard was successful!": "Copia negli appunti riuscita!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS deve essere configurato correttamente dal fornitore per consentire le richieste da Open WebUI.", "Create": "<PERSON><PERSON>", "Create a knowledge base": "Crea una base di conoscenze", "Create a model": "<PERSON><PERSON><PERSON> un modello", "Create Account": "Crea account", "Create Admin Account": "Crea account amministratore", "Create Channel": "<PERSON>rea canale", "Create Folder": "", "Create Group": "<PERSON>rea gruppo", "Create Knowledge": "<PERSON><PERSON> con<PERSON>", "Create new key": "Crea nuova chiave", "Create new secret key": "Crea nuova chiave segreta", "Create Note": "<PERSON><PERSON> nota", "Create your first note by clicking on the plus button below.": "Crea la tua prima nota cliccando sul pulsante + sotto.", "Created at": "Creato il", "Created At": "Creato il", "Created by": "<PERSON><PERSON><PERSON> <PERSON>", "CSV Import": "Importazione CSV", "Ctrl+Enter to Send": "Ctrl+Invio per inviare", "Current Model": "<PERSON><PERSON>", "Current Password": "Password corrente", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Custom description enabled": "", "Custom Parameter Name": "Nome parametro personalizzato", "Custom Parameter Value": "Valore parametro personalizzato", "Danger Zone": "Zona di pericolo", "Dark": "<PERSON><PERSON>", "Database": "Database", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "Chiave Datalab Marker API necessaria.", "DD/MM/YYYY": "", "December": "Dicembre", "Default": "Predefinito", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Predefinito (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Il modello predefinito funziona con un'ampia gamma di modelli chiamando gli strumenti una volta prima dell'esecuzione. La modalità nativa sfrutta le capacità di chiamata degli strumenti integrate nel modello, ma richiede che il modello supporti intrinsecamente questa funzionalità.", "Default Model": "<PERSON><PERSON> pre<PERSON>fin<PERSON>", "Default model updated": "<PERSON><PERSON> predefinito a<PERSON>", "Default Models": "Modelli predefiniti", "Default permissions": "Permessi predefiniti", "Default permissions updated successfully": "Permessi predefiniti aggiornati con successo", "Default Prompt Suggestions": "Suggerimenti prompt predefiniti", "Default to 389 or 636 if TLS is enabled": "Predefinito a 389 o 636 se TLS è ab", "Default to ALL": "Predefinito su TUTTI", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Predefinito per il recupero segmentato per un'estrazione di contenuti mirata e pertinente, questo è raccomandato per la maggior parte dei casi.", "Default User Role": "<PERSON><PERSON>lo utente predefinito", "Delete": "Elimina", "Delete a model": "Elimina un modello", "Delete All Chats": "<PERSON>mina tutte le chat", "Delete All Models": "Elimina tutti i modelli", "Delete chat": "Elimina chat", "Delete Chat": "Elimina chat", "Delete chat?": "Elimina chat?", "Delete folder?": "Elimina cartella?", "Delete function?": "Elimina funzione?", "Delete Message": "<PERSON><PERSON> messaggio", "Delete message?": "Elimina messaggio?", "Delete note?": "Elimina nota?", "Delete prompt?": "Elimina prompt?", "delete this link": "elimina questo link", "Delete tool?": "Elimina strumento?", "Delete User": "Elimina utente", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} eliminato", "Deleted {{name}}": "{{name}} eliminato", "Deleted User": "Utente eliminato", "Deployment names are required for Azure OpenAI": "I nomi dei deployment sono obbligatori per Azure OpenAI", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Descrivi la tua base di conoscenza e gli obiettivi", "Description": "Descrizione", "Detect Artifacts Automatically": "Rileva artefatti automaticamente", "Dictate": "<PERSON><PERSON>", "Didn't fully follow instructions": "Non ha seguito completamente le istruzioni", "Direct": "<PERSON><PERSON><PERSON>", "Direct Connections": "<PERSON><PERSON><PERSON><PERSON> Dirette", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Le Connessioni Dirette consentono agli utenti di connettersi ai propri endpoint API compatibili con OpenAI.", "Direct Tool Servers": "Strimentu Server <PERSON>", "Disable Code Interpreter": "", "Disable Image Extraction": "Disattiva l'estrazione immagini", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Disattiva l'estrazione immagini dai PDF. Se LLM è attivo le immagini saranno didascalizzate. Predefinito a Falso.", "Disabled": "Disabilitato", "Discover a function": "Scopri una funzione", "Discover a model": "Scopri un modello", "Discover a prompt": "<PERSON><PERSON><PERSON> un prompt", "Discover a tool": "Scopri uno strumento", "Discover how to use Open WebUI and seek support from the community.": "Scopri come utilizzare Open WebUI e cerca supporto dalla community.", "Discover wonders": "<PERSON><PERSON><PERSON> mera<PERSON>glie", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON>, scarica ed esplora funzioni personalizzate", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, scarica ed esplora prompt personal<PERSON><PERSON><PERSON>", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON>, scarica ed esplora strumenti personalizzati", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON>, scarica ed esplora i preset dei modello", "Display": "Visualizza", "Display Emoji in Call": "Visualizza emoji nella chiamata", "Display the username instead of You in the Chat": "Visualizza il nome utente invece di Tu nella chat", "Displays citations in the response": "Visualizza citazioni nella risposta", "Dive into knowledge": "Immergiti nella conoscenza", "Do not install functions from sources you do not fully trust.": "Non installare funzioni da fonti di cui non ti fidi completamente.", "Do not install tools from sources you do not fully trust.": "Non installare strumenti da fonti di cui non ti fidi completamente.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "L'URL del server Docling è obbligatoria.", "Document": "Documento", "Document Intelligence": "Document Intelligence", "Document Intelligence endpoint and key required.": "Endpoint e chiave per Document Intelligence sono richiesti.", "Documentation": "Documentazione", "Documents": "Documenti", "does not make any external connections, and your data stays securely on your locally hosted server.": "non effettua connessioni esterne e i tuoi dati rimangono al sicuro sul tuo server ospitato localmente.", "Domain Filter List": "Elenco filtri dominio", "Don't have an account?": "Non hai un account?", "don't install random functions from sources you don't trust.": "non installare funzioni da fonti di cui non ti fidi.", "don't install random tools from sources you don't trust.": "non installare strumenti da fonti di cui non ti fidi.", "Don't like the style": "Non ti piace lo stile", "Done": "<PERSON><PERSON>", "Download": "Scarica", "Download as SVG": "Scarica come SVG", "Download canceled": "Scaricamento annullato", "Download Database": "Scarica database", "Drag and drop a file to upload or select a file to view": "Trascina e rilascia un file per caricarlo o seleziona un file da visualizzare", "Draw": "Disegna", "Drop any files here to upload": "Rilascia qui i file per caricarli", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "ad esempio '30s','10m'. Le unità di tempo valide sono 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "ad esempio \"json\" o uno schema JSON", "e.g. 60": "ad esempio 60", "e.g. A filter to remove profanity from text": "ad esempio un filtro per rimuovere le parolacce dal testo", "e.g. en": "ad esempio en", "e.g. My Filter": "ad esempio il Mio Filtro", "e.g. My Tools": "ad esempio i Miei Strumenti", "e.g. my_filter": "ad esempio il mio_filtro", "e.g. my_tools": "ad esempio i miei_strumenti", "e.g. pdf, docx, txt": "ad esempio pdf, docx, txt", "e.g. Tools for performing various operations": "ad esempio strumenti per eseguire varie operazioni", "e.g., 3, 4, 5 (leave blank for default)": "ad esempio, 3, 4, 5 (lascia vuoto per predefinito)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "ad esempio, en-US,ja-JP (lascia vuoto per auto-rilevamento)", "e.g., westus (leave blank for eastus)": "ad esempio, westus (lascia vuoto per est)", "e.g.) en,fr,de": "ad esempio ) en,fr,de", "Edit": "Modifica", "Edit Arena Model": "Modifica Modello Arena", "Edit Channel": "Modifica Canale", "Edit Connection": "Modifica Connessione", "Edit Default Permissions": "Modifica Permessi Predefiniti", "Edit Folder": "", "Edit Memory": "Modifica Memoria", "Edit User": "Modifica Utente", "Edit User Group": "Modifica Gruppo Utente", "Edited": "", "Editing": "", "Eject": "<PERSON><PERSON><PERSON>", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "Intraprendi avventure", "Embedding": "Embedding", "Embedding Batch Size": "Dimensione Batch Embedding", "Embedding Model": "Modello Embedding", "Embedding Model Engine": "Motore Modello di Embedding", "Embedding model set to \"{{embedding_model}}\"": "<PERSON><PERSON> di embedding impostato su \"{{embedding_model}}\"", "Enable API Key": "Abilita Chiave API", "Enable autocomplete generation for chat messages": "Abilita generazione autocompletamento per i messaggi di chat", "Enable Code Execution": "Abilita Esecuzione Codice", "Enable Code Interpreter": "Abilita Interprete Codice", "Enable Community Sharing": "Abilita Condivisione Community", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Abilita il blocco della memoria (mlock) per impedire che i dati del modello vengano scambiati dalla RAM. Questa opzione blocca l'insieme di pagine di lavoro del modello nella RAM, assicurando che non vengano scambiate su disco. Questo può aiutare a mantenere le prestazioni evitando errori di pagina e garantendo un accesso rapido ai dati.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Abilita il mapping della memoria (mmap) per caricare i dati del modello. Questa opzione consente al sistema di utilizzare lo spazio di archiviazione su disco come estensione della RAM trattando i file su disco come se fossero nella RAM. Questo può migliorare le prestazioni del modello consentendo un accesso più rapido ai dati. <PERSON>tta<PERSON>, potrebbe non funzionare correttamente con tutti i sistemi e può consumare una quantità significativa di spazio su disco.", "Enable Message Rating": "Abilita valutazione messaggio", "Enable Mirostat sampling for controlling perplexity.": "Abilita il campionamento Mirostat per controllare la perplessità.", "Enable New Sign Ups": "Abilita Nuove Registrazioni", "Enabled": "Abilitato", "Endpoint URL": "URL Endpoint", "Enforce Temporary Chat": "Forza Chat Temporanea", "Enhance": "<PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Assicurati che il tuo file CSV includa 4 colonne in questo ordine: <PERSON>me, Email, Password, <PERSON><PERSON><PERSON>.", "Enter {{role}} message here": "Inser<PERSON>ci il messaggio per {{role}} qui", "Enter a detail about yourself for your LLMs to recall": "Inserisci un dettaglio su di te per che i LLM possano ricordare", "Enter a title for the pending user info overlay. Leave empty for default.": "Inserisci un titolo per gli utente in attesa nella schermata informazioni. LAscia vuoto per il predefinito.", "Enter a watermark for the response. Leave empty for none.": "Inserisci un watermark per le risposte. <PERSON>cia vuoto per nessuno.", "Enter api auth string (e.g. username:password)": "Inserisci la stringa di autenticazione API (ad es. nome utente:password)", "Enter Application DN": "Inserisci DN dell'applicazione", "Enter Application DN Password": "Inserisci password DN dell'applicazione", "Enter Bing Search V7 Endpoint": "Inserisci l'endpoint di Bing Search V7", "Enter Bing Search V7 Subscription Key": "Inserisci Chiave di sottoscrizione di Bing Search V7", "Enter BM25 Weight": "Inserisci il peso BM25", "Enter Bocha Search API Key": "Inserisci Chiave API di Bocha Search", "Enter Brave Search API Key": "Inserisci Chiave API di Brave Search", "Enter certificate path": "Inserisci il percorso del certificato", "Enter CFG Scale (e.g. 7.0)": "Inserisci CFG Scale (ad esempio 7.0)", "Enter Chunk Overlap": "Inserisci Sovrapposizione Chunk", "Enter Chunk Size": "Inserisci Dimensione Chunk", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Inserisci coppie \"token:valore_bias\" separate da virgole (esempio: 5432:100, 413:-100)", "Enter Config in JSON format": "Inserisci la configurazione nel formato JSON", "Enter content for the pending user info overlay. Leave empty for default.": "Inserisci contenuto per l'overlay di info per utenti in attesa. Lascia vuoto per predefinito.", "Enter Datalab Marker API Key": "Inserisci la chiave Datalab Marker API", "Enter description": "Inserisci descrizione", "Enter Docling OCR Engine": "Inserisci Engine di Docling OCR", "Enter Docling OCR Language(s)": "Inserisci la lingua(/e) di Docling OCR", "Enter Docling Server URL": "Inserisci URL del Server Docling", "Enter Document Intelligence Endpoint": "Inserisci Endpoint di Document Intelligence", "Enter Document Intelligence Key": "Inserisci Chiave di Document Intelligence", "Enter domains separated by commas (e.g., example.com,site.org)": "Inserisci i domini separati da virgole (ad es., example.com,site.org)", "Enter Exa API Key": "Inserisci Chiave API Exa", "Enter External Document Loader API Key": "Inserisci Chiave API del Caricatore Documenti Esterno", "Enter External Document Loader URL": "Inserisci URL del Caricatore Documenti Esterno", "Enter External Web Loader API Key": "Inserisci Chiave API del Caricatore Web Esterno", "Enter External Web Loader URL": "Inserisci l'URL del Caricatore Web Esterno", "Enter External Web Search API Key": "Inserisci Chiave API di Ricerca Web Esterna", "Enter External Web Search URL": "Inserisci URL di Ricerca Web Esterna", "Enter Firecrawl API Base URL": "Inserisci l'URL base dell'API Firecrawl", "Enter Firecrawl API Key": "Inserisci Chiave API Firecrawl", "Enter folder name": "", "Enter Github Raw URL": "Inserisci URL Grezzo di Github", "Enter Google PSE API Key": "Inserisci Chiave API per Google PSE", "Enter Google PSE Engine Id": "Inserisci Engine Id per Google PSE", "Enter Image Size (e.g. 512x512)": "Inserisci Dimensione Immagine (ad esempio 512x512)", "Enter Jina API Key": "Inserisci Chiave API Jina", "Enter Jupyter Password": "Inserisci Password Jupyter", "Enter Jupyter Token": "Inserisci Token Jupyter", "Enter Jupyter URL": "Inserisci URL Jupyter", "Enter Kagi Search API Key": "Inserisci Chiave API Kagi Search", "Enter Key Behavior": "Comportamento Tasto Invio", "Enter language codes": "Inserisci i codici lingua", "Enter Mistral API Key": "Inserisci Chiave API Mistral", "Enter Model ID": "Inserisci ID Modello", "Enter model tag (e.g. {{modelTag}})": "Inserisci il tag del modello (ad esempio {{modelTag}})", "Enter Mojeek Search API Key": "Inserisci Chiave API di Mojeek Search", "Enter name": "Inserisci il nome", "Enter New Password": "Inserisci la Nuova Password", "Enter Number of Steps (e.g. 50)": "Inserisci Numero di Passaggi (ad esempio 50)", "Enter Perplexity API Key": "Inserisci Chiave API di Perplexity", "Enter Playwright Timeout": "Inserisci Timeout di Playwright", "Enter Playwright WebSocket URL": "Inserisci l'URL WebSocket di Playwright", "Enter proxy URL (e.g. **************************:port)": "Inserisci l'URL del proxy (ad es. **************************:port)", "Enter reasoning effort": "Inserisci lo sforzo di ragionamento", "Enter Sampler (e.g. Euler a)": "Inserisci il Campionatore (ad esempio Euler a)", "Enter Scheduler (e.g. Karras)": "Inserisci lo Scheduler (ad esempio <PERSON>)", "Enter Score": "Inser<PERSON>ci <PERSON>", "Enter SearchApi API Key": "Inserisci Chiave API SearchApi", "Enter SearchApi Engine": "Inserisci Motore SearchApi", "Enter Searxng Query URL": "Immettere l'URL della Query Searxng", "Enter Seed": "Inserisci il Seme", "Enter SerpApi API Key": "Inserisci Chiave API SerpApi", "Enter SerpApi Engine": "Inserisci Motore SerpApi", "Enter Serper API Key": "Inserisci Chiave API Serper", "Enter Serply API Key": "Inserisci Chiave API Serply", "Enter Serpstack API Key": "Inserisci Chiave API Serpstack", "Enter server host": "Inserisci l'host del server", "Enter server label": "Inserisci l'etichetta del server", "Enter server port": "Inserisci la porta del server", "Enter Sougou Search API sID": "Inserisci sID dell'API Sougou Search", "Enter Sougou Search API SK": "Inserisci SK dell'API Sougou Search", "Enter stop sequence": "Inserisci la sequenza di arresto", "Enter system prompt": "Inserisci il prompt di sistema", "Enter system prompt here": "Inserisci il prompt di sistema qui", "Enter Tavily API Key": "Inserisci Chiave API Tavily", "Enter Tavily Extract Depth": "Inserisci la Profondità di Estrazione Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Inserisci l'URL pubblico del tuo WebUI. Questo URL verrà utilizzato per generare collegamenti nelle notifiche.", "Enter the URL of the function to import": "Inserisci la URL della funzione di importazione", "Enter the URL to import": "Inserisci la URL della importazione", "Enter Tika Server URL": "Inserisci l'URL del Server Tika", "Enter timeout in seconds": "Inserisci la scadenza in secondi", "Enter to Send": "Premi Invio per Inviare", "Enter Top K": "Inserisci Top K", "Enter Top K Reranker": "Inserisci Top K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "Inserisci URL (ad esempio http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Inserisci URL (ad esempio http://localhost:11434)", "Enter Yacy Password": "Inserisci Password Yacy", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Inserisci l'URL Yacy (ad esempio http://yacy.example.com:8090)", "Enter Yacy Username": "Inserisci Nome Utente Yacy", "Enter your current password": "Inserisci la tua password attuale", "Enter Your Email": "Inserisci la Tua Email", "Enter Your Full Name": "Inserisci il Tuo Nome Completo", "Enter your message": "Inser<PERSON>ci il tuo messaggio", "Enter your name": "Inser<PERSON>ci il tuo nome", "Enter Your Name": "Inserisci il Tuo Nome", "Enter your new password": "Inserisci la tua nuova password", "Enter Your Password": "Inserisci la Tua Password", "Enter Your Role": "Inserisci il Tuo Ruolo", "Enter Your Username": "Inserisci il Tuo Nome Utente", "Enter your webhook URL": "Inserisci l'URL del tuo webhook", "Error": "Errore", "ERROR": "ERRORE", "Error accessing Google Drive: {{error}}": "Errore durante l'accesso a Google Drive: {{error}}", "Error accessing media devices.": "Errore durante l'accesso ai dispositivi multimediali.", "Error starting recording.": "Errore durante l'avvio della registrazione.", "Error unloading model: {{error}}": "Errore durante lo scaricamento della memoria del modello: {{error}}", "Error uploading file: {{error}}": "Errore durante il caricamento del file: {{error}}", "Evaluations": "Valutazioni", "Everyone": "", "Exa API Key": "Chiave API Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Esempio: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Esempio: TUTTI", "Example: mail": "Esempio: mail", "Example: ou=users,dc=foo,dc=example": "Esempio: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Esempio: sAMAccountName o uid o userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Superato il numero di posti nella tua licenza. Contatta il supporto per aumentare il numero di posti.", "Exclude": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Execute code for analysis": "Esegui codice per analisi", "Executing **{{NAME}}**...": "Esecuzione **{{NAME}}**...", "Expand": "Espandi", "Experimental": "Sperimentale", "Explain": "<PERSON><PERSON><PERSON>", "Explore the cosmos": "Esplora il cosmo", "Export": "Esportazione", "Export All Archived Chats": "Esporta Tutte le Chat Archiviate", "Export All Chats (All Users)": "Esporta Tutte le Chat (Tutti gli Utenti)", "Export chat (.json)": "Esporta chat (.json)", "Export Chats": "Esporta Chat", "Export Config to JSON File": "Esporta Configurazione in file JSON", "Export Functions": "Esporta Funzioni", "Export Models": "Esporta Modelli", "Export Presets": "Esporta Preset", "Export Prompt Suggestions": "Esporta i suggerimenti per il prompt", "Export Prompts": "Esporta Prompt", "Export to CSV": "Esporta in CSV", "Export Tools": "Esporta Strumenti", "External": "Esterno", "External Document Loader URL required.": "URL esterna per il Document Loader necessaria.", "External Task Model": "Task Modello esterna", "External Web Loader API Key": "Chiave API del web loaderesterno", "External Web Loader URL": "URL del web loader esterno", "External Web Search API Key": "Chiave API di ricerca web esterna", "External Web Search URL": "URL di ricerca web esterna", "Fade Effect for Streaming Text": "", "Failed to add file.": "Impossibile aggiungere il file.", "Failed to connect to {{URL}} OpenAPI tool server": "Impossibile connettersi al server dello strumento OpenAPI {{URL}}", "Failed to copy link": "Impossibile copiare il link", "Failed to create API Key.": "Impossibile creare Chiave API.", "Failed to delete note": "Impossibile eliminare la nota", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "Impossibile recuperare i modelli", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "Impossibile caricare il contenuto del file.", "Failed to read clipboard contents": "Impossibile leggere il contenuto degli appunti", "Failed to save connections": "Impossibile salvare le connessioni", "Failed to save models configuration": "Impossibile salvare la configurazione dei modelli", "Failed to update settings": "Impossibile aggiornare le impostazioni", "Failed to upload file.": "Impossibile caricare il file.", "Features": "<PERSON><PERSON><PERSON><PERSON>", "Features Permissions": "Permessi delle funzionalità", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "Dettagli feedback", "Feedback History": "Storico feedback", "Feedbacks": "<PERSON><PERSON><PERSON>", "Feel free to add specific details": "Sentiti libero/a di aggiungere dettagli specifici", "File": "File", "File added successfully.": "File aggiunto con successo.", "File content updated successfully.": "Contenuto del file aggiornato con successo.", "File Mode": "Modalità File", "File not found.": "File non trovato.", "File removed successfully.": "File rimosso con successo.", "File size should not exceed {{maxSize}} MB.": "La dimensione del file non deve superare {{maxSize}} MB.", "File Upload": "Caricamento file", "File uploaded successfully": "Caricamento file riuscito", "Files": "File", "Filter": "", "Filter is now globally disabled": "Il filtro è ora disabilitato globalmente", "Filter is now globally enabled": "Il filtro è ora abilitato globalmente", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "R<PERSON>vato spoofing delle impronte digitali: impossibile utilizzare le iniziali come avatar. Ripristino all'immagine del profilo predefinita.", "Firecrawl API Base URL": "URL base dell'API Firecrawl", "Firecrawl API Key": "Chiave API Firecrawl", "Fluidly stream large external response chunks": "<PERSON><PERSON><PERSON><PERSON> in modo fluido blocchi di risposta esterni di grandi dimensioni", "Focus chat input": "<PERSON><PERSON> a fuoco l'input della chat", "Folder deleted successfully": "<PERSON><PERSON><PERSON> rim<PERSON> con <PERSON>o", "Folder Name": "", "Folder name cannot be empty.": "Il nome della cartella non può essere vuoto.", "Folder name updated successfully": "Nome cartella aggiornato con successo", "Folder updated successfully": "", "Follow up": "Follow up", "Follow Up Generation": "Generazione follow up", "Follow Up Generation Prompt": "Generazione prompt follow up", "Follow-Up Auto-Generation": "Generazione automatico follow up", "Followed instructions perfectly": "Ha seguito le istruzioni alla perfezione", "Force OCR": "Forza OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "Forza OCR su tutte le pagine dei PDF. Questo porterebbe ai peggiori risultati se hai dell'ottimo testo nei PDF. Predefinito a Falso.", "Forge new paths": "Traccia nuovi percorsi", "Form": "<PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "Formatta le tue variabili usando le parentesi quadre in questo modo:", "Forwards system user session credentials to authenticate": "Inoltra le credenziali della sessione utente di sistema per autenticare", "Full Context Mode": "Modalità Contesto Completo", "Function": "Funzione", "Function Calling": "Chiamata Funzione", "Function created successfully": "Funzione creata con successo", "Function deleted successfully": "Funzione eliminata con successo", "Function Description": "Descrizione Funzione", "Function ID": "ID Funzione", "Function imported successfully": "Funzione importata con successo", "Function is now globally disabled": "Il filtro è ora disabilitato globalmente", "Function is now globally enabled": "Il filtro è ora abilitato globalmente", "Function Name": "Nome Funzione", "Function updated successfully": "Funzione aggiornata con successo", "Functions": "Funzioni", "Functions allow arbitrary code execution.": "Le funzioni consentono l'esecuzione di codice arbitrario.", "Functions imported successfully": "Funzioni importate con successo", "Gemini": "Gemini", "Gemini API Config": "Configurazione API Gemini", "Gemini API Key is required.": "La Chiave API Gemini è richiesta.", "General": "Generale", "Generate": "Genera", "Generate an image": "Genera un'immagine", "Generate Image": "Genera Immagine", "Generate prompt pair": "Genera coppia di prompt", "Generating search query": "Generazione query di ricerca", "Generating...": "Generazione in corso...", "Get information on {{name}} in the UI": "", "Get started": "Inizia", "Get started with {{WEBUI_NAME}}": "Inizia con {{WEBUI_NAME}}", "Global": "Globale", "Good Response": "<PERSON><PERSON><PERSON>", "Google Drive": "Google Drive", "Google PSE API Key": "Chiave API PSE di Google", "Google PSE Engine Id": "ID motore PSE di Google", "Group created successfully": "Gruppo creato con successo", "Group deleted successfully": "Gruppo eliminato con successo", "Group Description": "Descrizione Gruppo", "Group Name": "Nome Gruppo", "Group updated successfully": "Gruppo aggiornato con successo", "Groups": "Gruppi", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Feed<PERSON>", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Aiutaci a creare la migliore classifica della community condividendo la tua cronologia feedback!", "Hex Color": "Colore Esadecimale", "Hex Color - Leave empty for default color": "Colore esadecimale - <PERSON><PERSON> vuoto per il colore predefinito", "Hide": "Nascondi", "Hide from Sidebar": "Nascosta dalla barra laterale", "Hide Model": "Nascondi Modello", "High Contrast Mode": "Modalità Alto Contrasto", "Home": "Home", "Host": "Host", "How can I help you today?": "Come posso aiutarti oggi?", "How would you rate this response?": "Come valuteresti questa risposta?", "HTML": "", "Hybrid Search": "Ricerca Ibrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Riconosco di aver letto e compreso le implicazioni della mia azione. Sono consapevole dei rischi associati all'esecuzione di codice arbitrario e ho verificato l'affidabilità della fonte.", "ID": "ID", "iframe Sandbox Allow Forms": "iframe Sandbox Consenti moduli", "iframe Sandbox Allow Same Origin": "iframe Sandbox Consenti stessa origine", "Ignite curiosity": "Accendi la curiosità", "Image": "<PERSON><PERSON><PERSON><PERSON>", "Image Compression": "Comp<PERSON><PERSON>i", "Image Compression Height": "Altezza immagine compressa", "Image Compression Width": "<PERSON><PERSON><PERSON><PERSON> immagine compressa", "Image Generation": "Generazione Immagini", "Image Generation (Experimental)": "Generazione di immagini (sperimentale)", "Image Generation Engine": "Motore di generazione immagini", "Image Max Compression Size": "Dimensione Massima Compressione Immagine", "Image Max Compression Size height": "Altezza dimensione immagine massima", "Image Max Compression Size width": "Lunghezza dimensione immagine massima", "Image Prompt Generation": "Generazione Prompt Immagine", "Image Prompt Generation Prompt": "Prompt per la Generazione di Prompt Immagine", "Image Settings": "Impostazioni Immagine", "Images": "<PERSON><PERSON><PERSON><PERSON>", "Import": "Importa", "Import Chats": "<PERSON><PERSON>rta <PERSON>", "Import Config from JSON File": "Importa Configurazione da File JSON", "Import From Link": "Importa dai link", "Import Functions": "Importa Funzioni", "Import Models": "Importazione Modelli", "Import Notes": "Importa Note", "Import Presets": "Importa Preset", "Import Prompt Suggestions": "Importa suggerimenti Prompt", "Import Prompts": "Importa Prompt", "Import Tools": "<PERSON><PERSON>rta <PERSON>", "Include": "<PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON>ludi il flag `--api-auth` quando esegui stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "<PERSON>ludi il flag `--api` quando esegui stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Influenza la velocità con cui l'algoritmo risponde al feedback del testo generato. Un tasso di apprendimento più basso comporterà aggiustamenti più lenti, mentre un tasso di apprendimento più alto renderà l'algoritmo più reattivo.", "Info": "Informazioni", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Inietta l'intero contenuto come contesto per un'elaborazione completa, questo è consigliato per query complesse.", "Input commands": "Comandi di input", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Eseguire l'installazione dall'URL di Github", "Instant Auto-Send After Voice Transcription": "Invio automatico istantaneo dopo la trascrizione vocale", "Integration": "Integrazione", "Interface": "Interfaccia", "Invalid file content": "Contenuto del file non valido", "Invalid file format.": "Formato file non valido.", "Invalid JSON file": "File JSOn non valido", "Invalid Tag": "Tag non valido", "is typing...": "sta digitando...", "Italic": "", "January": "Gennaio", "Jina API Key": "Chiave API Jina", "join our Discord for help.": "unisciti al nostro Discord per ricevere aiuto.", "JSON": "JSON", "JSON Preview": "Anteprima JSON", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON>", "Jupyter Auth": "Autenticazione Jupyter", "Jupyter URL": "URL Jupyter", "JWT Expiration": "Scadenza JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "Chiave API di ricerca Kagi", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "<PERSON><PERSON><PERSON> nella barra laterale", "Key": "Chiave", "Keyboard shortcuts": "Scorciatoie da tastiera", "Knowledge": "Conoscenza", "Knowledge Access": "Accesso alla conoscenza", "Knowledge Base": "", "Knowledge created successfully.": "Conoscenza creata con successo.", "Knowledge deleted successfully.": "Conoscenza eliminata con successo.", "Knowledge Public Sharing": "Conoscenza condivisione pubblica", "Knowledge reset successfully.": "Conoscenza ripristinata con successo.", "Knowledge updated successfully": "Conoscenza aggiornata con successo", "Kokoro.js (Browser)": "Kokoro.js (Browser)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "<PERSON><PERSON><PERSON><PERSON>", "Landing Page Mode": "Modalità pagina iniziale", "Language": "<PERSON><PERSON>", "Language Locales": "Locales della lingua", "Languages": "Lingue", "Last Active": "Ultima attività", "Last Modified": "Ultima modifica", "Last reply": "Ultima risposta", "LDAP": "LDAP", "LDAP server updated": "Server LDAP aggiornato", "Leaderboard": "Classifica", "Learn more about OpenAPI tool servers.": "Scopri di più sui server di tool OpenAPI.", "Leave empty for no compression": "Lascia spazio per nessuna compressione", "Leave empty for unlimited": "<PERSON><PERSON> vuoto per illimitato", "Leave empty to include all models from \"{{url}}\" endpoint": "<PERSON><PERSON> vuoto per includere tutti i modelli dagli endpoint \"{{url}}\"", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "<PERSON><PERSON> vuoto per includere tutti i modelli dall'endpoint \"{{url}}/api/tags\"", "Leave empty to include all models from \"{{url}}/models\" endpoint": "<PERSON><PERSON> vuoto per includere tutti i modelli dall'endpoint \"{{url}}/models\"", "Leave empty to include all models or select specific models": "<PERSON><PERSON> vuoto per includere tutti i modelli o seleziona modelli specifici", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON> vuoto per utilizzare il prompt predefinito o inserisci un prompt personalizzato", "Leave model field empty to use the default model.": "<PERSON><PERSON> vuoto il campo modello per utilizzare il modello predefinito.", "License": "Licenza", "Lift List": "", "Light": "Chiaro", "Listening...": "In ascolto...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Gli LLM possono commettere errori. Verifica le informazioni importanti.", "Loader": "Caricatore", "Loading Kokoro.js...": "Caricamento Kokoro.js...", "Local": "Locale", "Local Task Model": "Modello Task locale", "Location access not allowed": "Accesso alla posizione non consentito", "Lost": "<PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Realizzato dalla Comunità Open WebUI", "Make password visible in the user interface": "Rendi la password visibile nella interfaccia utente", "Make sure to enclose them with": "Assicurati di racchiuderli con", "Make sure to export a workflow.json file as API format from ComfyUI.": "Assicurati di esportare un file workflow.json come formato API da ComfyUI.", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage Direct Connections": "Gestisci Connessioni Dirette", "Manage Models": "<PERSON><PERSON><PERSON><PERSON>", "Manage Ollama": "<PERSON><PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Gestisci le connessioni API Ollama", "Manage OpenAI API Connections": "Gestisci le connessioni API OpenAI", "Manage Pipelines": "Gestisci le Pipeline", "Manage Tool Servers": "Gestisci i Server dei Tool", "March": "<PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "Numero Massimo di Parlanti", "Max Upload Count": "Conteggio massimo di caricamenti", "Max Upload Size": "Dimensione massima di caricamento", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "È possibile scaricare un massimo di 3 modelli contemporaneamente. Riprova più tardi.", "May": "Maggio", "Memories accessible by LLMs will be shown here.": "I memori accessibili ai LLM saranno mostrati qui.", "Memory": "Memoria", "Memory added successfully": "Memoria aggiunta con successo", "Memory cleared successfully": "Memoria cancellata con successo", "Memory deleted successfully": "Memoria eliminata con successo", "Memory updated successfully": "Memoria aggiornata con successo", "Merge Responses": "Unisci Risposte", "Merged Response": "Risposta Unita", "Message rating should be enabled to use this feature": "La valutazione dei messaggi deve essere abilitata per utilizzare questa funzionalità", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "I messaggi inviati dopo la creazione del link non verranno condivisi. Gli utenti con l'URL saranno in grado di visualizzare la chat condivisa.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (personale)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (lavoro/scuola)", "Mistral OCR": "OCR Mistral", "Mistral OCR API Key required.": "La Chiave API OCR Mistral è richiesta.", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Il modello '{{modelName}}' è stato scaricato con successo.", "Model '{{modelTag}}' is already in queue for downloading.": "Il modello '{{modelTag}}' è già in coda per il download.", "Model {{modelId}} not found": "<PERSON>lo {{modelId}} non trovato", "Model {{modelName}} is not vision capable": "Il modello {{modelName}} non è in grado di vedere", "Model {{name}} is now {{status}}": "Il modello {{name}} è ora {{status}}", "Model {{name}} is now hidden": "Il modello {{name}} è ora nascosto", "Model {{name}} is now visible": "Il modello {{name}} è ora visibile", "Model accepts file inputs": "Tipo ti file accettati dal modello", "Model accepts image inputs": "Il modello accetta input immagine", "Model can execute code and perform calculations": "Il modello può eseguire del codice e fare calcoli", "Model can generate images based on text prompts": "Il modello può generare delle immagini basate sui prompt testuali", "Model can search the web for information": "Il modello può cercare nella rete per informazioni", "Model created successfully!": "<PERSON><PERSON> creato con successo!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Percorso del filesystem del modello rilevato. Il nome breve del modello è richiesto per l'aggiornamento, impossibile continuare.", "Model Filtering": "Filtraggio modelli", "Model ID": "ID modello", "Model IDs": "ID modello", "Model Name": "Nome modello", "Model not selected": "Modello non selezionato", "Model Params": "Parametri del modello", "Model Permissions": "Permessi del modello", "Model unloaded successfully": "Scaricamento dalla memoria del modello riuscito", "Model updated successfully": "<PERSON><PERSON> con <PERSON>o", "Model(s) do not support file upload": "I/l modello/i non supporta il caricamento file", "Modelfile Content": "Contenuto del file modello", "Models": "<PERSON><PERSON>", "Models Access": "Accesso ai modelli", "Models configuration saved successfully": "Configurazione modelli salvata con successo", "Models Public Sharing": "Conoscenza condivisione pubblica", "Mojeek Search API Key": "Chiave API di Mojeek Search", "more": "altro", "More": "Altro", "Name": "Nome", "Name your knowledge base": "Dai un nome alla tua base di conoscenza", "Native": "Nativo", "New Chat": "Nuova chat", "New Folder": "Nuova cartella", "New Function": "", "New Note": "Nuova nota", "New Password": "Nuova password", "New Tool": "Nuovo strumento", "new-channel": "nuovo-canale", "Next message": "Messaggio successivo", "No chats found": "", "No chats found for this user.": "Nessuna chat trovata per questo utente.", "No chats found.": "Nessuna chat trovata.", "No content": "<PERSON><PERSON><PERSON> contenuto", "No content found": "<PERSON><PERSON><PERSON> contenuto trovato", "No content found in file.": "<PERSON><PERSON>un contenuto trovato nel file.", "No content to speak": "<PERSON><PERSON><PERSON> contenuto da pronunciare", "No distance available": "Nessuna distanza disponibile", "No feedbacks found": "Nessun <PERSON> trovato", "No file selected": "Nessun file selezionato", "No groups with access, add a group to grant access": "Nessun gruppo con accesso, aggiungi un gruppo per concedere l'accesso", "No HTML, CSS, or JavaScript content found.": "<PERSON><PERSON>un contenuto HTML, CSS o JavaScript trovato.", "No inference engine with management support found": "Nessun motore di inferenza con supporto per la gestione trovato", "No knowledge found": "Nessuna conoscenza trovata", "No memories to clear": "<PERSON><PERSON><PERSON> ricordo da cancellare", "No model IDs": "Nessun ID modello", "No models found": "<PERSON><PERSON><PERSON> trovato", "No models selected": "Nessun modello se<PERSON>o", "No Notes": "<PERSON><PERSON><PERSON> nota", "No results found": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "No search query generated": "Nessuna query di ricerca generata", "No source available": "Nessuna fonte disponibile", "No users were found.": "<PERSON><PERSON>un utente trovato.", "No valves to update": "Nessuna valvola da aggiornare", "None": "<PERSON><PERSON><PERSON>", "Not factually correct": "Non corretto dal punto di vista fattuale", "Not helpful": "Non utile", "Note deleted successfully": "Nota eliminata con successo", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: se imposti un punteggio minimo, la ricerca restituirà solo i documenti con un punteggio maggiore o uguale al punteggio minimo.", "Notes": "Note", "Notification Sound": "Suono di notifica", "Notification Webhook": "Webhook di notifica", "Notifications": "Notifiche desktop", "November": "Novembre", "OAuth ID": "", "October": "Ottobre", "Off": "Disattivat<PERSON>", "Okay, Let's Go!": "Ok, andiamo!", "OLED Dark": "OLED scuro", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Impostazioni API Ollama aggiornate", "Ollama Version": "<PERSON><PERSON>", "On": "<PERSON><PERSON><PERSON><PERSON>", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "Solo attivo quando \"Incolla Molto Testo come File\" è attiva.", "Only active when the chat input is in focus and an LLM is generating a response.": "Solo attivo quando l'input chat ha il focus e un LLM sta generando una risposta.", "Only alphanumeric characters and hyphens are allowed": "Nella stringa di comando sono consentiti solo caratteri alfanumerici e trattini", "Only alphanumeric characters and hyphens are allowed in the command string.": "Nella stringa di comando sono consentiti solo caratteri alfanumerici e trattini.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Solo le collezioni possono essere modificate, crea una nuova base di conoscenza per modificare/aggiungere documenti.", "Only markdown files are allowed": "Sono consentiti solo file markdown", "Only select users and groups with permission can access": "Solo gli utenti e i gruppi selezionati con autorizzazione possono accedere", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ops! Sembra che l'URL non sia valido. Si prega di ricontrollare e riprovare.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ops! Ci sono file ancora in fase di caricamento. Si prega di attendere il completamento del caricamento.", "Oops! There was an error in the previous response.": "Ops! Si è verificato un errore nella risposta precedente.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ops! Stai utilizzando un metodo non supportato (solo frontend). Si prega di servire la WebUI dal backend.", "Open file": "Apri file", "Open in full screen": "Apri a schermo intero", "Open modal to configure connection": "", "Open new chat": "Apri nuova chat", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI può utilizzare tool forniti da qualsiasi server OpenAPI.", "Open WebUI uses faster-whisper internally.": "Open WebUI utilizza faster-whisper internamente.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI utilizza le incorporazioni vocali di SpeechT5 e CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "La versione di Open WebUI (v{{OPEN_WEBUI_VERSION}}) è inferiore alla versione richiesta (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configurazione API OpenAI", "OpenAI API Key is required.": "La Chiave API OpenAI è obbligatoria.", "OpenAI API settings updated": "Impostazioni API OpenAI aggiornate", "OpenAI URL/Key required.": "URL/Chiave OpenAI obbligatori.", "openapi.json URL or Path": "URL openapi.json o il percorso", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "Opzioni per l'esecuzione di un modello di vision-language locale nella descrizione dell'immagine. I parametri si riferiscono a un modello ospitato su Hugging Face. Questo parametro è esclusivo con picture_description_api.", "or": "o", "Ordered List": "", "Organize your users": "Organizza i tuoi utenti", "Other": "Altro", "OUTPUT": "OUTPUT", "Output format": "Formato di output", "Output Format": "Formato output", "Overview": "Panoramica", "page": "pagina", "Paginate": "Paginazione", "Parameters": "Parametri", "Password": "Password", "Paste Large Text as File": "<PERSON>olla Mo<PERSON> come File", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Estrazione Immagini PDF (OCR)", "pending": "in sospeso", "Pending": "In Attesa", "Pending User Overlay Content": "Contenuto utente in attesa", "Pending User Overlay Title": "<PERSON><PERSON> utente in attesa", "Permission denied when accessing media devices": "Autorizzazione negata durante l'accesso ai dispositivi multimediali", "Permission denied when accessing microphone": "Autorizzazione negata durante l'accesso al microfono", "Permission denied when accessing microphone: {{error}}": "Autorizzazione negata durante l'accesso al microfono: {{error}}", "Permissions": "<PERSON><PERSON><PERSON>", "Perplexity API Key": "Chiave API Perplexity", "Perplexity Model": "Modello Perplexity", "Perplexity Search Context Usage": "<PERSON><PERSON><PERSON>zo delcontesto della Ricerca Perplexity", "Personalization": "Personalizzazione", "Picture Description API Config": "Descrizione immagine per la configurazione API", "Picture Description Local Config": "Descrizione immagine per la configurazione locale", "Picture Description Mode": "Modalità descrizione immagine", "Pin": "App<PERSON>", "Pinned": "<PERSON><PERSON><PERSON><PERSON>", "Pioneer insights": "Scopri nuove intuizioni", "Pipe": "", "Pipeline deleted successfully": "Pipeline rimossa con successo", "Pipeline downloaded successfully": "Pipeline scaricata con successo", "Pipelines": "Pipeline", "Pipelines Not Detected": "Pipeline Non Rilevate", "Pipelines Valves": "Valvole per pipelines", "Plain text (.md)": "Testo normale (.md)", "Plain text (.txt)": "Testo normale (.txt)", "Playground": "Playground", "Playwright Timeout (ms)": "Timeout per Playwright (ms)", "Playwright WebSocket URL": "URL WebSocket per Playwright", "Please carefully review the following warnings:": "Si prega di esaminare attentamente i seguenti avvisi:", "Please do not close the settings page while loading the model.": "Si prega di non chiudere la pagina delle impostazioni durante il caricamento del modello.", "Please enter a prompt": "Si prega di inserire un prompt", "Please enter a valid path": "Si prega di inserire un percorso valido", "Please enter a valid URL": "Si prega di inserire un URL valido", "Please fill in all fields.": "Si prega di compilare tutti i campi.", "Please select a model first.": "Si prega di selezionare prima un modello.", "Please select a model.": "Si prega di selezionare un modello.", "Please select a reason": "Si prega di selezionare un motivo", "Port": "Porta", "Positive attitude": "Attitudine positiva", "Prefix ID": "ID prefisso", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "L'ID prefisso viene utilizzato per evitare conflitti con altre connessioni aggiungendo un prefisso agli ID dei modelli - lasciare vuoto per disabilitare", "Prevent file creation": "", "Preview": "Anteprima", "Previous 30 days": "Ultimi 30 giorni", "Previous 7 days": "Ultimi 7 giorni", "Previous message": "", "Private": "Privato", "Profile Image": "Immagine del Profilo", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (ad esempio Dimmi un fatto divertente sull'Impero Romano)", "Prompt Autocompletion": "Autocompletamento del Prompt", "Prompt Content": "Contenuto del Prompt", "Prompt created successfully": "Prompt creato con successo", "Prompt suggestions": "<PERSON>gger<PERSON><PERSON> prompt", "Prompt updated successfully": "Prompt aggiornato con successo", "Prompts": "Prompt", "Prompts Access": "Accesso ai Prompt", "Prompts Public Sharing": "Condivisione Pubblica dei Prompt", "Public": "Pubblico", "Pull \"{{searchValue}}\" from Ollama.com": "E<PERSON>i \"{{searchValue}}\" da Ollama.com", "Pull a model from Ollama.com": "Estrai un modello da Ollama.com", "Query Generation Prompt": "Prompt di Generazione Query", "RAG Template": "Modello RAG", "Rating": "Valutazione", "Re-rank models by topic similarity": "Riordina i modelli in base alla somiglianza degli argomenti", "Read": "<PERSON><PERSON><PERSON>", "Read Aloud": "Leggi ad Alta Voce", "Reason": "", "Reasoning Effort": "Sforzo di ragionamento", "Record": "Registra", "Record voice": "Registra voce", "Redirecting you to Open WebUI Community": "Reindirizzamento alla comunità OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Riduce la probabilità di generare sciocchezze. Un valore più alto (ad esempio 100) darà risposte più varie, mentre un valore più basso (ad esempio 10) sarà più conservativo.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Riferisciti a te stesso come \"Utente\" (ad esempio, \"L'utente sta imparando lo spagnolo\")", "References from": "Riferimenti da", "Refused when it shouldn't have": "Rifiutato quando non avrebbe dovuto", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Reindex": "<PERSON>ind<PERSON><PERSON>", "Reindex Knowledge Base Vectors": "Reindicizza i Vettori della Base di Conoscenza", "Release Notes": "Note di Rilascio", "Releases": "<PERSON><PERSON><PERSON><PERSON>", "Relevance": "<PERSON><PERSON><PERSON><PERSON>", "Relevance Threshold": "Soglia di Rilevanza", "Remember Dismissal": "", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON><PERSON><PERSON>", "Remove this tag from list": "Rimuovi questo tag dalla lista", "Rename": "Rinomina", "Reorder Models": "<PERSON><PERSON><PERSON>", "Reply in Thread": "Rispondi nel thread", "Reranking Engine": "Engine di Riclassificazione", "Reranking Model": "Modello di Riclassificazione", "Reset": "R<PERSON><PERSON><PERSON>", "Reset All Models": "<PERSON><PERSON><PERSON><PERSON>", "Reset Upload Directory": "Ripristina Directory di Caricamento", "Reset Vector Storage/Knowledge": "Ripristina Archiviazione Vettoriale/Conoscenza", "Reset view": "Ripristina visualizzazione", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Le notifiche di risposta non possono essere attivate poiché i permessi del sito web sono stati negati. Si prega di visitare le impostazioni del browser per concedere l'accesso necessario.", "Response splitting": "Divisione della risposta", "Response Watermark": "Watermark della richiesta", "Result": "Risultato", "Retrieval": "<PERSON><PERSON><PERSON> ricordo", "Retrieval Query Generation": "Generazione di query di recupero ricordo", "Rich Text Input for Chat": "Input di testo ricco per la chat", "RK": "RK", "Role": "<PERSON><PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "In esecuzione", "Save": "<PERSON><PERSON>", "Save & Create": "Salva e crea", "Save & Update": "Salva e aggiorna", "Save As Copy": "Salva come copia", "Save Tag": "Salva tag", "Saved": "Sal<PERSON><PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Il salvataggio dei registri della chat direttamente nell'archivio del browser non è più supportato. Si prega di dedicare un momento per scaricare ed eliminare i registri della chat facendo clic sul pulsante in basso. Non preoccuparti, puoi facilmente reimportare i registri della chat nel backend tramite", "Scroll On Branch Change": "Scorri al cambio di branch", "Search": "Cerca", "Search a model": "Cerca un modello", "Search Base": "Cerca base", "Search Chats": "Cerca nelle chat", "Search Collection": "Cerca collezione", "Search Filters": "Cerca filtri", "search for tags": "cerca tag", "Search Functions": "Cerca funzioni", "Search Knowledge": "Cerca conoscenza", "Search Models": "Cerca modelli", "Search Notes": "", "Search options": "Cerca opzioni", "Search Prompts": "Cerca prompt", "Search Result Count": "Conteggio dei risultati della ricerca", "Search the internet": "Cerca su Internet", "Search Tools": "Cerca Strumenti", "SearchApi API Key": "Chiave API SearchApi", "SearchApi Engine": "Engine SearchApi", "Searched {{count}} sites": "<PERSON><PERSON><PERSON> {{count}} siti", "Searching \"{{searchQuery}}\"": "Cercando \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Cercando conoscenza per \"{{searchQuery}}\"", "Searching the web...": "Cercando nel web...", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "Vedi readme.md per le istruzioni", "See what's new": "Guarda le novità", "Seed": "<PERSON><PERSON>", "Select a base model": "Selezionare un modello di base", "Select a conversation to preview": "", "Select a engine": "Seleziona un motore", "Select a function": "Seleziona una funzione", "Select a group": "Seleziona un gruppo", "Select a model": "Seleziona un modello", "Select a pipeline": "Selezionare una pipeline", "Select a pipeline url": "Selezionare l'URL di una pipeline", "Select a tool": "Seleziona uno strumento", "Select an auth method": "Seleziona un metodo di autenticazione", "Select an Ollama instance": "Seleziona un'istanza Ollama", "Select Engine": "Seleziona motore", "Select Knowledge": "Seleziona conoscenza", "Select only one model to call": "Seleziona solo un modello da chiamare", "Selected model(s) do not support image inputs": "I modelli selezionati non supportano l'input di immagini", "Semantic distance to query": "Distanza semantica alla query", "Send": "Invia", "Send a Message": "Invia un messaggio", "Send message": "Invia messaggio", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Invia `stream_options: { include_usage: true }` nella richiesta.\nI provider supportati restituiranno informazioni sull'utilizzo dei token nella risposta quando impostato.", "September": "Settembre", "SerpApi API Key": "Chiave API SerpApi", "SerpApi Engine": "Engine SerpApi", "Serper API Key": "Chiave API Serper", "Serply API Key": "Chiave API Serply", "Serpstack API Key": "Chiave API Serpstack", "Server connection verified": "Connessione al server verificata", "Set as default": "Imposta come predefinito", "Set CFG Scale": "Imposta scala CFG", "Set Default Model": "Imposta modello predefinito", "Set embedding model": "Imposta modello di embedding", "Set embedding model (e.g. {{model}})": "Imposta modello di embedding (ad esempio {{model}})", "Set Image Size": "Imposta dimensione immagine", "Set reranking model (e.g. {{model}})": "Imposta modello di riclassificazione (ad esempio {{model}})", "Set Sampler": "Imposta campionatore", "Set Scheduler": "Imposta pianificatore", "Set Steps": "<PERSON><PERSON><PERSON> passaggi", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Set il numero di strati, che verranno scaricati su GPU. Aumentare questo valore può migliorare significativamente le prestazioni per i modelli ottimizzati per l'accelerazione GPU, ma può anche consumare più energia e risorse GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Imposta il numero di thread di lavoro utilizzati per il calcolo. Questa opzione controlla quanti thread vengono utilizzati per elaborare le richieste in arrivo in modo concorrente. Aumentare questo valore può migliorare le prestazioni sotto carichi di lavoro ad alta concorrenza, ma può anche consumare più risorse CPU.", "Set Voice": "Imposta voce", "Set whisper model": "Imposta modello whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Imposta un bias piatto contro i token che sono apparsi almeno una volta. Un valore più alto (ad esempio, 1,5) penalizzerà le ripetizioni in modo più forte, mentre un valore più basso (ad esempio, 0,9) sarà più indulgente. A 0, è disabilitato.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Imposta un bias di scaling contro i token per penalizzare le ripetizioni, in base a quante volte sono apparsi. Un valore più alto (ad esempio, 1,5) penalizzerà le ripetizioni in modo più forte, mentre un valore più basso (ad esempio, 0,9) sarà più indulgente. A 0, è disabilitato.", "Sets how far back for the model to look back to prevent repetition.": "Imposta quanto lontano il modello deve guardare indietro per prevenire la ripetizione.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Imposta il seme del numero casuale da utilizzare per la generazione. Impostando questo su un numero specifico, il modello genererà lo stesso testo per lo stesso prompt.", "Sets the size of the context window used to generate the next token.": "Imposta la dimensione della finestra di contesto utilizzata per generare il token successivo.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Imposta le sequenze di arresto da utilizzare. Quando questo modello viene incontrato, l'LLM smetterà di generare testo e restituirà. Più modelli di arresto possono essere impostati specificando più parametri di arresto separati in un file modello.", "Settings": "Impostazioni", "Settings saved successfully!": "Impostazioni salvate con successo!", "Share": "Condi<PERSON><PERSON>", "Share Chat": "<PERSON><PERSON><PERSON><PERSON> chat", "Share to Open WebUI Community": "Condividi con la comunità OpenWebUI", "Sharing Permissions": "Condivisione dei permessi", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Mostra", "Show \"What's New\" modal on login": "Mostra il modulo \"Novità\" al login", "Show Admin Details in Account Pending Overlay": "Mostra i dettagli dell'amministratore nella sovrapposizione dell'account in attesa", "Show All": "<PERSON><PERSON>", "Show image preview": "", "Show Less": "<PERSON><PERSON>", "Show Model": "<PERSON><PERSON>", "Show shortcuts": "<PERSON>ra scorciatoie", "Show your support!": "Mostra il tuo supporto!", "Showcased creativity": "Creatività messa in mostra", "Sign in": "Accedi", "Sign in to {{WEBUI_NAME}}": "Accedi a {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Accedi a {{WEBUI_NAME}} con LDAP", "Sign Out": "<PERSON><PERSON><PERSON>", "Sign up": "Registrati", "Sign up to {{WEBUI_NAME}}": "Registrati a {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Accedi a {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "Salta cache", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "sID per Songou Search API", "Sougou Search API SK": "SK per Songou Search API", "Source": "Fonte", "Speech Playback Speed": "Velocità di riproduzione vocale", "Speech recognition error: {{error}}": "Errore di riconoscimento vocale: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Motore da voce a testo", "Stop": "<PERSON><PERSON><PERSON>", "Stop Generating": "Ferma generazione", "Stop Sequence": "Sequenza di arresto", "Stream Chat Response": "Stream risposta chat", "Strikethrough": "", "Strip Existing OCR": "Rimuovi OCR esistente", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Rimuovi il testo OCR esistente dal PDF e rilancia OCR. Ignorato se Forza OCR è attivo. Predefinito a Falso", "STT Model": "Modello STT", "STT Settings": "Impostazioni STT", "Stylized PDF Export": "Esportazione PDF Stilizzata", "Subtitle (e.g. about the Roman Empire)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ad esempio sull'Imper<PERSON> Romano)", "Success": "Successo", "Successfully updated.": "Aggiornato con successo.", "Suggested": "<PERSON><PERSON><PERSON>", "Support": "Supporto", "Support this plugin:": "Supporta questo plugin:", "Supported MIME Types": "", "Sync directory": "Sincronizza directory", "System": "Sistema", "System Instructions": "Istruzioni di sistema", "System Prompt": "Prompt di sistema", "Tags": "Tag", "Tags Generation": "Generazione tag", "Tags Generation Prompt": "Prompt di generazione dei tag", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Il campionamento tail free viene utilizzato per ridurre l'impatto di token meno probabili dall'output. Un valore più alto (ad esempio, 2.0) ridurrà maggiormente l'impatto, mentre un valore di 1.0 disabilita questa impostazione.", "Talk to model": "Parla al modello", "Tap to interrupt": "Tocca per interrompere", "Task List": "", "Task Model": "Modello Task", "Tasks": "Attività", "Tavily API Key": "Chiave API Tavily", "Tavily Extract Depth": "Profondita' di estrazione Tavily", "Tell us more:": "Raccontaci di più:", "Temperature": "Temperatura", "Temporary Chat": "Chat temporanea", "Text Splitter": "Divisore di testo", "Text-to-Speech": "", "Text-to-Speech Engine": "Motore da testo a voce", "Thanks for your feedback!": "Grazie per il tuo feedback!", "The Application Account DN you bind with for search": "L'account dell'applicazione DN con cui ti colleghi per la ricerca", "The base to search for users": "La base da cercare per gli utenti", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "La dimensione del batch determina quanti richieste di testo vengono elaborate insieme in una sola volta. Una dimensione del batch più alta può aumentare le prestazioni e la velocità del modello, ma richiede anche più memoria.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Gli sviluppatori dietro questo plugin sono volontari appassionati della comunità. Se trovi utile questo plugin, ti preghiamo di considerare di contribuire al suo sviluppo.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "La classifica di valutazione è basata sul sistema di rating Elo ed è aggiornata in tempo reale.", "The format to return a response in. Format can be json or a JSON schema.": "Il formato per ritornare una risposta. Il formato può essere un JSON o uno schema JSON.", "The height in pixels to compress images to. Leave empty for no compression.": "L'altezza in pixel per comprimere le immagini. <PERSON><PERSON> vuoto per nessuna compressione.", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "Il linguaggio degli input audio. Fornire la lingua in formato ISO-639-1 (es: en) migliorerà la accuratezza e la latenza. Lascia vuoto per per riconoscere il linguaggio in automatico.", "The LDAP attribute that maps to the mail that users use to sign in.": "L'attributo LDAP che mappa alla mail che gli utenti usano per accedere.", "The LDAP attribute that maps to the username that users use to sign in.": "L'attributo LDAP che mappa al nome utente che gli utenti usano per accedere.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "La classifica è attualmente in beta e potremmo regolare i calcoli dei punteggi mentre perfezioniamo l'algoritmo.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "La dimensione massima del file in MB. Se la dimensione del file supera questo limite, il file non verrà caricato.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "La dimensione massima del numero di file che possono essere utilizzati contemporaneamente nella chat. Se il numero di file supera questo limite, i file non verranno caricati.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Il formato di output per il testo. Può essere 'json', 'markdown', o 'html'. Predefinito 'markdown'.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Il punteggio dovrebbe essere un valore compreso tra 0.0 (0%) e 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "La temperatura del modello. Aumentare la temperatura farà sì che il modello risponda in modo più creativo.", "The width in pixels to compress images to. Leave empty for no compression.": "La larghezza in pixel per comprimere le immagini. <PERSON><PERSON> vuoto per nessuna compressione.", "Theme": "<PERSON><PERSON>", "Thinking...": "Sto pensando...", "This action cannot be undone. Do you wish to continue?": "Questa azione non può essere annullata. Vuoi continuare?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Questo canale è stato creato il {{createdAt}}. Questo è l'inizio del canale {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Questa chat non apparirà nella cronologia e i tuoi messaggi non verranno salvati.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON><PERSON> garan<PERSON>ce che le tue preziose conversazioni siano salvate in modo sicuro nel tuo database backend. Grazie!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Questa è una funzionalità sperimentale, potrebbe non funzionare come previsto ed è soggetta a modifiche in qualsiasi momento.", "This model is not publicly available. Please select another model.": "Questo modello non è disponibile pubblicamente. Seleziona un altro modello.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "Questa opzione controlla quanto a lungo il modello rimarrà in memoria seguendo la richiesta (predefinito: 5m)", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Questa opzione controlla quanti token vengono preservati quando si aggiorna il contesto. Ad esempio, se impostato su 2, gli ultimi 2 token del contesto della conversazione verranno mantenuti. Preservare il contesto può aiutare a mantenere la continuità di una conversazione, ma potrebbe ridurre la capacità di rispondere a nuovi argomenti.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "Questa opzione abilita o disabilita l'utilizzo della funzionalità di ragionamento in Ollama, che consente al modello di riflettere prima di generare una risposta. Quando abilitata, il modello può impiegare un po' di tempo per elaborare il contesto della conversazione e generare una risposta più elaborata.", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Questa opzione imposta il numero massimo di token che il modello può generare nella sua risposta. Aumentare questo limite consente al modello di fornire risposte più lunghe, ma potrebbe anche aumentare la probabilità che vengano generati contenuti non utili o irrilevanti.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Questa opzione eliminerà tutti i file esistenti nella collezione e li sostituirà con i file appena caricati.", "This response was generated by \"{{model}}\"": "Questa risposta è stata generata da \"{{model}}\"", "This will delete": "Questa opzione eliminerà", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Questa opzione eliminerà <strong>{{NAME}}</strong> e <strong>tutti i suoi contenuti</strong>.", "This will delete all models including custom models": "Questa opzione eliminerà tutti i modelli, compresi i modelli personalizzati", "This will delete all models including custom models and cannot be undone.": "Questa opzione eliminerà tutti i modelli, compresi i modelli personalizzati e non può essere annullata.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Questa opzione ripristinerà la base di conoscenza e sincronizzerà tutti i file. Vuoi continuare?", "Thorough explanation": "Spiegazione dettagliata", "Thought for {{DURATION}}": "Pensiero per {{DURATION}}", "Thought for {{DURATION}} seconds": "Pensiero per {{DURATION}} secondi", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "L'URL del server Tika è obbligatorio.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Suggerimento: aggiorna più slot di variabili consecutivamente premendo il tasto tab nell'input della chat dopo ogni sostituzione.", "Title": "<PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON> (ad esempio Dimmi un fatto divertente)", "Title Auto-Generation": "Generazione automatica del titolo", "Title cannot be an empty string.": "Il titolo non può essere una stringa vuota.", "Title Generation": "Generazione del titolo", "Title Generation Prompt": "Prompt di generazione del titolo", "TLS": "TLS", "To access the available model names for downloading,": "Per accedere ai nomi dei modelli disponibili per il download,", "To access the GGUF models available for downloading,": "Per accedere ai modelli GGUF disponibili per il download,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Per accedere a WebUI, contatta l'amministratore. Gli amministratori possono gestire gli stati degli utenti dal pannello di amministrazione.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Per allegare la base di conoscenza qui, aggiungili prima allo spazio di lavoro \"Conoscenza\".", "To learn more about available endpoints, visit our documentation.": "Per saperne di più sugli endpoint disponibili, visita la nostra documentazione.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Per proteggere la tua privacy, solo le valutazioni, gli ID dei modelli, i tag e i metadati vengono condivisi dal tuo feedback: i registri della chat rimangono privati e non sono inclusi.", "To select actions here, add them to the \"Functions\" workspace first.": "Per selezionare le azioni qui, aggiungile prima allo spazio di lavoro \"Funzioni\".", "To select filters here, add them to the \"Functions\" workspace first.": "Per selezionare i filtri qui, aggiungili prima allo spazio di lavoro \"Funzioni\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Per selezionare i toolkit qui, aggiungili prima allo spazio di lavoro \"Strumenti\".", "Toast notifications for new updates": "Notifiche toast per nuovi aggiornamenti", "Today": "<PERSON><PERSON><PERSON>", "Toggle search": "Attiva/disattiva la ricerca", "Toggle settings": "Attiva/disattiva impostazioni", "Toggle sidebar": "Attiva/disattiva barra laterale", "Toggle whether current connection is active.": "Attiva/disattiva la connessione attuale quando è attiva", "Token": "Token", "Too verbose": "<PERSON><PERSON><PERSON>", "Tool created successfully": "Strumento creato con successo", "Tool deleted successfully": "Strumento eliminato con successo", "Tool Description": "Descrizione dello Strumento", "Tool ID": "ID Strumento", "Tool imported successfully": "Strumento importato con successo", "Tool Name": "Nome dello Strumento", "Tool Servers": "Server degli <PERSON>", "Tool updated successfully": "Strumento aggiornato con successo", "Tools": "Strumenti", "Tools Access": "Accesso agli Strumento", "Tools are a function calling system with arbitrary code execution": "Gli strumenti sono un sistema di chiamata di funzioni con esecuzione di codice arbitrario", "Tools Function Calling Prompt": "Prompt di Chiamata Funzione dei Strumenti", "Tools have a function calling system that allows arbitrary code execution.": "Gli strumenti hanno un sistema di chiamata di funzione che consente l'esecuzione di codice arbitrario.", "Tools Public Sharing": "Condivisione Pubblica degli strumenti", "Top K": "Top K", "Top K Reranker": "<PERSON><PERSON><PERSON>", "Transformers": "Transformer", "Trouble accessing Ollama?": "Problemi di accesso a Ollama?", "Trust Proxy Environment": "Fidati dell'Ambiente Proxy", "TTS Model": "Modello TTS", "TTS Settings": "Impostazioni TTS", "TTS Voice": "Voce TTS", "Type": "Digitare", "Type Hugging Face Resolve (Download) URL": "Digita URL di Risoluzione (Download) di Hugging Face", "Uh-oh! There was an issue with the response.": "Oh-oh! C'è stato un problema con la risposta.", "UI": "UI", "Unarchive All": "<PERSON><PERSON><PERSON><PERSON>", "Unarchive All Archived Chats": "Disarchivia Tutte le Chat Archiviate", "Unarchive Chat": "Disarchivia <PERSON>", "Underline": "", "Unloads {{FROM_NOW}}": "Scarica {{FROM_NOW}}", "Unlock mysteries": "<PERSON><PERSON><PERSON><PERSON> misteri", "Unpin": "<PERSON><PERSON><PERSON><PERSON> fissato", "Unravel secrets": "<PERSON><PERSON><PERSON> segreti", "Unsupported file type.": "", "Untagged": "Non taggato", "Untitled": "<PERSON>za titolo", "Update": "Aggiorna", "Update and Copy Link": "Aggiorna e Copia Link", "Update for the latest features and improvements.": "Aggiorna per le ultime funzionalità e miglioramenti.", "Update password": "Aggiorna password", "Updated": "Aggiornato", "Updated at": "Aggiornato il", "Updated At": "Aggiornato Il", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Aggiorna a un piano con licenza per funzionalità avanzate, tra cui personalizzazione del tema e branding, e supporto dedicato.", "Upload": "Carica", "Upload a GGUF model": "Carica un modello GGUF", "Upload Audio": "Carica Audio", "Upload directory": "Carica cartella", "Upload files": "Carica file", "Upload Files": "Carica File", "Upload Pipeline": "Carica <PERSON>", "Upload Progress": "Avanzamento Caricamento", "URL": "URL", "URL Mode": "Modalità URL", "Usage": "<PERSON><PERSON><PERSON><PERSON>", "Use '#' in the prompt input to load and include your knowledge.": "Usa '#' nell'input del prompt per caricare e includere la tua conoscenza.", "Use Gravatar": "<PERSON><PERSON> Gravatar", "Use groups to group your users and assign permissions.": "Usa i gruppi per raggruppare i tuoi utenti e assegnare permessi.", "Use Initials": "<PERSON>a iniz<PERSON>i", "Use LLM": "Utilizza LLM", "Use no proxy to fetch page contents.": "Usa nessun proxy per recuperare i contenuti della pagina.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Usa il proxy designato dalle variabili di ambiente http_proxy e https_proxy per recuperare i contenuti della pagina.", "user": "utente", "User": "Utente", "User location successfully retrieved.": "Posizione utente recuperata con successo.", "User menu": "", "User Webhooks": "Webhook Utente", "Username": "Nome Utente", "Users": "<PERSON><PERSON><PERSON>", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Utiliz<PERSON>do il modello di arena predefinito con tutti i modelli. Fai clic sul pulsante più per aggiungere modelli personalizzati.", "Valid time units:": "Unità di tempo valide:", "Valves": "Val<PERSON><PERSON>", "Valves updated": "Valvole aggiornate", "Valves updated successfully": "Valvole aggiornate con successo", "variable": "variabile", "Verify Connection": "Verifica connessione", "Verify SSL Certificate": "Verifica certificato SSL", "Version": "Versione", "Version {{selectedVersion}} of {{totalVersions}}": "Versione {{selectedVersion}} di {{totalVersions}}", "View Replies": "Visualizza Risposte", "View Result from **{{NAME}}**": "Visualizza risultato da **{{NAME}}**", "Visibility": "Visibilità", "Vision": "", "Voice": "Voce", "Voice Input": "Input vocale", "Voice mode": "Modalità vocale", "Warning": "Attenzione", "Warning:": "Attenzione:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Attenzione: abilita<PERSON> questo, gli utenti potranno caricare codice arbitrario sul server.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Attenzione: se aggiorni o cambi il tuo modello di embedding, dovrai reimportare tutti i documenti.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Attenzione: l'esecuzione di Jupyter consente l'esecuzione di codice arbitrario, comportando gravi rischi per la sicurezza: procedere con estrema cautela.", "Web": "Web", "Web API": "API Web", "Web Loader Engine": "Motore di Caricamento Web", "Web Search": "Ricerca sul Web", "Web Search Engine": "Motore di Ricerca Web", "Web Search in Chat": "Ricerca Web in chat", "Web Search Query Generation": "Generazione di query di ricerca Web", "Webhook URL": "URL webhook", "WebUI Settings": "Impostazioni WebUI", "WebUI URL": "URL WebUI", "WebUI will make requests to \"{{url}}\"": "WebUI farà richieste a \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI farà richieste a \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI farà richieste a \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "Peso del BM25 Retrieval", "What are you trying to achieve?": "Cosa stai cercando di o<PERSON>ere?", "What are you working on?": "Su cosa stai lavorando?", "What's New in": "Novità in", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON>uando abilitato, il modello risponderà a ciascun messaggio della chat in tempo reale, generando una risposta non appena l'utente invia un messaggio. Questa modalità è utile per le applicazioni di chat dal vivo, ma potrebbe influire sulle prestazioni su hardware più lento.", "wherever you are": "Ovunque tu sia", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Specifica se paginare l'output. Ogni pagina sarà separata da una riga orizzontale e dal numero di pagina. Predefinito è Falso.", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Locale)", "Why?": "Perché?", "Widescreen Mode": "Modalità widescreen", "Won": "Vin<PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Lavora insieme a top-k. Un valore più alto (ad esempio, 0,95) porterà a un testo più vario, mentre un valore più basso (ad esempio, 0,5) genererà un testo più focalizzato e conservativo.", "Workspace": "Spazio di lavoro", "Workspace Permissions": "Permessi dello spazio di lavoro", "Write": "<PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "Scrivi un suggerimento per il prompt (ad esempio Chi sei?)", "Write a summary in 50 words that summarizes [topic or keyword].": "<PERSON><PERSON><PERSON> un riassunto in 50 parole che riassume [argomento o parola chiave].", "Write something...": "<PERSON><PERSON>vi qualcosa...", "Yacy Instance URL": "URL dell'istanza Yacy", "Yacy Password": "Password Yacy", "Yacy Username": "Nome utente Yacy", "Yesterday": "<PERSON><PERSON>", "You": "Tu", "You are currently using a trial license. Please contact support to upgrade your license.": "Stai attualmente utilizzando una licenza di prova. Contatta il supporto per aggiornare la tua licenza.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON><PERSON>i chattare solo con un massimo di {{maxCount}} file alla volta.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Puoi personalizzare le tue interazioni con LLM aggiungendo memorie tramite il pulsante 'Gestisci' qui sotto, rendendole più utili e su misura per te.", "You cannot upload an empty file.": "Non puoi caricare un file vuoto.", "You do not have permission to upload files.": "Non hai il permesso di caricare file.", "You have no archived conversations.": "Non hai conversazioni archiviate.", "You have shared this chat": "Hai condiviso questa chat", "You're a helpful assistant.": "Sei un assistente utile.", "You're now logged in.": "Ora hai effettuato l'accesso.", "Your account status is currently pending activation.": "Lo stato del tuo account è attualmente in attesa di attivazione.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Il tuo intero contributo andrà direttamente allo sviluppatore del plugin; Open WebUI non prende alcuna percentuale. Tuttavia, la piattaforma di finanziamento scelta potrebbe avere le proprie commissioni.", "Youtube": "Youtube", "Youtube Language": "Lingua Youtube", "Youtube Proxy URL": "URL proxy Youtube"}