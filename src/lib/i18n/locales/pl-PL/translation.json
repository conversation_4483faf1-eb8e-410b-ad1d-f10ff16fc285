{"-1 for no limit, or a positive integer for a specific limit": "-1 oznacza brak limitu, lub dodatnia liczba całkowita dla konkretnego limitu", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' lub '-1' dla braku wygaś<PERSON>ę<PERSON>.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(np. `sh webui.sh --api --api-auth username_password`)>", "(e.g. `sh webui.sh --api`)": "(np. `sh webui.sh --api`)", "(latest)": "(na<PERSON><PERSON><PERSON>)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ modele }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} o<PERSON><PERSON><PERSON><PERSON><PERSON>", "{{COUNT}} words": "", "{{user}}'s Chats": "<PERSON><PERSON><PERSON> użytkownika {{user}}", "{{webUIName}} Backend Required": "Backend dla {{webUIName}} jest wymagany", "*Prompt node ID(s) are required for image generation": "Wymagane są identyfikatory węzłów wyzwalających do generowania obrazów.", "A new version (v{{LATEST_VERSION}}) is now available.": "Do<PERSON><PERSON><PERSON><PERSON> jest nowa wersja (v{{LATEST_VERSION}}).", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model zada<PERSON> jest wykorzystywany podczas realizacji zadań, takich jak generowanie tytułów rozmów i zapytań wyszukiwania internetowego.", "a user": "użytkownik", "About": "O nas", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "Dostę<PERSON>", "Access Control": "<PERSON><PERSON><PERSON><PERSON>", "Accessible to all users": "Dostępny dla wszystkich użytkowników", "Account": "Ko<PERSON>", "Account Activation Pending": "Aktywacja konta w toku", "Accurate information": "Precyzyjna informacja", "Action": "", "Actions": "<PERSON><PERSON><PERSON><PERSON>", "Activate": "Włącz", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktywuj tę komendę, wpisując \"/{{COMMAND}}\" do pola wprowadzania czatu.", "Active Users": "Aktywni użytkownicy", "Add": "<PERSON><PERSON><PERSON>", "Add a model ID": "Dodaj identyfikator modelu", "Add a short description about what this model does": "Dodaj krótki opis działania tego modelu", "Add a tag": "<PERSON><PERSON><PERSON> tag", "Add Arena Model": "Dodaj Model Arena", "Add Connection": "Dodaj połączenie", "Add Content": "<PERSON><PERSON><PERSON>", "Add content here": "<PERSON><PERSON><PERSON> tutaj t<PERSON>", "Add Custom Parameter": "", "Add custom prompt": "<PERSON><PERSON><PERSON> pole<PERSON>", "Add Files": "<PERSON><PERSON>j pliki", "Add Group": "<PERSON><PERSON><PERSON>", "Add Memory": "<PERSON><PERSON><PERSON>", "Add Model": "Dodaj model", "Add Reaction": "<PERSON><PERSON><PERSON>", "Add Tag": "<PERSON><PERSON><PERSON> tag", "Add Tags": "<PERSON><PERSON><PERSON> tagi", "Add text content": "<PERSON><PERSON><PERSON> teksto<PERSON>", "Add User": "Dodaj użytkownika", "Add User Group": "Dodaj grupę użytkowników", "Adjusting these settings will apply changes universally to all users.": "Dostosowanie tych ustawień spowoduje wprowadzenie zmian dla wszystkich użytkowników.", "admin": "administrator", "Admin": "Administrator", "Admin Panel": "Panel administracyjny", "Admin Settings": "Ustawienia administratora", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "<PERSON><PERSON> mają dostęp do wszystkich narzędzi przez cały czas; użytkownicy muszą mieć przydzielone narzędzia dla każdego modelu w przestrzeni roboczej.", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AI": "", "All": "Wszystkie", "All Documents": "Wszystkie dokumenty", "All models deleted successfully": "Wszystkie modele zostały usunięte pomyślnie.", "Allow Call": "", "Allow Chat Controls": "Zezwól na dostęp do ustawień czatu", "Allow Chat Delete": "Zezwól na usunięcie czatu", "Allow Chat Deletion": "Zezwól na usuwanie czatu", "Allow Chat Edit": "Zezwól na edycję czatu", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "Pozwól na przesyłanie plików", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Pozwól na głosy spoza lokalnej społeczności", "Allow Speech to Text": "", "Allow Temporary Chat": "Zezwól na tymczasową rozmowę", "Allow Text to Speech": "", "Allow User Location": "Zezwól na lokalizację użytkownika", "Allow Voice Interruption in Call": "Zezwól na przerwanie połączenia głosowego", "Allowed Endpoints": "Dozwolone punkty końcowe", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "<PERSON><PERSON> masz już konto?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "<PERSON><PERSON><PERSON>", "Always Collapse Code Blocks": "Zawsze zwijaj bloki kodu", "Always Expand Details": "Zawsze rozwiń szczegóły", "Always Play Notification Sound": "", "Amazing": "Niesamowite", "an assistant": "asystent", "Analyzed": "Przeanalizowane", "Analyzing...": "Analizowanie...", "and": "oraz", "and {{COUNT}} more": "i {{COUNT}} wi<PERSON><PERSON>j", "and create a new shared link.": "i utwórz nowy link współdzielony.", "Android": "", "API": "", "API Base URL": "Adres bazowy interfejsu API", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "Klucz API", "API Key created.": "Klucz API został utworzony.", "API Key Endpoint Restrictions": "Ograniczenia punktu końcowego klucza API", "API keys": "Klucze API", "API Version": "", "Application DN": "Konto techniczne - Format DN", "Application DN Password": "Hasło do konta technicznego", "applies to all users with the \"user\" role": "dotyczy wszystkich użytkowników z rolą \"user\"", "April": "Kwiecień", "Archive": "Archiwu<PERSON>", "Archive All Chats": "Archiwizuj wszystkie rozmowy", "Archived Chats": "Zarchiwizowane rozmowy", "archived-chat-export": "archiwizowany eksport czatu", "Are you sure you want to clear all memories? This action cannot be undone.": "Czy na pewno chcesz wyczyścić wszystkie wspomnienia? Tej akcji nie można cofnąć.", "Are you sure you want to delete this channel?": "<PERSON>zy na pewno chcesz usunąć ten kanał?", "Are you sure you want to delete this message?": "<PERSON>zy na pewno chcesz usunąć tę wiadomość?", "Are you sure you want to unarchive all archived chats?": "Czy na pewno chcesz przywrócić wszystkie zapisane rozmowy?", "Are you sure?": "<PERSON><PERSON> j<PERSON> p<PERSON>?", "Arena Models": "<PERSON><PERSON>", "Artifacts": "Artefakty", "Ask": "", "Ask a question": "<PERSON><PERSON><PERSON>", "Assistant": "Asystent", "Attach file from knowledge": "", "Attention to detail": "Dbałość o szczegóły", "Attribute for Mail": "Atrybut dla poczty", "Attribute for Username": "Atrybut dla nazwy użytkownika", "Audio": "Dźwięk", "August": "Sierpień", "Auth": "", "Authenticate": "<PERSON><PERSON><PERSON><PERSON>", "Authentication": "Uwierzytelnianie", "Auto": "", "Auto-Copy Response to Clipboard": "Automatyczne kopiowanie odpowiedzi do schowka", "Auto-playback response": "Automatyczna odpowiedź na powtórzenie", "Autocomplete Generation": "Generowanie autouzupełniania", "Autocomplete Generation Input Max Length": "Maks<PERSON><PERSON><PERSON> dł<PERSON> wejścia dla generowania autouzupełniania", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Automatic1111 - ciąg uwierzytelniania API", "AUTOMATIC1111 Base URL": "Automatic1111 - Domyślny adres URL", "AUTOMATIC1111 Base URL is required.": "Automatic1111 - <PERSON><PERSON> jest wymagany.", "Available list": "Dostępna lista", "Available Tools": "", "available!": "dostę<PERSON>ny!", "Awful": "Okropne", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Region Azure", "Back": "Wstecz", "Bad Response": "Nieprawidłowa odpowiedź", "Banners": "<PERSON><PERSON>", "Base Model (From)": "Model bazowy (od)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "przed", "Being lazy": "<PERSON><PERSON> leni<PERSON>.", "Beta": "Beta", "Bing Search V7 Endpoint": "Wyszukiwarka Bing V7 Endpoint", "Bing Search V7 Subscription Key": "Klucz subskrypcji Wyszukiwarki Bing V7", "Bocha Search API Key": "Klucz API Bocha Search", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Klucz API wyszukiwania Brave", "Bullet List": "", "By {{name}}": "<PERSON><PERSON><PERSON> {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "Kalendarz", "Call": "Rozmowa", "Call feature is not supported when using Web STT engine": "Funkcja rozmowy nie jest obsługiwana podczas korzystania z silnika Web STT", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON><PERSON>ści", "Capture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capture Audio": "", "Certificate Path": "Ścieżka certyfikatu", "Change Password": "<PERSON><PERSON><PERSON> hasło", "Channel Name": "Nazwa kanału", "Channels": "Kanały", "Character": "Znak", "Character limit for autocomplete generation input": "Limit znaków dla wejścia generowanego automatycznie", "Chart new frontiers": "Odkrywaj nowe horyzonty", "Chat": "<PERSON><PERSON><PERSON>", "Chat Background Image": "<PERSON><PERSON><PERSON> tła c<PERSON>tu", "Chat Bubble UI": "Okno dialogowe czatu", "Chat Controls": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "Chat direction": "Kierunek rozmowy czatu", "Chat Overview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Permissions": "Uprawnienia do czatu", "Chat Tags Auto-Generation": "Automatyczne generowanie tagów czatu", "Chats": "<PERSON><PERSON><PERSON>", "Check Again": "Sprawdź ponownie", "Check for updates": "Sprawdź dostępność aktualizacji", "Checking for updates...": "Sprawdzanie dostępności aktualizacji...", "Choose a model before saving...": "Wybierz model przed zapisaniem.", "Chunk Overlap": "Nakładanie się bloków", "Chunk Size": "Roz<PERSON>r bloku", "Ciphers": "Szy<PERSON><PERSON>", "Citation": "Cytat", "Citations": "", "Clear memory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Clear Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "click here": "k<PERSON><PERSON><PERSON> tutaj", "Click here for filter guides.": "Kliknij tutaj, aby uzyskać podpowiedź do filtrów.", "Click here for help.": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby uzyskać pomoc.", "Click here to": "Klik<PERSON>j tutaj, aby przej<PERSON> do", "Click here to download user import template file.": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, aby pobrać szablon pliku importu użytkownika.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, aby do<PERSON><PERSON><PERSON> się więcej o faster-whisper i zobaczyć dostępne modele.", "Click here to see available models.": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, a<PERSON> z<PERSON><PERSON><PERSON> dostępne modele.", "Click here to select": "<PERSON><PERSON><PERSON><PERSON> tutaj, a<PERSON> w<PERSON><PERSON>", "Click here to select a csv file.": "K<PERSON><PERSON><PERSON> tutaj, aby wybrać plik CSV.", "Click here to select a py file.": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby wy<PERSON><PERSON> plik py.", "Click here to upload a workflow.json file.": "K<PERSON><PERSON><PERSON> tutaj, aby prz<PERSON><PERSON> plik workflow.json.", "click here.": "k<PERSON><PERSON><PERSON> tutaj.", "Click on the user role button to change a user's role.": "Kliknij przycisk roli użytkownika, aby zmienić jego uprawnienia.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Nie można było skopiować do schowka. Sprawdź ustawienia przeglądarki, aby prz<PERSON><PERSON><PERSON><PERSON> wymagany dostęp.", "Clone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clone Chat": "Sk<PERSON><PERSON>j c<PERSON>t", "Clone of {{TITLE}}": "<PERSON>lon {{TITLE}}", "Close": "Zamknij", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "Wykonanie kodu", "Code Execution": "Wykonanie kodu", "Code Execution Engine": "Silnik wykonawczy kodu", "Code Execution Timeout": "Limit czasu wykonywania kodu", "Code formatted successfully": "Kod został sformatowany pomyślnie.", "Code Interpreter": "Interpreter kodu", "Code Interpreter Engine": "Silnik interpretatora kodu", "Code Interpreter Prompt Template": "Szablon promptu interpretera kodu", "Collapse": "", "Collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Color": "<PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Klucz API ComfyUI", "ComfyUI Base URL": "Adres URL bazowy ComfyUI", "ComfyUI Base URL is required.": "Adres URL bazowy ComfyUI jest wymagany.", "ComfyUI Workflow": "Komfortowy przepływ pracy ComfyUI", "ComfyUI Workflow Nodes": "Komfortowe węzły przepływu pracy ComfyUI", "Command": "Polecenie", "Comment": "", "Completions": "Uzupełnienia", "Concurrent Requests": "Równoległe żądania", "Configure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm": "Potwierdź", "Confirm Password": "Potwierd<PERSON> hasło", "Confirm your action": "Potwierdź swoją akcję", "Confirm your new password": "Potwierdź nowe hasło", "Connect to your own OpenAI compatible API endpoints.": "Połącz się ze swoimi własnymi punktami końcowymi API kompatybilnego z OpenAI.", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Połączenia", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Skontaktuj się z <PERSON>em, aby uzyskać dostęp do WebUI.", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction Engine": "Silnik ekstrakcji treści", "Continue Response": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> od<PERSON>wiedź", "Continue with {{provider}}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> z {{provider}}", "Continue with Email": "Kontynuuj z e-mailem", "Continue with LDAP": "Kontynuuj z LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kont<PERSON><PERSON>j sposób dzielenia tekstu wiadomości dla żądań TTS. 'Punctuation' dzieli na zdania, 'paragraphs' dzieli na akapity, a 'none' pozostawia wiadomość jako pojedynczy ciąg znaków.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "Ustawienia", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Skopiowane", "Copied link to clipboard": "Skopiowano link do schowka", "Copied shared chat URL to clipboard!": "Skopiowano udostępniony URL czatu do schowka!", "Copied to clipboard": "Skopiowane do schowka", "Copy": "Skopiuj", "Copy Formatted Text": "Skopiuj sformatowany tekst", "Copy last code block": "Skopiuj ostatni fragment kodu", "Copy last response": "Skopiuj ostatnią wypowiedź", "Copy link": "Skopiuj link", "Copy Link": "Skopiuj link", "Copy to clipboard": "Wklej do schowka", "Copying to clipboard was successful!": "Kopiowanie do schowka zakończyło się sukcesem!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS musi być prawidłowo skonfigurowany przez dostawcę, aby umożliwić żądania z Open WebUI.", "Create": "Utwórz", "Create a knowledge base": "Utwórz bazę wiedzy", "Create a model": "Utwórz model", "Create Account": "Utwórz konto", "Create Admin Account": "Utwórz konto administratora", "Create Channel": "Utw<PERSON>rz kanał", "Create Folder": "", "Create Group": "Utwórz grupę", "Create Knowledge": "Utwórz wiedzę", "Create new key": "Utwórz nowy klucz", "Create new secret key": "Utwórz nowy secret key", "Create Note": "Utwórz notat<PERSON>ę", "Create your first note by clicking on the plus button below.": "Utwórz swoją pierwszą notatkę klikając w plus poniżej.", "Created at": "Utworzono o", "Created At": "Utworzono o", "Created by": "Stworzone przez", "CSV Import": "Import CSV", "Ctrl+Enter to Send": "Ctrl+Enter aby w<PERSON>", "Current Model": "Aktualny model", "Current Password": "<PERSON><PERSON><PERSON><PERSON>", "Custom": "Niestandardowy", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Ciemny", "Database": "<PERSON><PERSON> danych", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Grudzień", "Default": "Domyślny", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Tryb domyślny współpracuje z szerszym zakresem modeli, wywołując narzędzia raz przed wykonaniem. Tryb natywny wykorzystuje wbudowane możliwości wywoływania narzędzi przez model, ale wymaga, aby model wewnętrznie obsługiwał tę funkcję.", "Default Model": "<PERSON> do<PERSON>ślny", "Default model updated": "Domyślny model z<PERSON><PERSON><PERSON>", "Default Models": "Domyślne modele", "Default permissions": "Domyślne uprawnienia", "Default permissions updated successfully": "Domyślne uprawnienia zaktualizowane pomyślnie", "Default Prompt Suggestions": "Domyślne propozycje promptów", "Default to 389 or 636 if TLS is enabled": "Domyślnie użyj 389 lub 636, j<PERSON><PERSON><PERSON> TLS jest włączony", "Default to ALL": "Domyślne dla wszystkich", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Domyślna rola użytkownika", "Delete": "Usuń", "Delete a model": "Usuń model", "Delete All Chats": "Usuń wszystkie rozmowy", "Delete All Models": "Usuń Wszystkie Modele", "Delete chat": "Usuń rozmowę", "Delete Chat": "Usuń rozmowę", "Delete chat?": "<PERSON><PERSON><PERSON><PERSON> czat?", "Delete folder?": "Usunąć folder?", "Delete function?": "<PERSON>zy na pewno chcesz usunąć funkcję?", "Delete Message": "<PERSON><PERSON><PERSON> w<PERSON>dom<PERSON>", "Delete message?": "<PERSON><PERSON><PERSON><PERSON> wiadom<PERSON>?", "Delete note?": "<PERSON><PERSON><PERSON><PERSON> notatkę?", "Delete prompt?": "<PERSON><PERSON> ch<PERSON>z <PERSON> prompt?", "delete this link": "usuń to połączenie", "Delete tool?": "<PERSON><PERSON><PERSON>ć narzędzie?", "Delete User": "Usuń użytkownika", "Deleted {{deleteModelTag}}": "Usunieto {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON>ę<PERSON> użytkownika {{name}}", "Deleted User": "Usunięty użytkownik", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Opisz swoją bazę wiedzy i cele", "Description": "Opis", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Nie wykonał w pełni instrukcji", "Direct": "", "Direct Connections": "Połączenia bezpośrednie", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Połączenia bezpośrednie umożliwiają użytkownikom łączenie się z własnymi końcówkami API kompatybilnymi z OpenAI.", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Wyłączony", "Discover a function": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a model": "Odkrywaj model", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prompty", "Discover a tool": "Odkrywaj narzędzia", "Discover how to use Open WebUI and seek support from the community.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak k<PERSON> z Open WebUI i szukaj wsparcia w społeczności.", "Discover wonders": "Odk<PERSON><PERSON><PERSON> cuda", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pobierz i eksploruj niestandardowe funkcje", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pobierz i eksploruj niestandardowe prompty", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pobierz i eksploruj niestandardowe narzędzia", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pobierz i badaj ustawienia modeli", "Display": "Wyś<PERSON><PERSON>l", "Display Emoji in Call": "Wyświetl emoji w połączeniu", "Display the username instead of You in the Chat": "Wyświetl nazwę użytkownika zamiast 'You' w czacie.", "Displays citations in the response": "Wyświetla cytowania w odpowiedzi", "Dive into knowledge": "Zanurz się w wiedzy", "Do not install functions from sources you do not fully trust.": "Nie instaluj funkcji ze źródeł, którym nie ufasz w pełni.", "Do not install tools from sources you do not fully trust.": "Nie instaluj narzędzi ze źródeł, którym nie ufasz w pełni.", "Docling": "", "Docling Server URL required.": "", "Document": "Dokument", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Dokumentacja", "Documents": "Dokumenty", "does not make any external connections, and your data stays securely on your locally hosted server.": "nie nawią<PERSON>je żadnych zewnętrznych połączeń, a Twoje dane pozostają bezpiecznie na Twoim lokalnie hostowanym serwerze.", "Domain Filter List": "Lista filtrów domeny", "Don't have an account?": "Nie masz konta?", "don't install random functions from sources you don't trust.": "<PERSON>e instaluj losowych funkcji z niezaufanych źródeł.", "don't install random tools from sources you don't trust.": "Nie instaluj przypadkowych narzędzi z niezaufanych źródeł.", "Don't like the style": "Nie przypadł mi do gustu styl", "Done": "Wykonano", "Download": "Pobieranie", "Download as SVG": "Pobierz jako SVG", "Download canceled": "<PERSON><PERSON><PERSON><PERSON>", "Download Database": "Pobierz bazę danych", "Drag and drop a file to upload or select a file to view": "Przeciągnij i upuść plik, aby go przesłać lub wybierz plik, aby go wyświetlić.", "Draw": "<PERSON><PERSON><PERSON><PERSON>", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "np. '30s', '10m'. Poprawne jednostki czasu to: 's' (sekunda), 'm' (minuta), 'h' (<PERSON><PERSON><PERSON>).", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "np. Filtr do usuwania wulgaryzmów z tekstu", "e.g. en": "", "e.g. My Filter": "np. <PERSON><PERSON><PERSON> filtr", "e.g. My Tools": "np. <PERSON><PERSON> narzęd<PERSON>", "e.g. my_filter": "np. moj_filtr", "e.g. my_tools": "np. moje_narzędzia", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "np. Narzędzia do wykonywania różnych operacji", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Edyt<PERSON>j model arenę", "Edit Channel": "<PERSON><PERSON><PERSON><PERSON> kanał", "Edit Connection": "Edytuj połączenie", "Edit Default Permissions": "Edyt<PERSON>j domyślne uprawnienia", "Edit Folder": "<PERSON><PERSON><PERSON><PERSON> folder", "Edit Memory": "<PERSON><PERSON><PERSON><PERSON>", "Edit User": "Edytuj profil użytkownika", "Edit User Group": "Edytuj grupa użytkowników", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "Wyruszaj na przygody", "Embedding": "", "Embedding Batch Size": "Rozmiar partii wstępnego przetwarzania", "Embedding Model": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Embedding Model Engine": "Silnik modelu osadzania", "Embedding model set to \"{{embedding_model}}\"": "Model osadzania ustawiony na '{{embedding_model}}'", "Enable API Key": "Włącz klucz API", "Enable autocomplete generation for chat messages": "Włącz generowanie autouzupełniania dla wiadomości czatu", "Enable Code Execution": "Włącz wykonywanie kodu", "Enable Code Interpreter": "Włącz interpreter kodu", "Enable Community Sharing": "Włączanie udostępniania społecznościowego", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Włącz blokowanie <PERSON> (mlock), aby zap<PERSON><PERSON><PERSON> swappingowi danych modelu z RAM. Ta opcja blokuje zbiór stron roboczych modelu w RAM, co gwarantuje, że nie będą one wymieniane na dysk. <PERSON>że to pomóc w utrzymaniu wydajności poprzez unikanie błędów strony i zapewnienie szybkiego dostępu do danych.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Włącz mapowanie pamięci (mmap), aby z<PERSON><PERSON><PERSON><PERSON><PERSON> dane modelu. Ta opcja pozwala systemowi traktować pliki dysku jako rozszerzenie RAM, co może poprawić wydajność modelu przez umożliwienie szybszego dostępu do danych. Należy jednak pami<PERSON>, że ta funkcja może nie działać poprawnie ze wszystkimi systemami i zużywać znaczną ilość przestrzeni dyskowej.", "Enable Message Rating": "Włącz ocenianie wiadomości", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Włącz nowe rejestracje", "Enabled": "Włą<PERSON><PERSON>", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Upew<PERSON>j <PERSON>ę, że twój plik CSV zawiera dokładnie 4 kolumny w następującej kolejności: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Rola.", "Enter {{role}} message here": "Wprowadź komunikat dla {{role}} tutaj", "Enter a detail about yourself for your LLMs to recall": "Podaj informacje o sobie, aby LLMs mogły je przypomnieć.", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Wprowadź ciąg uwierzytelniania API (np. nazwa użytkownika:hasło)", "Enter Application DN": "Wprowadź nazwę konta technicznego - Format DN", "Enter Application DN Password": "Wprowadź hasło do konta technicznego", "Enter Bing Search V7 Endpoint": "Wprowadź endpoint wyszukiwania Bing V7", "Enter Bing Search V7 Subscription Key": "Wprowadź klucz subskrypcji Bing Search V7", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "Wprowadź klucz API Bocha Search", "Enter Brave Search API Key": "Wprowadź klucz API Brave Search", "Enter certificate path": "Wprowadź ścieżkę do certyfikatu", "Enter CFG Scale (e.g. 7.0)": "Wprowadź skalę CFG (np. 7.0)", "Enter Chunk Overlap": "Wprowadź nakładanie się bloków", "Enter Chunk Size": "Wprowadź wielkość bloku", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Wprowadź opis", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "Wprowadź domeny oddzielone przecinkami (np. example.com, site.org)", "Enter Exa API Key": "Wprowadź klucz API Exa", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "Wprowadź nazwę folderu", "Enter Github Raw URL": "Wprowadź surowy adres URL usługi GitHub", "Enter Google PSE API Key": "Wprowadź klucz API Google PSE", "Enter Google PSE Engine Id": "Wprowadź identyfikator urządzenia Google PSE", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON> (np. 512x512)", "Enter Jina API Key": "Wprowadź klucz API Jiny", "Enter Jupyter Password": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasł<PERSON>", "Enter Jupyter Token": "Wprowadź token Ju<PERSON>", "Enter Jupyter URL": "Podaj adres URL Jupytera", "Enter Kagi Search API Key": "Wprowadź klucz wyszukiwania Kagi", "Enter Key Behavior": "Zachowanie klawisza Enter", "Enter language codes": "Wprowadź kody języków", "Enter Mistral API Key": "", "Enter Model ID": "Wprowadź ID modelu", "Enter model tag (e.g. {{modelTag}})": "Wprowadź znacznik modelu (np. {{modelTag}})", "Enter Mojeek Search API Key": "Wprowadź klucz API Mojeek Search", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Podaj liczbę kroków (np. 50)", "Enter Perplexity API Key": "Klucz API Perplexity", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Podaj adres URL proxy (np. **************************:port)", "Enter reasoning effort": "Podaj powód wysiłku", "Enter Sampler (e.g. Euler a)": "W<PERSON>rowad<PERSON> sampler (np. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON><PERSON> harmonogram (np. <PERSON>)", "Enter Score": "Wprowadź wynik", "Enter SearchApi API Key": "Wprowadź klucz API SearchApi", "Enter SearchApi Engine": "Wprowadź zapytanie do silnika wyszukiwania SearchApi", "Enter Searxng Query URL": "Wprowadź adres URL zapytania wyszukiwania Searxng", "Enter Seed": "Wprowadź Seed", "Enter SerpApi API Key": "Wprowadź klucz API SerpApi", "Enter SerpApi Engine": "Wprowadź silnik SerpApi", "Enter Serper API Key": "Wprowadź klucz API Serper", "Enter Serply API Key": "Wprowadź klucz API Serply", "Enter Serpstack API Key": "Wprowadź klucz API Serpstack", "Enter server host": "Wprowadź nazwę hosta serwera", "Enter server label": "Wprowadź etykietę serwera", "Enter server port": "Wprowadź numer portu serwera", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekwencję stop", "Enter system prompt": "Wprowadź prompt systemowy", "Enter system prompt here": "", "Enter Tavily API Key": "Wprowadź klucz API Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Wprowadź publiczny adres URL Twojego WebUI. Ten adres URL zostanie użyty do generowania linków w powiadomieniach.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Wprowadź adres URL serwera Tika", "Enter timeout in seconds": "", "Enter to Send": "<PERSON>ter a<PERSON> w<PERSON>", "Enter Top K": "W<PERSON><PERSON><PERSON>ź {Top K}", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Podaj adres URL (np. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Wprowadź adres URL (np. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "Wprowadź swoje aktualne hasło", "Enter Your Email": "Podaj swój adres e-mail", "Enter Your Full Name": "Podaj swoje pełne imię i nazwisko", "Enter your message": "Wprowadź swój komunikat", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "Wprowadź nowe hasło", "Enter Your Password": "Wprowadź swoje hasło", "Enter Your Role": "Podaj swoją rolę", "Enter Your Username": "Podaj swoją nazwę użytkownika", "Enter your webhook URL": "Podaj adres URL swojego webhooka", "Error": "Błąd", "ERROR": "BŁĄD", "Error accessing Google Drive: {{error}}": "<PERSON><PERSON><PERSON><PERSON> podczas dostępu do Google Drive: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Błąd podczas przesyłania pliku: {{error}}", "Evaluations": "<PERSON><PERSON><PERSON>", "Everyone": "Ka<PERSON><PERSON>", "Exa API Key": "Klucz API Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Przykład: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Przykład: ALL", "Example: mail": "Przykład: mail", "Example: ou=users,dc=foo,dc=example": "Przykład: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Przykład: sAMAccountName lub uid lub userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Przekroczono liczbę stanowisk w licencji. Skontaktuj się z pomocą techniczną, aby zwięks<PERSON>ć liczbę stanowisk.", "Exclude": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Execute code for analysis": "Wykonaj kod do analizy", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Eksperymentalne", "Explain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "Odkrywaj kosmos", "Export": "Eksport", "Export All Archived Chats": "Wyeksportuj wszystkie archiwalne rozmowy", "Export All Chats (All Users)": "Eksportuj wszystkie rozmowy (wszyscy użytkownicy)", "Export chat (.json)": "Eksport czatu (.json)", "Export Chats": "Eksportuj rozmowy", "Export Config to JSON File": "Eksportuj konfigurację do pliku JSON", "Export Functions": "Funkcje eksportu", "Export Models": "Eksportuj modele", "Export Presets": "Wyeksportuj ustawienia domyślne", "Export Prompt Suggestions": "", "Export Prompts": "Eksportuj prompty", "Export to CSV": "Eksport do CSV", "Export Tools": "Eksportuj narzędzia", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "<PERSON>e udało się dodać pliku.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "<PERSON>e udało się skopiować linku", "Failed to create API Key.": "Nie udało się wygenerować klucza API.", "Failed to delete note": "<PERSON>e udało się usunąć notatki", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "<PERSON><PERSON> udało się pobrać modeli", "Failed to generate title": "<PERSON>e udało się wygenerować tytułu", "Failed to load chat preview": "", "Failed to load file content.": "Nie udało się załadować zawartości pliku.", "Failed to read clipboard contents": "Nie udało się odczytać zawartości schowka", "Failed to save connections": "<PERSON>e udałio się zapisać połączeń", "Failed to save models configuration": "<PERSON>e udało się zapisać konfiguracji modelu", "Failed to update settings": "Nie udało się zaktualizować ustawień", "Failed to upload file.": "<PERSON>e udało się przesłać pliku.", "Features": "<PERSON><PERSON><PERSON>", "Features Permissions": "Uprawnienia do funkcji", "February": "<PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Historia ocen", "Feedbacks": "<PERSON><PERSON><PERSON>", "Feel free to add specific details": "Nie krępuj się dodawać szczegółów", "File": "Plik", "File added successfully.": "Plik dodany pomyślnie.", "File content updated successfully.": "Plik został zaktualizowany pomyślnie.", "File Mode": "<PERSON><PERSON> pliku", "File not found.": "Plik nie został znaleziony.", "File removed successfully.": "Plik został usunięty pomyślnie.", "File size should not exceed {{maxSize}} MB.": "Rozmiar pliku nie powinien przekraczać {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "Plik został pomyślnie przesłany", "Files": "Pliki", "Filter": "Filtr", "Filter is now globally disabled": "Filtr jest teraz globalnie wyłączony", "Filter is now globally enabled": "Filtr jest teraz globalnie włączony", "Filters": "Filtry", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Wykryto próbę oszustwa z odciskiem palca: Nie można używać inicjałów jako awatara. Powrót do domyślnego obrazu profilowego.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Płynnie strumieniuj duże fragmenty odpowiedzi zewnętrznych", "Focus chat input": "Skup się na czacie", "Folder deleted successfully": "Folder został usunięty pomyślnie", "Folder Name": "<PERSON><PERSON><PERSON>", "Folder name cannot be empty.": "Nazwa folderu nie może być pusta.", "Folder name updated successfully": "Nazwa folderu została zaktualizowana pomyślnie", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Wykonał instrukcje idealnie", "Force OCR": "Wymuś OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Wytyczaj nowe ścieżki", "Form": "Formularz", "Format your variables using brackets like this:": "Sformatuj swoje zmienne, używając nawiasów w następujący sposób:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "Tryb pełnego kontekstu", "Function": "<PERSON><PERSON><PERSON>", "Function Calling": "Wywoływanie funkcji", "Function created successfully": "Funkcja utworzona pomyślnie", "Function deleted successfully": "Funkcja została usunięta pomyślnie", "Function Description": "<PERSON>is <PERSON>", "Function ID": "Funkcja ID", "Function imported successfully": "", "Function is now globally disabled": "Funkcja jest teraz globalnie wyłączona", "Function is now globally enabled": "Funkcja jest teraz globalnie włączona", "Function Name": "Nazwa <PERSON>", "Function updated successfully": "Funkcja została zaktualizowana pomyślnie", "Functions": "<PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution.": "Funkcje umożliwiają wykonanie dowolnego kodu.", "Functions imported successfully": "Funkcje zostały pomyślnie zaimportowane", "Gemini": "Gemini", "Gemini API Config": "Konfiguracja API Gemini", "Gemini API Key is required.": "Wymagany jest klucz API Gemini.", "General": "Ogólne", "Generate": "Wygeneruj", "Generate an image": "Wygeneruj obraz", "Generate Image": "Wygeneruj obraz", "Generate prompt pair": "", "Generating search query": "Tworzenie zapytania wyszukiwania", "Generating...": "Generowanie...", "Get information on {{name}} in the UI": "", "Get started": "Rozpocznij", "Get started with {{WEBUI_NAME}}": "Rozpocznij pracę z {{WEBUI_NAME}}", "Global": "Globalny", "Good Response": "Do<PERSON> odpowiedź", "Google Drive": "Dysk Google", "Google PSE API Key": "Klucz API Google PSE", "Google PSE Engine Id": "Identyfikator silnika Google PSE", "Group created successfully": "Grupa utworzona pomyślnie", "Group deleted successfully": "Grupa została usunięta pomyślnie", "Group Description": "Opis grupy", "Group Name": "Nazwa grupy", "Group updated successfully": "Grupa zaktualizowana pomyślnie", "Groups": "Grupy", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Haptyczne sprzężenie zwrotne", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "Pomoc", "Help us create the best community leaderboard by sharing your feedback history!": "Pomóż nam stworzyć najlepszą tablicę liderów społeczności, dzieląc się swoją historią opinii!", "Hex Color": "<PERSON><PERSON>", "Hex Color - Leave empty for default color": "<PERSON><PERSON> - pozostaw puste dla domyślnego koloru", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Hide from Sidebar": "Ukryj z paska bocznego", "Hide Model": "Uk<PERSON>j model", "High Contrast Mode": "", "Home": "Dom", "Host": "<PERSON><PERSON>", "How can I help you today?": "Jak mogę Ci dzisiaj pomóc?", "How would you rate this response?": "Jak oceniłbyś tę odpowiedź?", "HTML": "", "Hybrid Search": "<PERSON>yszuki<PERSON><PERSON> h<PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Potwierd<PERSON>, że przeczytałem i rozumiem konsekwencje mojego działania. Jestem świadomy ryzyka związanego z wykonywaniem kodu o nieznanym pochodzeniu i zweryfikowałem wiarygodność źródła.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Rozbudź c<PERSON>aw<PERSON>", "Image": "<PERSON><PERSON><PERSON>", "Image Compression": "Kompresja obrazu", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Generowanie obrazów", "Image Generation (Experimental)": "Generow<PERSON><PERSON> o<PERSON> (eksperymentalne)", "Image Generation Engine": "Silnik generowania obrazów", "Image Max Compression Size": "Maksymalny rozmiar kompresji obrazu", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Generowanie promptu obrazu", "Image Prompt Generation Prompt": "Prompt do generowania obrazów", "Image Settings": "Ustawienia grafiki", "Images": "<PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Config from JSON File": "Importuj konfigurację z pliku JSON", "Import From Link": "Importuj z linku", "Import Functions": "<PERSON><PERSON>rt<PERSON><PERSON>", "Import Models": "Importuj modele", "Import Notes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Presets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Prompt Suggestions": "", "Import Prompts": "Import<PERSON><PERSON> prompty", "Import Tools": "Importuj narzędzia", "Include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON><PERSON> flagi `--api-auth` podczas uruchamiania stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON><PERSON> flagi `--api` podczas uruchamiania stable-diffusion-webui.", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Informacje", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Wprowadź polecenia", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Instalacja z adresu URL serwisu Github", "Instant Auto-Send After Voice Transcription": "Automatyczne natychmiastowe wysyłanie po transkrypcji głosowej", "Integration": "", "Interface": "Interfejs", "Invalid file content": "Nieprawidłowa zawartość pliku", "Invalid file format.": "Nieprawidłowy format pliku.", "Invalid JSON file": "Nieprawidłowy plik JSON", "Invalid Tag": "Nieprawidłowy tag", "is typing...": "<PERSON><PERSON><PERSON>...", "Italic": "", "January": "Styczeń", "Jina API Key": "Klucz API Jiny", "join our Discord for help.": "Dołącz do naszego Discorda, aby uzyskać pomoc.", "JSON": "JSON", "JSON Preview": "Podgląd JSON", "July": "Lipiec", "June": "Czerwiec", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "Adres URL Jupytera", "JWT Expiration": "<PERSON><PERSON><PERSON>ści JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "Klucz API Kagi Search", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON>cz", "Keyboard shortcuts": "Skróty <PERSON>zo<PERSON>", "Knowledge": "<PERSON><PERSON><PERSON>", "Knowledge Access": "Dostęp do wiedzy", "Knowledge Base": "", "Knowledge created successfully.": "Pomyślnie utworzona wiedza.", "Knowledge deleted successfully.": "Wiedza została usunięta pomyślnie.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Pomyślnie zresetowano wiedzę.", "Knowledge updated successfully": "<PERSON><PERSON>za zaktualizowana pomyślnie", "Kokoro.js (Browser)": "Kokoro.js (Przeglądarka)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "<PERSON><PERSON><PERSON> ser<PERSON>a", "Landing Page Mode": "<PERSON><PERSON> strony głównej", "Language": "Język", "Language Locales": "", "Languages": "", "Last Active": "Ostatnio aktywny", "Last Modified": "Ostatnia modyfikacja", "Last reply": "Ostatnia odpowiedź", "LDAP": "LDAP", "LDAP server updated": "Serwer LDAP został zaktualizowany", "Leaderboard": "Tablica wyników", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Pozostaw puste dla nieograniczonego", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Pozosta<PERSON> puste, aby u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystkie modele lub wybierz konkretne modele", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON><PERSON><PERSON> puste, a<PERSON> <PERSON><PERSON><PERSON><PERSON> promptu, lub wp<PERSON><PERSON><PERSON> niestandardowy prompt", "Leave model field empty to use the default model.": "Pozostaw pole modelu puste, aby <PERSON><PERSON><PERSON><PERSON> domyślnego modelu.", "License": "Licencja", "Lift List": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Słuchanie...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLMy mogą <PERSON> błędy. Upewnij się, że ważne informacje są poprawne.", "Loader": "", "Loading Kokoro.js...": "Wczytywanie Kokoro.js...", "Local": "Lokalny", "Local Task Model": "", "Location access not allowed": "", "Lost": "Przegrał", "LTR": "LTR", "Made by Open WebUI Community": "Opracowane przez społeczność Open WebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "Upewnij się, że są one zawarte w", "Make sure to export a workflow.json file as API format from ComfyUI.": "Upewnij się, że wyeksportowałeś plik workflow.json w formacie API z ComfyUI.", "Manage": "Zarządzaj", "Manage Direct Connections": "Zarządzaj bezpośrednimi połączeniami", "Manage Models": "Zarządzaj modelami", "Manage Ollama": "Zarządzaj <PERSON>", "Manage Ollama API Connections": "Zarządzaj połączeniami z API Ollama", "Manage OpenAI API Connections": "Zarządzaj połączeniami z API OpenAI", "Manage Pipelines": "Zarządzanie przepływem", "Manage Tool Servers": "", "March": "Marzec", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "Maksymalna liczba przesyłanych plików", "Max Upload Size": "Maksymalny rozmiar przesyłanego pliku", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maksymalnie 3 modele można pobierać jednocześnie. Proszę spróbować ponownie później.", "May": "Maj", "Memories accessible by LLMs will be shown here.": "Wspomnienia dostępne za pomocą LLM zostaną wyświetlone tutaj.", "Memory": "<PERSON><PERSON><PERSON><PERSON>", "Memory added successfully": "<PERSON><PERSON><PERSON><PERSON> dodana p<PERSON>ślnie", "Memory cleared successfully": "Pam<PERSON><PERSON>ć oczyszczona pomyślnie", "Memory deleted successfully": "<PERSON><PERSON><PERSON>ć została usunięta pomyślnie", "Memory updated successfully": "<PERSON><PERSON><PERSON><PERSON> zaktualizowana pomyślnie", "Merge Responses": "Sc<PERSON><PERSON> ", "Merged Response": "Połączona odpowiedź", "Message rating should be enabled to use this feature": "Ocena wiadomości powinna być włączona, aby k<PERSON>zy<PERSON> z tej funkcji.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Wiadomości wysyłane po utworzeniu linku nie będą udostępniane. Użytkownicy z adresem URL będą mogli wyświetlić udostępnioną rozmowę.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' został pomyślnie pobrany.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' jest już w kolejce do pobrania.", "Model {{modelId}} not found": "Model o identyfikatorze {{modelId}} nie został znaleziony.", "Model {{modelName}} is not vision capable": "Model {{modelName}} nie jest zdolny do widzenia", "Model {{name}} is now {{status}}": "Model {{name}} jest teraz {{status}}", "Model {{name}} is now hidden": "Model {{name}} jest teraz ukryty", "Model {{name}} is now visible": "Model {{name}} jest teraz wido<PERSON>ny", "Model accepts file inputs": "", "Model accepts image inputs": "Model prz<PERSON><PERSON><PERSON><PERSON> wej<PERSON>cia obrazowe", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Model utworzony pomyślnie!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Wykryto ścieżkę systemu plików modelu. Podanie krótkiej nazwy modelu jest wymagane do aktualizacji, nie można kontynuować.", "Model Filtering": "Filtracja modeli", "Model ID": "Identyfikator modelu", "Model IDs": "Identyfikatory modeli", "Model Name": "Nazwa modelu", "Model not selected": "Model nie został wybrany", "Model Params": "Parametry modelu", "Model Permissions": "Uprawnienia modelu", "Model unloaded successfully": "", "Model updated successfully": "Model z<PERSON><PERSON><PERSON> zak<PERSON>alizowany pomyślnie", "Model(s) do not support file upload": "", "Modelfile Content": "T<PERSON>ść pliku modelu", "Models": "<PERSON>e", "Models Access": "Dostęp do modeli", "Models configuration saved successfully": "Konfiguracja modeli została zapisana pomyślnie", "Models Public Sharing": "", "Mojeek Search API Key": "Klucz API Mojeek Search", "more": "wię<PERSON>j", "More": "<PERSON><PERSON><PERSON><PERSON>j", "Name": "Nazwa", "Name your knowledge base": "Nazwij swoją bazę wiedzy", "Native": "Rodzimy", "New Chat": "<PERSON><PERSON> czat", "New Folder": "Nowy folder", "New Function": "Nowa funkcja", "New Note": "Nowa notatka", "New Password": "Nowe hasło", "New Tool": "Nowe narzędzie", "new-channel": "nowy-kanał", "Next message": "Nastę<PERSON><PERSON> w<PERSON>", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "Nie znaleziono żadnej zawartości.", "No content found in file.": "", "No content to speak": "Brak treści do omówienia", "No distance available": "<PERSON><PERSON> dostępnej odległ<PERSON>", "No feedbacks found": "Nie znaleziono żadnych opinii", "No file selected": "Nie wybrano żadnego pliku", "No groups with access, add a group to grant access": "Brak grup z dostępem, doda<PERSON> grup<PERSON>, a<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dostęp", "No HTML, CSS, or JavaScript content found.": "Nie znaleziono żadnej zawartości HTML, CSS ani JavaScript.", "No inference engine with management support found": "Nie znaleziono silnika wnioskującego z obsługą zarządzania", "No knowledge found": "Brak znalezionej wiedzy", "No memories to clear": "Brak wspomnień do wyczyszczenia", "No model IDs": "Brak identyfikatorów modeli", "No models found": "Nie znaleziono modeli", "No models selected": "Brak wybranych modeli", "No Notes": "Brak notatek", "No results found": "Brak wyników", "No search query generated": "<PERSON><PERSON> wygene<PERSON>ano żadnego zapytania wyszukiwania", "No source available": "Źródło nie jest dost<PERSON><PERSON>.", "No users were found.": "Nie znaleziono użytkowników.", "No valves to update": "Brak zaworów do aktualizacji", "None": "Brak", "Not factually correct": "Niezgodne z rzeczywistością", "Not helpful": "Nieprzydatne", "Note deleted successfully": "Notatka została usunięta", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Uwaga: <PERSON><PERSON><PERSON>, wyszukiwanie zwróci tylko dokumenty o wyniku równym lub wyższym niż minimalna punktacja.", "Notes": "Notatki", "Notification Sound": "Dźwięk powiadomienia", "Notification Webhook": "Powiadomienie Webhook", "Notifications": "Powiadomienia", "November": "Listopad", "OAuth ID": "Identyfi<PERSON><PERSON>", "October": "Październik", "Off": "Wyłączone", "Okay, Let's Go!": "<PERSON><PERSON>, do dzieła!", "OLED Dark": "Ciemny OLED", "Ollama": "Ollama", "Ollama API": "Interfejs API Ollama", "Ollama API settings updated": "Ustawienia API Ollama zostały zaktualizowane", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Włą<PERSON><PERSON>", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Dozwolone są tylko znaki alfanumeryczne i myślniki", "Only alphanumeric characters and hyphens are allowed in the command string.": "W komendzie dozwolone są wyłącznie znaki alfanumeryczne i myślniki.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON><PERSON><PERSON> kole<PERSON> m<PERSON>, utw<PERSON>rz nową bazę wiedzy, aby edyt<PERSON>/doda<PERSON><PERSON> dokumenty.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Tylko wybrani użytkownicy i grupy z uprawnieniami mogą uzyskać dostęp.", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oops! Wygląda na to, że podany URL jest nieprawidłowy. <PERSON><PERSON><PERSON> sprawdzić go ponownie i spróbować jeszcze raz.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Oops! Nadal trwa przesyłanie plików. <PERSON><PERSON><PERSON>, aż przesyłanie zostanie ukończone.", "Oops! There was an error in the previous response.": "Oops! Wystąpił błąd w poprzedniej odpowiedzi.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oops! Używasz nieobsługiwanej metody (tylko interfejs użytkownika). Proszę serwować WebUI z poziomu backendu.", "Open file": "Otwórz plik", "Open in full screen": "Otwórz na pełny ekran", "Open modal to configure connection": "", "Open new chat": "Otwórz nową rozmowę", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI korzysta wewnętrznie z szybszego faster-whisper.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Otwarta WebUI wykorzystuje SpeechT5 i wbudowane zbiory danych mówcy CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Wersja Open WebUI (v{{OPEN_WEBUI_VERSION}}) jest niższa niż wymagana wersja (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "Interfejs API OpenAI", "OpenAI API Config": "Konfiguracja interfejsu API OpenAI", "OpenAI API Key is required.": "Klucz API OpenAI jest niezbędny.", "OpenAI API settings updated": "Ustawienia API OpenAI zostały zaktualizowane", "OpenAI URL/Key required.": "<PERSON><PERSON><PERSON>y jest URL/klucz OpenAI.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "lub", "Ordered List": "", "Organize your users": "Zorganizuj swoich użytkowników", "Other": "Pozostałe", "OUTPUT": "WYNIKI", "Output format": "Format wyjściowy", "Output Format": "", "Overview": "Przegląd", "page": "strona", "Paginate": "", "Parameters": "", "Password": "<PERSON><PERSON><PERSON>", "Paste Large Text as File": "Wklej duży tekst jako plik", "PDF document (.pdf)": "Dokument PDF (.pdf)", "PDF Extract Images (OCR)": "PDF Ekstrahuj <PERSON> (OCR)", "pending": "oczekiwanie", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Odmowa dostępu podczas uzyskiwania dostępu do urządzeń multimedialnych", "Permission denied when accessing microphone": "Odmowa dostępu podczas uzyskiwania dostępu do mikrofonu", "Permission denied when accessing microphone: {{error}}": "Odmowa dostępu do mikrofonu: {{error}}", "Permissions": "Uprawnienia", "Perplexity API Key": "Klucz API Perplexity", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Personalizacja", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Przypnij", "Pinned": "Przypię<PERSON>", "Pioneer insights": "Pionierskie spostrzeżenia", "Pipe": "", "Pipeline deleted successfully": "Przepływ usunięty pomyślnie", "Pipeline downloaded successfully": "Przepływ pobrany pomyślnie", "Pipelines": "Przepływy", "Pipelines Not Detected": "Przepływ nie wykryty", "Pipelines Valves": "Przepływy i Zawory", "Plain text (.md)": "Zwykły tekst (.md)", "Plain text (.txt)": "Zwykły tekst (.txt)", "Playground": "Plac zabaw", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Proszę uważnie przejrzeć poniższe ostrzeżenia:", "Please do not close the settings page while loading the model.": "Proszę nie zamykać strony ustawień podczas ładowania modelu.", "Please enter a prompt": "<PERSON><PERSON><PERSON> prompt", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "Proszę wypełnić wszystkie pola.", "Please select a model first.": "<PERSON><PERSON><PERSON> najpier<PERSON> wybrać model.", "Please select a model.": "<PERSON><PERSON><PERSON> model.", "Please select a reason": "<PERSON><PERSON><PERSON> w<PERSON> powód", "Port": "Port", "Positive attitude": "Pozytywne nastawienie", "Prefix ID": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prefi<PERSON>u", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "ID prefiksu jest używane do unikania konfliktów z innymi połączeniami poprzez dodanie prefiksu do ID modelu - pozostaw puste, aby w<PERSON>", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Ostatnie 30 dni", "Previous 7 days": "Ostatnie 7 dni", "Previous message": "", "Private": "<PERSON><PERSON><PERSON><PERSON>", "Profile Image": "<PERSON><PERSON><PERSON><PERSON><PERSON> profilow<PERSON>", "Prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> prompt: ", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (np. podaj ciekawostkę o Imperium Rzymskim)", "Prompt Autocompletion": "", "Prompt Content": "<PERSON><PERSON><PERSON><PERSON> promptu", "Prompt created successfully": "Prompt został utworzony pomyślnie", "Prompt suggestions": "Sugestie promptów", "Prompt updated successfully": "Prompt został zaktualizowany pomyślnie.", "Prompts": "Prompty", "Prompts Access": "Dostęp do promptów", "Prompts Public Sharing": "Publiczne udostępnianie promptów", "Public": "Publiczne", "Pull \"{{searchValue}}\" from Ollama.com": "Po<PERSON>rz \"{{searchValue}}\" z Ollama.com", "Pull a model from Ollama.com": "Pobierz model z Ollama.com", "Query Generation Prompt": "Prompt do generowania zapytań", "RAG Template": "Szablon RAG", "Rating": "Ocena", "Re-rank models by topic similarity": "Ponowny ranking modeli według podobieństwa tematycznego", "Read": "<PERSON><PERSON><PERSON><PERSON>", "Read Aloud": "Czytaj na głos", "Reason": "<PERSON><PERSON><PERSON><PERSON>", "Reasoning Effort": "Wysiłek rozumowania", "Record": "<PERSON><PERSON><PERSON>", "Record voice": "Nagraj swój głos", "Redirecting you to Open WebUI Community": "Przekierowujemy Cię do społeczności Open WebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Odnoś się do mnie jako \"Użytkownik\" (np. \"Użytkownik uczy się hiszpańskiego\")", "References from": "Odniesienia do", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gdy nie powinien", "Regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Notatki do wydania", "Releases": "", "Relevance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Usuń", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Usuń model", "Remove this tag from list": "", "Rename": "Zmień nazwę", "Reorder Models": "Przeorganizuj modele", "Reply in Thread": "Odpowiedz w wątku", "Reranking Engine": "", "Reranking Model": "Poprawa rankingu modelu", "Reset": "<PERSON><PERSON><PERSON><PERSON>", "Reset All Models": "Resetuj wszystkie modele", "Reset Upload Directory": "Resetuj katalog pobierania", "Reset Vector Storage/Knowledge": "Reset magazynu wektorowego/wiedzy", "Reset view": "<PERSON><PERSON><PERSON><PERSON> widok", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Powiadomienia o odpowiedziach nie mogą zostać aktywowane, ponieważ uprawnienia strony zostały odrzucone. Prosz<PERSON> odwiedzić ustawienia przeglądarki, aby prz<PERSON><PERSON><PERSON><PERSON> wymagany dostęp.", "Response splitting": "Rozdzielani<PERSON>", "Response Watermark": "", "Result": "<PERSON><PERSON><PERSON>", "Retrieval": "", "Retrieval Query Generation": "Generowanie zapytań pobierania", "Rich Text Input for Chat": "Pole do wprowadzania tekstu sformatowanego dla czatu", "RK": "RK", "Role": "Rola", "Rosé Pine": "<PERSON><PERSON>ż<PERSON>", "Rosé Pine Dawn": "Różany Poranek Pine Dawn", "RTL": "RTL", "Run": "Uruchom", "Running": "Uru<PERSON>miono", "Save": "<PERSON><PERSON><PERSON><PERSON>", "Save & Create": "Zapisz i stwórz", "Save & Update": "Zapisz i odśwież", "Save As Copy": "Zapisz jako kopia", "Save Tag": "Zapisz tag", "Saved": "Zapisano", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Zapisywanie dzienników czatu bezpośrednio w pamięci przeglądarki nie jest już obsługiwane. Prosimy o pobranie i usunięcie dzienników czatu, klikając przycisk poniżej. Nie przejmuj się, możesz łatwo ponownie zaimportować dzienniki czatu do backendu przez", "Scroll On Branch Change": "", "Search": "Szukaj", "Search a model": "<PERSON><PERSON><PERSON>j model", "Search Base": "Użytkownicy - Baza wyszukiwania", "Search Chats": "Przes<PERSON><PERSON>", "Search Collection": "Przes<PERSON>j kolekcję", "Search Filters": "Filtry wyszukiwania", "search for tags": "wyszukiwanie tagów", "Search Functions": "Funk<PERSON>je wyszukiwania", "Search Knowledge": "Przeszukaj wiedzę", "Search Models": "Wyszukiwanie modeli", "Search Notes": "Wyszukiwanie notatek", "Search options": "<PERSON><PERSON>je w<PERSON>zukiwan<PERSON>", "Search Prompts": "Szukaj promptów", "Search Result Count": "Liczba wyników wyszukiwania", "Search the internet": "Przeszukaj internet", "Search Tools": "Szukaj narzędzi", "SearchApi API Key": "Klucz API SearchApi", "SearchApi Engine": "Search API Engine", "Searched {{count}} sites": "<PERSON>rz<PERSON><PERSON><PERSON> {{count}} stron", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Przeszuki<PERSON><PERSON> wiedzy dla \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "Adres URL zapytania Searxng", "See readme.md for instructions": "Sprawdź readme.md dla instrukcji", "See what's new": "Sprawdź nowości", "Seed": "Seed", "Select a base model": "Wybór modelu bazowego", "Select a conversation to preview": "", "Select a engine": "<PERSON><PERSON><PERSON><PERSON>", "Select a function": "<PERSON><PERSON><PERSON><PERSON>", "Select a group": "<PERSON><PERSON>bierz grupę", "Select a model": "Wybierz model", "Select a pipeline": "<PERSON><PERSON><PERSON>rz potok", "Select a pipeline url": "W<PERSON><PERSON>rz adres URL przepływu", "Select a tool": "<PERSON><PERSON>bierz narzędzie", "Select an auth method": "<PERSON><PERSON><PERSON><PERSON> metodę uwi<PERSON>zy<PERSON>", "Select an Ollama instance": "<PERSON><PERSON><PERSON><PERSON> instanc<PERSON><PERSON>", "Select Engine": "<PERSON><PERSON><PERSON><PERSON>", "Select Knowledge": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> tylko jeden model do wywołania", "Selected model(s) do not support image inputs": "Wybrane modele nie obsługują danych wejściowych w formie obrazu", "Semantic distance to query": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> semantyczna od zapytania", "Send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Send a Message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Send message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Wysyła `stream_options: { include_usage: true }` w żądaniu.\nObsługiwani dostawcy zwrócą informacje o użyciu tokenów w odpowiedzi, gdy to ustawienie jest aktywne.", "September": "Wrzesień", "SerpApi API Key": "Klucz API SerpApi", "SerpApi Engine": "Silnik SerpApi", "Serper API Key": "Klucz API Serper", "Serply API Key": "Klucz API Serply", "Serpstack API Key": "Klucz API Serpstack", "Server connection verified": "Połączenie z serwerem zostało zweryfikowane", "Set as default": "Ustaw jako <PERSON>", "Set CFG Scale": "Ustaw skalę CFG", "Set Default Model": "Ustaw model domyślny", "Set embedding model": "Ustawianie modelu osadzania", "Set embedding model (e.g. {{model}})": "Skonfiguruj model osadzania (np. {{model}})", "Set Image Size": "Ustaw roz<PERSON>r obrazu", "Set reranking model (e.g. {{model}})": "Skonfiguruj model ponownego rankingu (np. {{model}})", "Set Sampler": "Próbnik samplera", "Set Scheduler": "Ustaw harmonogram", "Set Steps": "Ustaw kroki", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Ustaw liczbę warstw, które zostaną przeniesione na GPU. Zwiększenie tej wartości może znacząco poprawić wydajność dla modeli optymalizowanych pod kątem akceleracji GPU, ale także może zużywać więcej energii i zasobów GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Ustaw liczbę wątków pracowników używanych do obliczeń. Ta opcja kontroluje, ile wątków jest używanych do jednoczesnego przetwarzania przychodzących żądań. Zwiększenie tej wartości może poprawić wydajno<PERSON> pod wysokim obciążeniem, ale może również zużywać więcej zasobów CPU.", "Set Voice": "Ustaw głos", "Set whisper model": "Ustaw model szeptu", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Ustawia sekwencje stopu do użycia. Gdy ten wzorzec zostanie napotkany, LLM przestanie generować tekst i zwróci wynik. <PERSON><PERSON><PERSON> skonfigurować wiele sekwencji stopu, określając kilka oddzielnych parametrów stopu w pliku modelu.", "Settings": "Ustawienia", "Settings saved successfully!": "Ustawienia zostały zapisane pomyślnie!", "Share": "Udostępnij", "Share Chat": "Udostępnij rozmowę", "Share to Open WebUI Community": "Udostępnij w społeczności OpenWebUI", "Sharing Permissions": "Uprawnienia udostępniania", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Wyś<PERSON><PERSON>l", "Show \"What's New\" modal on login": "Wyświetl okno dialogowe \"What's New\" podczas logowania", "Show Admin Details in Account Pending Overlay": "Wyświetl szczegóły administratora w okienu informacyjnym o potrzebie zatwierdzenia przez administratora konta użytkownika", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "Wyświetl skróty", "Show your support!": "Wyraź swoje poparcie!", "Showcased creativity": "Prezentacja kreatywności", "Sign in": "<PERSON><PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "Z<PERSON><PERSON>j się do {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Zaloguj się do {{WEBUI_NAME}} przy użyciu LDAP", "Sign Out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign up": "Zarejestruj się", "Sign up to {{WEBUI_NAME}}": "Zarejestruj się w {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Logowanie do {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Źródło", "Speech Playback Speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odtwarzania mowy", "Speech recognition error: {{error}}": "Błąd rozpoznawania mowy: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Silnik konwersji mowy na tekst", "Stop": "Zatrzymaj", "Stop Generating": "", "Stop Sequence": "Zatrzym<PERSON>", "Stream Chat Response": "Strumieniowanie odpowiedzi z czatu", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "Model STT", "STT Settings": "Ustawienia STT", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Podtytuł (np. o Imperium Rzymskim)", "Success": "Sukces", "Successfully updated.": "Uaktualniono pomyślnie.", "Suggested": "Proponowane", "Support": "<PERSON><PERSON><PERSON><PERSON>", "Support this plugin:": "Wes<PERSON>rz<PERSON>j ten plugin:", "Supported MIME Types": "", "Sync directory": "Sync directory", "System": "System", "System Instructions": "Instrukcje systemowe", "System Prompt": "Prompt systemowy", "Tags": "Tagi", "Tags Generation": "Generowanie tagów", "Tags Generation Prompt": "Prompt do generowania tagów", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "<PERSON><PERSON><PERSON><PERSON>, aby przerwać", "Task List": "", "Task Model": "", "Tasks": "Zadania", "Tavily API Key": "Klucz API Tavily", "Tavily Extract Depth": "", "Tell us more:": "Podaj więcej informacji", "Temperature": "Temperatura", "Temporary Chat": "Tymczasowa rozmowa", "Text Splitter": "Rozdzielacz tekstu", "Text-to-Speech": "", "Text-to-Speech Engine": "Silnik konwersji tekstu na mowę", "Thanks for your feedback!": "Dziękujemy za twoją opinię!", "The Application Account DN you bind with for search": "Konto techniczne w formacie DN, z którym się wiążesz w celu przeszukiwania", "The base to search for users": "Podstawa do wyszukiwania użytkowników", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Twórcy tego w<PERSON>czki to en<PERSON><PERSON><PERSON><PERSON><PERSON>, którzy działają jako wolontariusze ze społeczności. <PERSON><PERSON><PERSON>, że ta wtyczka jest pomocna, roz<PERSON>ż wsparcie jej rozwoju.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Tablica wyników oceny opiera się na systemie rankingu Elo i jest aktualizowana w czasie rzeczywistym.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "Atrybut LDAP, który mapuje się na adres e-mail używany przez użytkowników do logowania.", "The LDAP attribute that maps to the username that users use to sign in.": "Atrybut LDAP, który mapuje się na nazwę użytkownika, którą użytkownicy używają do logowania.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Tablica wyników jest w wersji beta, wi<PERSON><PERSON> w miarę udoskonalania algorytmu możemy jeszcze modyfikować sposób obliczania ocen.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Maksymalny rozmiar pliku w MB. Jeśli rozmiar pliku przekroczy ten limit, plik nie zostanie przesłany.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Maks<PERSON>alna liczba plików, które można użyć jednocześnie w czacie. Jeśli liczba plików przekroczy ten limit, pliki nie zostaną przesłane.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Wynik powinien być wartością pomiędzy 0,0 (0%) a 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Motyw", "Thinking...": "Myślę...", "This action cannot be undone. Do you wish to continue?": "Czy na pewno chcesz kontynuować? Ta akcja nie może zostać cofnięta.", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "To gwarantu<PERSON>, że Twoje wartościowe rozmowy są bezpiecznie zapisywane w bazie danych backendowej. Dziękujemy!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "To jest funk<PERSON><PERSON> eksperymentalna, może nie d<PERSON>ła<PERSON> zgodnie z oczekiwaniami i jest podatna na zmiany w dowolnym momencie.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Ta opcja usunie wszystkie istniejące pliki w kolekcji i zastąpi je nowo przesłanymi plikami.", "This response was generated by \"{{model}}\"": "Ta odpowiedź została wygenerowana przez \"{{model}}\".", "This will delete": "To usunie wszystkie pliki z katalogu.", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "To usunie <strong>{{NAME}}</strong> i <strong>wsz<PERSON>t<PERSON> jego zawa<PERSON>oś<PERSON></strong>.", "This will delete all models including custom models": "To usunie wszys<PERSON>kie modele, w tym niestandardowe.", "This will delete all models including custom models and cannot be undone.": "To usunie wsz<PERSON><PERSON>kie modele, w tym niestandardowe modele, i nie można tego co<PERSON>nąć.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON><PERSON> ch<PERSON>z wyzerować bazę wiedzy i zsynchronizować wszystkie pliki? Proszę potwierdź swoją decyzję.", "Thorough explanation": "Szczegółowe wyjaśnienie", "Thought for {{DURATION}}": "<PERSON><PERSON><PERSON><PERSON> przez {{DURATION}}", "Thought for {{DURATION}} seconds": "<PERSON><PERSON><PERSON><PERSON> przez {{DURATION}} sekund", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "<PERSON><PERSON><PERSON><PERSON> jest adres URL serwera Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Porada: <PERSON><PERSON> wiele zmiennych jednocześnie, naciśnij klawisz Tab w polu wprowadzania czatu po każdej zmianie.", "Title": "<PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Tytuł (na przykład {e.g.} Powiedz mi jakiś zabawny fakt)", "Title Auto-Generation": "Automatyczne tworzenie tytułu", "Title cannot be an empty string.": "Tytuł nie może być pustym stringiem.", "Title Generation": "Generowanie tytułów", "Title Generation Prompt": "Prompt do generowania tytułu", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dostępne nazwy modeli do pobrania,", "To access the GGUF models available for downloading,": "<PERSON><PERSON> <PERSON><PERSON><PERSON>ć dostęp do modeli GGUF dostępnych do pobrania,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON>by uzy<PERSON>ć dostęp do interfejsu WebUI, skontaktuj się z administratorem. Administratorzy mogą zarządzać statusami użytkowników z Panelu Administracyjnego.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "<PERSON><PERSON> <PERSON> bazę wiedzy tutaj, dodaj je najpierw do przestrzeni roboczej \"Wiedza\". ", "To learn more about available endpoints, visit our documentation.": "<PERSON><PERSON> do<PERSON><PERSON><PERSON> się więcej o dostępnych punktach końcowych, odwied<PERSON> naszą dokumentację.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, udostępniane są tylko o<PERSON>ny, identyfikatory modeli, tagi i metadane z Twoich opinii – Twoje dzienniki czatu pozostają prywatne i nie są uwzględniane.", "To select actions here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> w<PERSON><PERSON><PERSON> działania tutaj, najpierw dodaj je do przestrzeni roboczej \"Funkcje\".", "To select filters here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> w<PERSON><PERSON> filtry tutaj, naj<PERSON><PERSON><PERSON> dodaj je do przestrzeni roboczej \"Funkcje\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "<PERSON><PERSON> w<PERSON><PERSON> zestawy narzędzi tutaj, dodaj je najpierw do przestrzeni roboczej \"Narzędzia\". ", "Toast notifications for new updates": "Powiadomienia o nowych aktualizacjach", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle search": "", "Toggle settings": "Przełącz opcje", "Toggle sidebar": "Przełącz pasek boczny", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "Zbyt rozwlekłe", "Tool created successfully": "Narzędzie utworzone pomyślnie", "Tool deleted successfully": "Narzędzie zostało usunięte pomyślnie", "Tool Description": "Opis narzędzia", "Tool ID": "ID narzędzia", "Tool imported successfully": "Narzędzie zostało pomyślnie zaimportowane", "Tool Name": "Nazwa narzędzia", "Tool Servers": "", "Tool updated successfully": "Narzędzie zaktualizowane pomyślnie", "Tools": "Narzędzia", "Tools Access": "Narzędzia Dostępu", "Tools are a function calling system with arbitrary code execution": "Narzędzia to system wywoływania funkcji z możliwością wykonania dowolnego kodu.", "Tools Function Calling Prompt": "Narzędzia Funkcja Wywołania Promptu", "Tools have a function calling system that allows arbitrary code execution.": "Narzędzia mają <PERSON>ę wywoływania systemu, która umożliwia wykonanie dowolnego kodu.", "Tools Public Sharing": "", "Top K": "Najlepsze K", "Top K Reranker": "", "Transformers": "Transformery", "Trouble accessing Ollama?": "<PERSON><PERSON> masz <PERSON>y z dostępem do Ollama?", "Trust Proxy Environment": "", "TTS Model": "Model TTS", "TTS Settings": "Ustawienia syntezatora mowy", "TTS Voice": "Głos TTS", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Podaj adres URL do pobrania z Hugging Face", "Uh-oh! There was an issue with the response.": "Ojej! Wystąpił problem z odpowiedzią.", "UI": "Interfejs użytkownika", "Unarchive All": "Odarchiwizuj wszystko", "Unarchive All Archived Chats": "Odarchiwizuj wszystkie zarchiwizowane rozmowy", "Unarchive Chat": "Odarchiwi<PERSON><PERSON> c<PERSON>t", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Rozwiązywanie zagadek", "Unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unravel secrets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unsupported file type.": "", "Untagged": "<PERSON><PERSON>", "Untitled": "", "Update": "Aktualizacja", "Update and Copy Link": "Aktualizuj i kopiuj link", "Update for the latest features and improvements.": "Aktualizacja do najnowszych funkcji i ulepszeń.", "Update password": "<PERSON><PERSON><PERSON> hasła", "Updated": "<PERSON><PERSON> z<PERSON><PERSON><PERSON><PERSON><PERSON> wyd<PERSON> aplikacji, na<PERSON><PERSON><PERSON> rozważyć optymalizację kodu i użycie odpowiednich struktur danych. {optimization} [optimizations] mogą obejmować minimalizację liczby operacji we/wy, zwiększenie wydajności algorytmów oraz zmniejszenie zużycia pamięci. Ponadto, warto rozważyć użycie {caching} [pamięci podręcznej] do przechowywania często używanych danych, co może znacząco przyspieszyć działanie aplikacji. Wreszcie, monitorowanie wydajności aplikacji za pomocą narzędzi takich jak {profiling} [profilowanie] może pomóc zidentyfikować wolne miejsca i dalsze obszary do optymalizacji.", "Updated at": "Aktualizacja dnia", "Updated At": "Czas aktualizacji", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Przejdź na licencjonowany plan, aby uzyskać rozszerzone możliwości, w tym niestandardowe motywy, personalizację oraz dedykowane wsparcie.", "Upload": "Prześ<PERSON>j", "Upload a GGUF model": "Prześlij model GGUF", "Upload Audio": "Prześlij audio", "Upload directory": "Prześ<PERSON>j katalog", "Upload files": "Prześlij pliki", "Upload Files": "Prześlij pliki", "Upload Pipeline": "Prześlij przepływ", "Upload Progress": "Postęp przesyłania plików", "URL": "Adres URL", "URL Mode": "Tryb URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Użyj '#' w polu wprowadzania zapytania, aby załadowa<PERSON> i uwzględnić swoją wiedzę.", "Use Gravatar": "Użyj Gravatara", "Use groups to group your users and assign permissions.": "Wykorzystaj grupy do grupowania użytkowników i przypisywania uprawnień.", "Use Initials": "Użyj inicjałów", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "użytkownik", "User": "Użytkownik", "User location successfully retrieved.": "Lokalizacja użytkownika została pomyślnie pobrana.", "User menu": "", "User Webhooks": "Webhooki użytkownika", "Username": "Nazwa użytkownika", "Users": "Użytkownicy", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Korzystanie z domyślnego modelu areny ze wszystkimi modelami. Kliknij przycisk plus, aby dodać niestandardowe modele.", "Valid time units:": "Dozwolone jednostki czasu:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "<PERSON><PERSON><PERSON>", "Valves updated successfully": "Zawory zak<PERSON>alizowane pomyślnie", "variable": "zmienna", "Verify Connection": "Sprawdź połączenie", "Verify SSL Certificate": "Sprawdź certyfikat SSL", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Wersja {{selectedVersion}} z {{totalVersions}}", "View Replies": "Wyświet<PERSON> odpowiedzi", "View Result from **{{NAME}}**": "", "Visibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Vision": "", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "Wprowadzanie głosowe", "Voice mode": "", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "Uwaga:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Uwaga: Włączenie tego pozwoli użytkownikom na przesyłanie dowolnego kodu na serwer.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Uwaga: W przypadku aktualizacji lub zmiany modelu osadzania, konieczne będzie ponowne zaimportowanie wszystkich dokumentów.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Uwaga: Uruchamianie Jupytera umożliwia wykonywanie dowolnego kodu, co stwarza poważne zagrożenia dla bezpieczeństwa – postępuj z ekstremalną ostrożnością.", "Web": "Sieć internetowa", "Web API": "Interfejs API sieci web", "Web Loader Engine": "", "Web Search": "Wyszukiwarka internetowa", "Web Search Engine": "Silnik wyszukiweania w sieci", "Web Search in Chat": "Wyszukiwanie w sieci Web na czacie", "Web Search Query Generation": "Generowanie zapytań Wyszukiwania Sieciowego", "Webhook URL": "Adres URL webhooka", "WebUI Settings": "Ustawienia interfejsu WebUI", "WebUI URL": "Adres URL interfejsu internetowego", "WebUI will make requests to \"{{url}}\"": "WebUI będzie wysyłać żądania do \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI będzie wysyłać żądania do \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI będzie wysyłać żądania do \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "Do czego dążysz?", "What are you working on?": "Nad czym pracujesz?", "What's New in": "Co nowego w", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON> jest w<PERSON><PERSON><PERSON><PERSON>, model b<PERSON><PERSON><PERSON> reagował na każdą wiadomość czatu w czasie rzeczywistym, generu<PERSON><PERSON><PERSON> odpowiedź tak szybko, jak użytkownik wyśle wiadomość. Ten tryb jest przydatny dla aplikacji czatu na żywo, ale może wpływać na wydajność na wolniejszym sprzęcie.", "wherever you are": "gdzie<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Szept (Lokalny)", "Why?": "Dlaczego?", "Widescreen Mode": "Tryb panoramiczny", "Won": "<PERSON><PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "<PERSON><PERSON><PERSON> rob<PERSON>", "Workspace Permissions": "Uprawnienia do przestrzeni roboczej", "Write": "Na<PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "Podaj przykładową sugestię dla polecenia (np. <PERSON><PERSON> jeste<PERSON>?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Napisz krótkie podsumowanie w maksymalnie 50 słowach, kt<PERSON>re streszcza [temat lub słowo kluczowe].", "Write something...": "<PERSON><PERSON><PERSON> coś...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON><PERSON><PERSON><PERSON>", "You": "Ty", "You are currently using a trial license. Please contact support to upgrade your license.": "Obecnie używasz licencji trial. Skontaktuj się z supportem, aby zupgradować Twoją licencję.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Moż<PERSON>z rozmawiać jednocześnie maksymalnie z {{maxCount}} plikiem(i).", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Mo<PERSON><PERSON>z spersonalizować swoje interakcje z LLM, dodając wspomnienia za pomocą przycisku 'Zarządzaj' p<PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON> czemu będą one bardziej pomocne i dostosowane do Ciebie.", "You cannot upload an empty file.": "Nie moż<PERSON>z przesłać pustego pliku.", "You do not have permission to upload files.": "Nie masz uprawnień do przesyłania plików.", "You have no archived conversations.": "Nie posiadasz zarchiwizowanych konwersacji.", "You have shared this chat": "Udostępniłeś tę rozmowę", "You're a helpful assistant.": "Jesteś pomocnym asystentem.", "You're now logged in.": "Je<PERSON>ś teraz zalogowany.", "Your account status is currently pending activation.": "Twoje konto oczekuje obecnie na aktywację.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Cała Twoja wpłata trafi bezpośrednio do dewelopera wtyczki; Open WebUI nie pobiera żadnej prowizji. Należy jednak <PERSON>, że wybrana platforma finansowania może mieć własne opłaty.", "Youtube": "Youtube", "Youtube Language": "Język Youtube", "Youtube Proxy URL": "URL proxy Youtube"}