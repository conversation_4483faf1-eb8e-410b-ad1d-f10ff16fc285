{"-1 for no limit, or a positive integer for a specific limit": "Cheksiz uchun -1 yoki ma'lum chegara uchun musbat butun son", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' yoki '-1' muddati tugama<PERSON>gi uchun.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(<PERSON><PERSON><PERSON>, `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(<PERSON><PERSON><PERSON>, `sh webui.sh --api`)", "(latest)": "(oxirgi)", "(leave blank for to use commercial endpoint)": "(tijo<PERSON><PERSON> so'nggi nuqtadan foy<PERSON><PERSON>sh uchun bo'sh qoldiring)", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} ta mavjud vositalar", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} ta yashirin chiziq", "{{COUNT}} Replies": "{{COUNT}} ta javob", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}} ning chatlari", "{{webUIName}} Backend Required": "{{webUIName}} Backend talab qilinadi", "*Prompt node ID(s) are required for image generation": "*Rasm yaratish uchun tezkor tugun identifikatorlari talab qilinadi", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON>i yangi versiya (v{{LATEST_VERSION}}) mavjud.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Vazifa modeli chatlar va veb-qidiruv so'rovlari uchun sarlavhalarni yaratish kabi vazifalarni bajaris<PERSON>da ishlatiladi", "a user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "About": "<PERSON><PERSON><PERSON>", "Accept autocomplete generation / Jump to prompt variable": "Avtomatik to'l<PERSON>ishni yaratishni qabul qiling / O'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o'tish", "Access": "<PERSON><PERSON><PERSON>", "Access Control": "Kirish nazorati", "Accessible to all users": "<PERSON><PERSON> foy<PERSON>v<PERSON>lar uchun ochiq", "Account": "<PERSON><PERSON>", "Account Activation Pending": "<PERSON><PERSON><PERSON>", "Accurate information": "<PERSON><PERSON>lumot", "Action": "", "Actions": "Harakatlar", "Activate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Chat kiritish uchun “/{{COMMAND}}” terib ushbu buyru<PERSON> faollas<PERSON>.", "Active Users": "<PERSON>ao<PERSON> f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>h", "Add a model ID": "Model identifika<PERSON><PERSON> qo'shing", "Add a short description about what this model does": "Ushbu model ni<PERSON> q<PERSON>hi haqida qisqacha tavsif qo'shing", "Add a tag": "Teg qo'shing", "Add Arena Model": "Arena modelini qo'shing", "Add Connection": "<PERSON><PERSON><PERSON> qo'shish", "Add Content": "Kontent qo'shish", "Add content here": "Bu yerga tarkib qo'shing", "Add Custom Parameter": "<PERSON><PERSON> parametr qo'shing", "Add custom prompt": "<PERSON><PERSON> taklif qo'shing", "Add Files": "<PERSON><PERSON><PERSON> qo'shish", "Add Group": "<PERSON><PERSON> qo'shish", "Add Memory": "<PERSON><PERSON><PERSON> qo'shish", "Add Model": "Model qo'shish", "Add Reaction": "<PERSON><PERSON><PERSON><PERSON> qo'shing", "Add Tag": "Teg qo'shish", "Add Tags": "<PERSON><PERSON><PERSON> qo'shish", "Add text content": "<PERSON><PERSON> tarkibini qo'shing", "Add User": "<PERSON>oy<PERSON><PERSON><PERSON><PERSON> qo'shish", "Add User Group": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> guruhini qo'shish", "Adjusting these settings will apply changes universally to all users.": "<PERSON><PERSON><PERSON> o'zgartirish barcha foydalanuvchilarga universal tarzda qo'llaniladi.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Administrator paneli", "Admin Settings": "Administrator <PERSON><PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "<PERSON><PERSON> har doim barcha vosital<PERSON>an foydal<PERSON><PERSON><PERSON>i mumkin; foydal<PERSON>v<PERSON>larga ish joyida har bir model uchun tayin<PERSON><PERSON> vositalar kerak bo'ladi.", "Advanced Parameters": "Kengaytirilgan parametrlar", "Advanced Params": "Kengaytirilgan parametrlar", "AI": "", "All": "<PERSON><PERSON><PERSON>", "All Documents": "<PERSON><PERSON>", "All models deleted successfully": "<PERSON><PERSON> <PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'<PERSON>iri<PERSON>i", "Allow Call": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruxsat berish", "Allow Chat Controls": "Chat bosh<PERSON><PERSON><PERSON>ga ruxsat bering", "Allow Chat Delete": "<PERSON><PERSON><PERSON> ruxsat bering", "Allow Chat Deletion": "<PERSON><PERSON><PERSON> ruxsat bering", "Allow Chat Edit": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON><PERSON> ruxsat bering", "Allow Chat Export": "Chat eksportiga ruxsat bering", "Allow Chat Share": "<PERSON>t alma<PERSON><PERSON>ga ruxsat bering", "Allow Chat System Prompt": "", "Allow File Upload": "<PERSON><PERSON> yuk<PERSON>ga ruxsat bering", "Allow Multiple Models in Chat": "Cha<PERSON>da bir nechta modellarga ruxsat bering", "Allow non-local voices": "<PERSON><PERSON><PERSON><PERSON> bo'l<PERSON><PERSON> ovozlarga ruxsat bering", "Allow Speech to Text": "<PERSON><PERSON><PERSON><PERSON> mat<PERSON> ruxsat berish", "Allow Temporary Chat": "Vaqtinchalik suhbatga ruxsat bering", "Allow Text to Speech": "<PERSON><PERSON><PERSON> ayla<PERSON>ga ruxsat bering", "Allow User Location": "Foydalanu<PERSON><PERSON> joylash<PERSON>ga ruxsat berish", "Allow Voice Interruption in Call": "Qo'ng'i<PERSON><PERSON>da ovozli uzilishga ruxsat bering", "Allowed Endpoints": "Ruxsat etilgan oxirgi nuqtalar", "Allowed File Extensions": "Ruxsat etilgan fayl kengay<PERSON>", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Yu<PERSON>sh uchun ruxsat berilgan fayl kengaytmal<PERSON>. Bir nechta kengaytmalarni vergul bilan ajrating. Barcha fayl turlari uchun bo'sh qoldiring.", "Already have an account?": "<PERSON><PERSON><PERSON><PERSON> bormi?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Top_p ga muqobil va sifat va xilma-xillik muvozanatini ta'minlashga qaratilgan. p parametri tokenning ko'rib chiqilishining minimal ehtimolini ifodalaydi, bu tokenning ehtimoliy ehtimoliga nisbatan. Misol uchun, p=0,05 va eng ehtimolli token 0,9 ehtimolga ega bo'lsa, qiymati 0,045 dan kam bo'lgan logitlar filtrlanadi.", "Always": "Har doim", "Always Collapse Code Blocks": "Har doim kod blo<PERSON>rini yig'ish", "Always Expand Details": "<PERSON>r doim Tafsilotlarni k<PERSON>", "Always Play Notification Sound": "Har doim bildirishnoma ovozini ijro etish", "Amazing": "<PERSON><PERSON><PERSON>", "an assistant": "<PERSON><PERSON><PERSON><PERSON>", "Analyzed": "<PERSON><PERSON><PERSON>", "Analyzing...": "<PERSON><PERSON><PERSON>...", "and": "va", "and {{COUNT}} more": "va yana {{COUNT}} ta", "and create a new shared link.": "va yangi umumiy havola ya<PERSON>.", "Android": "Android", "API": "", "API Base URL": "API bazasi URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API kaliti", "API Key created.": "API kaliti yaratildi.", "API Key Endpoint Restrictions": "API kalit so'nggi nuqta cheklov<PERSON>i", "API keys": "API kalitlari", "API Version": "API versiyasi", "Application DN": "Ilova DN", "Application DN Password": "Ilova DN paroli", "applies to all users with the \"user\" role": "\"foydalanuvchi\" roliga ega barcha foydalanuvchilarga te<PERSON>i", "April": "aprel", "Archive": "Arxiv", "Archive All Chats": "<PERSON><PERSON> arxivlash", "Archived Chats": "A<PERSON><PERSON><PERSON><PERSON><PERSON> chatlar", "archived-chat-export": "arxivlangan-chat-eksport", "Are you sure you want to clear all memories? This action cannot be undone.": "Haqiqatan ham barcha xotiralarni tozalamoqchimisiz? Bu amalni ortga qaytarib bo‘l<PERSON>ydi.", "Are you sure you want to delete this channel?": "<PERSON><PERSON><PERSON><PERSON> ham bu kanalni oʻchirib tashlamoqchi<PERSON>?", "Are you sure you want to delete this message?": "<PERSON>qi<PERSON><PERSON> ham bu xabarni oʻ<PERSON><PERSON> tashlam<PERSON>qchi<PERSON>?", "Are you sure you want to unarchive all archived chats?": "<PERSON>qi<PERSON>tan ham barcha arxivlangan chatlarni arxivdan chiqarmoqchimisiz?", "Are you sure?": "Ishonchingiz komilmi?", "Arena Models": "Arena modellari", "Artifacts": "Artefaktlar", "Ask": "So'rang", "Ask a question": "Savol bering", "Assistant": "<PERSON><PERSON><PERSON><PERSON>", "Attach file from knowledge": "<PERSON><PERSON><PERSON>dan faylni bi<PERSON>", "Attention to detail": "Tafsilotlarga e'tibor", "Attribute for Mail": "Pochta uchun atribut", "Attribute for Username": "Foydalanuvchi nomi uchun atribut", "Audio": "Audio", "August": "avgust", "Auth": "A<PERSON><PERSON>", "Authenticate": "Autentifikatsiya q<PERSON>", "Authentication": "Autentifikatsiya", "Auto": "Avtomatik", "Auto-Copy Response to Clipboard": "Javobni vaqtinchalik xotiraga avtomatik nusxalash", "Auto-playback response": "Avtomatik ijro javobi", "Autocomplete Generation": "Avto<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Autocomplete Generation Input Max Length": "Avtomatik <PERSON><PERSON><PERSON><PERSON><PERSON> is<PERSON>ab chiq<PERSON>h kiritish maksima<PERSON>", "Automatic1111": "Avtomatik 1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 asosiy URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 asosiy URL manzili talab qilinadi.", "Available list": "<PERSON><PERSON><PERSON><PERSON> ro'yxat", "Available Tools": "<PERSON><PERSON><PERSON><PERSON>", "available!": "mavjud!", "Awful": "<PERSON><PERSON><PERSON><PERSON>", "Azure AI Speech": "Azure AI nutqi", "Azure Region": "Azure mint<PERSON>asi", "Back": "Orqaga", "Bad Response": "<PERSON><PERSON> javob", "Banners": "<PERSON><PERSON>", "Base Model (From)": "<PERSON><PERSON><PERSON><PERSON> model (dan boshlab)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "oldin", "Being lazy": "<PERSON><PERSON><PERSON> bo'lish", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing Search V7 Endpoint", "Bing Search V7 Subscription Key": "Bing Search V7 obuna kaliti", "Bocha Search API Key": "Bocha qidiruv API kaliti", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Cheklangan javoblar uchun maxsus tokenlarni kuchay<PERSON>h yoki jazolash. Yo'naltirilgan qiymatlar -100 va 100 (shu jumladan) oralig'ida mahkamlanadi. (Birlamchi: yo‘q)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Docling OCR mexanizmi va til(lar) koʻrsatilishi yoki ikkalasi ham boʻsh qolishi kerak.", "Brave Search API Key": "Brave Search API kaliti", "Bullet List": "", "By {{name}}": "Muallif: {{name}}", "Bypass Embedding and Retrieval": "O'<PERSON><PERSON><PERSON> va qidirishni chetlab o'tish", "Bypass Web Loader": "<PERSON><PERSON> yuk<PERSON> chetlab o'tish", "Cache Base Model List": "", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qiling", "Call feature is not supported when using Web STT engine": "Web STT mexanizmidan foydalanilganda qo'ng'iroq funksiyasi qo'llab-quvvatlanmaydi", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Bekor qilish", "Capabilities": "Imkoniyatlar", "Capture": "<PERSON><PERSON>'<PERSON><PERSON> olish", "Capture Audio": "Audio yozib olish", "Certificate Path": "<PERSON><PERSON><PERSON><PERSON> yo'li", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "Kanal nomi", "Channels": "<PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Avtomatik to<PERSON><PERSON><PERSON><PERSON>ni yaratish uchun belgilar chegarasi", "Chart new frontiers": "<PERSON><PERSON> chegara<PERSON> bel<PERSON>", "Chat": "Cha<PERSON>", "Chat Background Image": "Chat fon rasmi", "Chat Bubble UI": "Chat Bubble UI", "Chat Controls": "<PERSON><PERSON> b<PERSON>", "Chat direction": "Chat yo'nalishi", "Chat Overview": "<PERSON><PERSON><PERSON> umumi<PERSON> nu<PERSON>", "Chat Permissions": "<PERSON><PERSON> r<PERSON>", "Chat Tags Auto-Generation": "<PERSON><PERSON> teglar<PERSON> avtomatik yaratish", "Chats": "<PERSON><PERSON><PERSON><PERSON>", "Check Again": "<PERSON><PERSON>", "Check for updates": "<PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "Checking for updates...": "Yangilanish<PERSON> teks<PERSON><PERSON>...", "Choose a model before saving...": "<PERSON><PERSON><PERSON><PERSON> oldin modelni tanlang...", "Chunk Overlap": "<PERSON>'laklarning bir-biriga o'<PERSON><PERSON><PERSON><PERSON>", "Chunk Size": "<PERSON><PERSON><PERSON><PERSON> hajmi", "Ciphers": "<PERSON><PERSON><PERSON><PERSON>", "Citation": "<PERSON><PERSON><PERSON><PERSON>", "Citations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clear memory": "<PERSON><PERSON><PERSON><PERSON>", "Clear Memory": "<PERSON><PERSON><PERSON><PERSON>", "click here": "bu yerni bosing", "Click here for filter guides.": "Filtr qo'llanmalari uchun bu yerni bosing.", "Click here for help.": "<PERSON><PERSON><PERSON> uchun shu yerni bosing.", "Click here to": "Bu yerga bosing", "Click here to download user import template file.": "Foydalanuvchi import shablon faylini yuklab olish uchun shu yerni bosing.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON><PERSON> shivirlash haqida ko'proq ma'lumot olish va mavjud modellarni ko'rish uchun shu yerni bosing.", "Click here to see available models.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> ko'rish uchun shu yerni bosing.", "Click here to select": "<PERSON><PERSON> uchun shu yerni bosing", "Click here to select a csv file.": "csv faylini tanlash uchun shu yerni bosing.", "Click here to select a py file.": "py faylini tanlash uchun shu yerni bosing.", "Click here to upload a workflow.json file.": "Workflow.j<PERSON> faylini yuklash uchun shu yerni bosing.", "click here.": "bu yerni bosing.", "Click on the user role button to change a user's role.": "Foydalanuvchi rolini o'z<PERSON><PERSON>sh uchun foydalanuvchi roli <PERSON> bosing.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Buferga yozish ruxsati rad etildi. <PERSON><PERSON><PERSON> ruxsat berish uchun brauzer so<PERSON><PERSON> tekshiring.", "Clone": "Klonlash", "Clone Chat": "Chatni klonlash", "Clone of {{TITLE}}": "{{TITLE}} kloni", "Close": "<PERSON><PERSON><PERSON>", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "<PERSON><PERSON><PERSON> b<PERSON>", "Code Execution": "<PERSON><PERSON><PERSON> b<PERSON>", "Code Execution Engine": "<PERSON><PERSON><PERSON> baja<PERSON> mexa<PERSON>", "Code Execution Timeout": "<PERSON><PERSON><PERSON> baja<PERSON>h vaqti tugashi", "Code formatted successfully": "Kod mu<PERSON>tl<PERSON>", "Code Interpreter": "Ko<PERSON>i", "Code Interpreter Engine": "Kod tarji<PERSON> mexa<PERSON>", "Code Interpreter Prompt Template": "Kod tarji<PERSON>i so'rovi shabloni", "Collapse": "<PERSON><PERSON><PERSON>", "Collection": "To'plam", "Color": "<PERSON>ng", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API kaliti", "ComfyUI Base URL": "ComfyUI asosiy URL manzili", "ComfyUI Base URL is required.": "ComfyUI asosiy URL manzili talab qilinadi.", "ComfyUI Workflow": "ComfyUI ish jarayoni", "ComfyUI Workflow Nodes": "ComfyUI ish oqimi tugunlari", "Command": "Buyruq", "Comment": "", "Completions": "Tugallashlar", "Concurrent Requests": "Bir vaqtning o'zida so'rovlar", "Configure": "Sozlang", "Confirm": "Tasdiqlang", "Confirm Password": "<PERSON><PERSON><PERSON>", "Confirm your action": "Harakatingizni tasdi<PERSON>", "Confirm your new password": "Yangi parolingizni tasdi<PERSON>", "Connect to your own OpenAI compatible API endpoints.": "O'zingizning OpenAI-ga mos keladigan API so'nggi nuqtalariga ulaning.", "Connect to your own OpenAPI compatible external tool servers.": "O'zingizning OpenAPI-ga mos keladigan tashqi asboblar serverlariga ulaning.", "Connection failed": "<PERSON><PERSON><PERSON>alga o<PERSON>i", "Connection successful": "<PERSON><PERSON><PERSON>", "Connection Type": "<PERSON><PERSON><PERSON> turi", "Connections": "<PERSON><PERSON><PERSON><PERSON>", "Connections saved successfully": "Ulanishlar muvaffaqiyatli saqlandi", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Fikrlash modellari uchun mulohaza yuritish bo'yicha harakat<PERSON> cheklaydi. Faqat fikrlash harakatlarini qo'llab-quvvatlaydigan maxsus provayderlarning fikrlash modellari uchun qo'llaniladi.", "Contact Admin for WebUI Access": "WebUI-ga kirish uchun administrator bilan bog'laning", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction Engine": "Kontentni a<PERSON> olish mexa<PERSON>z<PERSON>", "Continue Response": "<PERSON><PERSON><PERSON> davom et<PERSON>", "Continue with {{provider}}": "{{provider}} bilan davom eting", "Continue with Email": "Elektron pochta orqali davom eting", "Continue with LDAP": "LDAP bilan davom eting", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "TTS so'rovlari uchun xabar matni qanday bo'lini<PERSON>i boshqaring. \"Tinish belgilari\" jum<PERSON><PERSON><PERSON>, \"paragraflar\" paragraflarga bo'linadi va \"yo'q\" xabarni bitta qator sifatida saqlaydi.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Yaratilgan matndagi tokenlar ketma-ketligini takrorlashni nazorat qilish. <PERSON><PERSON><PERSON> qiymat (masalan, 1,5) takrorlash uchun qattiqroq jazola<PERSON>, pastroq qiymat (masalan, 1,1) esa yumshoqroq bo'ladi. 1-da, u o'chiri<PERSON><PERSON>.", "Controls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Chiqarishning izchilligi va xilma-x<PERSON><PERSON> o'rtasidagi muvozanatni nazorat qiladi. Pastroq qiymat ko'proq diqqat markazida va izchil matnga olib keladi.", "Copied": "<PERSON><PERSON>", "Copied link to clipboard": "Buferga havola n<PERSON>i", "Copied shared chat URL to clipboard!": "<PERSON><PERSON>y chat URL manzili vaqtinchalik xotiraga nusxalandi!", "Copied to clipboard": "Bufer<PERSON> n<PERSON>i", "Copy": "Nus<PERSON>lash", "Copy Formatted Text": "Formatlangan matnni nus<PERSON>", "Copy last code block": "O<PERSON><PERSON>i kod blokidan nusxa oling", "Copy last response": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> nusxa oling", "Copy link": "", "Copy Link": "<PERSON><PERSON><PERSON> n<PERSON>", "Copy to clipboard": "<PERSON>ufer<PERSON> nusxalash", "Copying to clipboard was successful!": "<PERSON><PERSON><PERSON><PERSON> nusxalash muva<PERSON>aq<PERSON>tli boʻldi!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "Open WebUI soʻrovlariga ruxsat berish uchun CORS provayder tomonidan toʻgʻri sozlangan boʻlishi kerak.", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "<PERSON><PERSON><PERSON><PERSON> bazasini ya<PERSON>", "Create a model": "Model ya<PERSON>ish", "Create Account": "<PERSON><PERSON>", "Create Admin Account": "Administrator his<PERSON><PERSON> ya<PERSON>", "Create Channel": "<PERSON><PERSON> ya<PERSON>", "Create Folder": "", "Create Group": "<PERSON><PERSON>", "Create Knowledge": "<PERSON><PERSON><PERSON>", "Create new key": "<PERSON><PERSON> ka<PERSON>", "Create new secret key": "<PERSON><PERSON> max<PERSON>y kalit ya<PERSON>", "Create Note": "<PERSON><PERSON><PERSON><PERSON>", "Create your first note by clicking on the plus button below.": "<PERSON><PERSON><PERSON><PERSON> or<PERSON> bosish orqali birinchi qay<PERSON> ya<PERSON>.", "Created at": "<PERSON><PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON><PERSON>", "Created by": "tomonidan ya<PERSON>", "CSV Import": "CSV importi", "Ctrl+Enter to Send": "Yuborish uchun Ctrl+Enter", "Current Model": "<PERSON><PERSON><PERSON>", "Current Password": "<PERSON><PERSON><PERSON>", "Custom": "<PERSON><PERSON>", "Custom description enabled": "", "Custom Parameter Name": "Maxsus parametr nomi", "Custom Parameter Value": "<PERSON><PERSON> parametr qiymati", "Danger Zone": "Xavfli zona", "Dark": "Qorong'i", "Database": "<PERSON>'l<PERSON><PERSON><PERSON> bazasi", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "Datalab Marker API kaliti talab qilinadi.", "DD/MM/YYYY": "", "December": "dekabr", "Default": "<PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON> (Ochiq AI)", "Default (SentenceTransformers)": "<PERSON><PERSON><PERSON><PERSON> (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Standart model", "Default model updated": "Standart model ya<PERSON><PERSON><PERSON>", "Default Models": "Standart modellar", "Default permissions": "<PERSON><PERSON><PERSON><PERSON> rux<PERSON>", "Default permissions updated successfully": "<PERSON><PERSON><PERSON><PERSON> ruxsatlar muvaffaqiyatli yang<PERSON>ndi", "Default Prompt Suggestions": "<PERSON><PERSON> tak<PERSON>lar", "Default to 389 or 636 if TLS is enabled": "<PERSON>gar TLS yoqilgan bo'<PERSON><PERSON>, su<PERSON><PERSON> b<PERSON>'<PERSON><PERSON><PERSON> 389 yoki 636", "Default to ALL": "ALL uchun birlamchi", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Fokuslangan va tegishli kontentni ajratib olish uchun birlamchi <PERSON> qidirish, bu koʻp hollarda tavsiya etiladi.", "Default User Role": "<PERSON><PERSON><PERSON><PERSON>oy<PERSON> roli", "Delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Delete a model": "<PERSON><PERSON>irish", "Delete All Chats": "<PERSON><PERSON> o'chirish", "Delete All Models": "<PERSON><PERSON> o<PERSON>chirish", "Delete chat": "<PERSON><PERSON><PERSON> o'chirish", "Delete Chat": "<PERSON><PERSON><PERSON>", "Delete chat?": "Chat o<PERSON><PERSON><PERSON><PERSON><PERSON>?", "Delete folder?": "<PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON>?", "Delete function?": "<PERSON><PERSON><PERSON> o'chirilsinmi?", "Delete Message": "<PERSON><PERSON><PERSON>irish", "Delete message?": "<PERSON><PERSON>?", "Delete note?": "<PERSON><PERSON>d o<PERSON>chiri<PERSON><PERSON><PERSON>?", "Delete prompt?": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>?", "delete this link": "<PERSON><PERSON><PERSON> ha<PERSON><PERSON> o'chiring", "Delete tool?": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "Delete User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "<PERSON>ʻ<PERSON><PERSON>di {{deleteModelTag}}", "Deleted {{name}}": "{{name}} o<PERSON><PERSON><PERSON><PERSON>", "Deleted User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> foy<PERSON>", "Deployment names are required for Azure OpenAI": "Azure OpenAI uchun tarqatish nomlari talab qilinadi", "Describe Pictures in Documents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rasmlarga tavsif bering", "Describe your knowledge base and objectives": "<PERSON><PERSON>m bazasi va maqsadlaringizni tavsiflang", "Description": "<PERSON><PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "Artefaktlarni avtomatik aniqlash", "Dictate": "Diktatsiya qilish", "Didn't fully follow instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to'liq amal qil<PERSON>i", "Direct": "To'g'ridan-to'g'ri", "Direct Connections": "To'g'ridan-to'g'ri ulanishlar", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "To'g'ridan-to'g'ri ulanishlar foydalanuvchilarga o'zlarining OpenAI-ga mos keluvchi API so'nggi nuqtalariga ulanish imkonini beradi.", "Direct Tool Servers": "To'g'ridan-to'g'ri as<PERSON><PERSON><PERSON>i", "Disable Code Interpreter": "", "Disable Image Extraction": "Rasm chiq<PERSON><PERSON>i o'chirib qo'ying", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "PDF-dan tasvirni ajratib o<PERSON> o'chirib qo'ying. Agar LLM dan foydalanish yoqilgan boʻlsa, tasvirlarga avtomatik sarlavha qoʻyiladi. Birlamchi parametrlar False.", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a function": "<PERSON><PERSON><PERSON><PERSON> kash<PERSON> q<PERSON>h", "Discover a model": "<PERSON><PERSON> ka<PERSON> q<PERSON>", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> kashf qiling", "Discover a tool": "<PERSON><PERSON><PERSON><PERSON> kashf q<PERSON>", "Discover how to use Open WebUI and seek support from the community.": "Open WebUI-dan qanday foydal<PERSON><PERSON>ni bilib oling va hamja<PERSON><PERSON>dan yordam so'rang.", "Discover wonders": "<PERSON><PERSON>'ji<PERSON><PERSON><PERSON> ka<PERSON> eting", "Discover, download, and explore custom functions": "<PERSON><PERSON> ka<PERSON>, y<PERSON><PERSON><PERSON> oling va o'r<PERSON>ing", "Discover, download, and explore custom prompts": "<PERSON><PERSON> takli<PERSON> kash<PERSON>, y<PERSON><PERSON><PERSON> oling va oʻ<PERSON>ing", "Discover, download, and explore custom tools": "<PERSON><PERSON> vos<PERSON><PERSON>ni ka<PERSON> q<PERSON>, y<PERSON><PERSON><PERSON> oling va o'r<PERSON>ing", "Discover, download, and explore model presets": "Model <PERSON><PERSON><PERSON><PERSON><PERSON> kashf eting, y<PERSON><PERSON><PERSON> oling va o'r<PERSON>ing", "Display": "<PERSON><PERSON><PERSON>", "Display Emoji in Call": "Chaqiruvda kulgichlarni ko‘rsatish", "Display the username instead of You in the Chat": "Chatda Siz o'rniga foydalanuvchi nomini ko'rsating", "Displays citations in the response": "Javobda i<PERSON>boslar<PERSON> k<PERSON>'r<PERSON>adi", "Dive into knowledge": "<PERSON><PERSON><PERSON><PERSON> sho'ng'ing", "Do not install functions from sources you do not fully trust.": "<PERSON><PERSON><PERSON><PERSON> is<PERSON>mayd<PERSON> manbalardan funksiyalarni oʻrnatmang.", "Do not install tools from sources you do not fully trust.": "O'zing<PERSON> ishonma<PERSON><PERSON> manbalardan asboblarni o'rnatmang.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling Server URL manzili talab qilinadi.", "Document": "<PERSON><PERSON><PERSON><PERSON>", "Document Intelligence": "<PERSON><PERSON><PERSON><PERSON>", "Document Intelligence endpoint and key required.": "Document Intelligence so‘nggi nuqtasi va kalit talab qilinadi.", "Documentation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Documents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "hech qanday tashqi ulanishlarni amalga oshirmaydi va sizning ma'lumotlaringiz mahalliy serveringizda xavfsiz saqlanadi.", "Domain Filter List": "Domen filt<PERSON> ro'yxati", "Don't have an account?": "His<PERSON><PERSON><PERSON> yo'qmi?", "don't install random functions from sources you don't trust.": "o'zing<PERSON> ishonma<PERSON><PERSON> manbalardan tasodifiy funksiyalarni o'rnatmang.", "don't install random tools from sources you don't trust.": "o'zingiz ishonmaydigan manbalardan tasodifiy vositalarni o'rnatmang.", "Don't like the style": "Uslub yoqmaydi", "Done": "<PERSON><PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Download as SVG": "SVG sifatida yuklab oling", "Download canceled": "<PERSON><PERSON><PERSON> olish bekor qilindi", "Download Database": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bazasini yuk<PERSON>b olish", "Drag and drop a file to upload or select a file to view": "Yu<PERSON>sh uchun faylni sudrab tashlang yoki ko‘rish uchun faylni tanlang", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to upload": "<PERSON><PERSON><PERSON> uchun istalgan faylni shu yerga tashlang", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "ma<PERSON>an. '30s', '10m'. <PERSON><PERSON><PERSON><PERSON> vaqt birl<PERSON> 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "masalan. \"json\" yoki J<PERSON> sxemasi", "e.g. 60": "masalan. 60", "e.g. A filter to remove profanity from text": "masalan. <PERSON><PERSON><PERSON> ha<PERSON> so'zlarni olib tashlash uchun filtr", "e.g. en": "masalan. uz", "e.g. My Filter": "masalan. Mening filtrim", "e.g. My Tools": "masalan. <PERSON><PERSON>", "e.g. my_filter": "masalan. my_filtr", "e.g. my_tools": "masalan. my_tools", "e.g. pdf, docx, txt": "masalan. pdf, docx, txt", "e.g. Tools for performing various operations": "masalan. <PERSON>r xil operatsiya<PERSON>ni bajarish uchun as<PERSON><PERSON>r", "e.g., 3, 4, 5 (leave blank for default)": "<PERSON><PERSON><PERSON>, 3, 4, 5 (su<PERSON><PERSON> bo<PERSON><PERSON><PERSON> bo<PERSON><PERSON> qoldiring)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "<PERSON><PERSON><PERSON>, en-<PERSON>,ja-<PERSON> (avtomatik aniq<PERSON> uchun boʻsh qoldiring)", "e.g., westus (leave blank for eastus)": "ma<PERSON>an, westus (estus uchun bo'sh qoldiring)", "e.g.) en,fr,de": "masalan) en,fr,de", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Arena modelini <PERSON>", "Edit Channel": "<PERSON><PERSON><PERSON>", "Edit Connection": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "Edit Default Permissions": "<PERSON>art rux<PERSON><PERSON><PERSON> ta<PERSON>", "Edit Folder": "", "Edit Memory": "<PERSON><PERSON><PERSON><PERSON>", "Edit User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "Edit User Group": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> guru<PERSON>i ta<PERSON>", "Edited": "", "Editing": "", "Eject": "<PERSON><PERSON><PERSON><PERSON>", "ElevenLabs": "ElevenLabs", "Email": "Elektron pochta", "Embark on adventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kiri<PERSON>", "Embedding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Embedding Batch Size": "O'<PERSON><PERSON><PERSON> to'plami hajmi", "Embedding Model": "<PERSON><PERSON><PERSON><PERSON><PERSON> modeli", "Embedding Model Engine": "Dvigatel model<PERSON> o'r<PERSON>ish", "Embedding model set to \"{{embedding_model}}\"": "Oʻrnatish modeli “{{embedding_model}}”ga oʻrnatildi", "Enable API Key": "API kalitini yoqish", "Enable autocomplete generation for chat messages": "<PERSON>t x<PERSON>i uchun avtomatik to‘l<PERSON>ishni yaratishni yoqing", "Enable Code Execution": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> yo<PERSON>", "Enable Code Interpreter": "<PERSON><PERSON> tar<PERSON><PERSON> yo<PERSON>", "Enable Community Sharing": "Hamjamiyat bilan almashishni yoqing", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Model ma'lumot<PERSON><PERSON>dan almashtirishning oldini olish uchun Xotirani qulflashni (mlock) yoqing. Ushbu parametr modelning ishlaydigan sahifalar to'plamini RAMga bloklaydi va ular diskka almashtirilmasligini ta'minlaydi. Bu sahifa xatolaridan qochish va ma'lumotlarga tezkor kirishni ta'minlash orqali ishlashni saqlashga yordam beradi.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Model ma'lumot<PERSON><PERSON> yuklash uchun Xotira xaritasini (mmap) yoqing. Ushbu parametr tizimga disk fayllarini operativ xotirada bo'lganidek davolash orqali RAM kengaytmasi sifatida disk xotirasidan foydalanish imkonini beradi. Bu maʼlumotlarga tezroq kirish imkonini berish orqali model ish faoliyatini yaxshilashi mumkin. Biroq, u barcha tizimlar bilan to'g'ri ishlamasligi va katta hajmdagi disk maydonini iste'mol qilishi mumkin.", "Enable Message Rating": "Xabar re<PERSON>ini yoqish", "Enable Mirostat sampling for controlling perplexity.": "Ajablanishni nazorat qilish uchun Mirostat namunasini yoqing.", "Enable New Sign Ups": "<PERSON><PERSON> ro'yxa<PERSON><PERSON> o'tishni yoqing", "Enabled": "Yoqilgan", "Endpoint URL": "Oxirgi nuqta URL", "Enforce Temporary Chat": "Vaqtinchal<PERSON> suh<PERSON> joriy qilish", "Enhance": "<PERSON><PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSV faylingiz quyidagi tartibda 4 ta ustundan iboratligiga ishonch hosil qiling: Ism, Elektron pochta, Parol, Rol.", "Enter {{role}} message here": "<PERSON>u yerga {{role}} x<PERSON><PERSON> kiriting", "Enter a detail about yourself for your LLMs to recall": "LLMlar eslab qolishlari uchun oʻ<PERSON>iz ha<PERSON>da maʼlumot kiriting", "Enter a title for the pending user info overlay. Leave empty for default.": "Kutilayotgan foydalanuvchi maʼlumotlari uchun sarlavha kiriting. <PERSON><PERSON><PERSON> bo'yicha bo'sh qoldiring.", "Enter a watermark for the response. Leave empty for none.": "<PERSON><PERSON><PERSON> uchun moy<PERSON>'yoqli belgini kiriting. Hech kim uchun bo'sh qoldiring.", "Enter api auth string (e.g. username:password)": "api auth satrini kiriting (ma<PERSON><PERSON>, foydalanuvchi nomi: parol)", "Enter Application DN": "Ilova DN ni kiriting", "Enter Application DN Password": "Ilova DN parolini kiriting", "Enter Bing Search V7 Endpoint": "Bing Search V7 oxirgi nuq<PERSON>ini kiriting", "Enter Bing Search V7 Subscription Key": "Bing Search V7 obuna kalitini kiriting", "Enter BM25 Weight": "BM25 vaznini kiriting", "Enter Bocha Search API Key": "Bocha Search API kalitini kiriting", "Enter Brave Search API Key": "Brave Search API kalitini kiriting", "Enter certificate path": "<PERSON><PERSON><PERSON><PERSON> yo'lini kiriting", "Enter CFG Scale (e.g. 7.0)": "CFG shkalasini kiri<PERSON> (ma<PERSON><PERSON>, 7.0)", "Enter Chunk Overlap": "Chunk Overlap-ni kiriting", "Enter Chunk Size": "Chunk hajmini kiriting", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Vergul bilan ajratilgan \"token:bias_value\" juf<PERSON><PERSON><PERSON> kiri<PERSON> (misol: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "Kutilayotgan foydalanuvchi ma'lumotlari qoplamasi uchun tarkibni kiriting. <PERSON><PERSON><PERSON> bo'yicha bo'sh qoldiring.", "Enter Datalab Marker API Key": "Datalab Marker API kalitini kiriting", "Enter description": "Tavsi<PERSON><PERSON> kiri<PERSON>", "Enter Docling OCR Engine": "Docling OCR mexanizmini kiriting", "Enter Docling OCR Language(s)": "Docling OCR til(lar)ini kiriting", "Enter Docling Server URL": "Docling Server URL manzilini kiriting", "Enter Document Intelligence Endpoint": "Document Intelligence Endpoint-ni kiriting", "Enter Document Intelligence Key": "Hujjatning razvedka kalitini kiriting", "Enter domains separated by commas (e.g., example.com,site.org)": "Domenlarni vergul bilan ajrating (masalan, example.com,site.org)", "Enter Exa API Key": "Exa API kalitini kiriting", "Enter External Document Loader API Key": "Tashqi hujjat yuklov<PERSON> API kalitini kiriting", "Enter External Document Loader URL": "Tashqi hujjat yuklovchi URL manzilini kiriting", "Enter External Web Loader API Key": "Tashqi Web Loader API kalitini kiriting", "Enter External Web Loader URL": "Tashqi Web Loader URL manzilini kiriting", "Enter External Web Search API Key": "Tashqi veb-qidiruv API kalitini kiriting", "Enter External Web Search URL": "Tashqi veb-qidiruv URL manzilini kiriting", "Enter Firecrawl API Base URL": "Firecrawl API bazasi URL manzilini kiriting", "Enter Firecrawl API Key": "Firecrawl API kalitini kiriting", "Enter folder name": "", "Enter Github Raw URL": "Github Raw URL manzilini kiriting", "Enter Google PSE API Key": "Google PSE API kalitini kiriting", "Enter Google PSE Engine Id": "Google PSE Engine identifikatorini kiriting", "Enter Image Size (e.g. 512x512)": "Rasm hajmini kiriting (masalan, 512x512)", "Enter Jina API Key": "Jina API kalitini kiriting", "Enter Jupyter Password": "<PERSON><PERSON><PERSON> parolini kiriting", "Enter Jupyter Token": "<PERSON><PERSON><PERSON> kiriting", "Enter Jupyter URL": "Jupyter URL manzilini kiriting", "Enter Kagi Search API Key": "Kagi Search API kalitini kiriting", "Enter Key Behavior": "<PERSON><PERSON><PERSON><PERSON>-harak<PERSON><PERSON> kiriting", "Enter language codes": "<PERSON>il kodlarini kiriting", "Enter Mistral API Key": "Mistral API kalitini kiriting", "Enter Model ID": "Model identifikatorini kiriting", "Enter model tag (e.g. {{modelTag}})": "Model tegini kiriting (ma<PERSON>an, {{modelTag}})", "Enter Mojeek Search API Key": "Mojeek Search API kalitini kiriting", "Enter name": "Ismni kiriting", "Enter New Password": "<PERSON><PERSON> par<PERSON>ni kiriting", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> sonini kiri<PERSON> (<PERSON><PERSON><PERSON>, 50)", "Enter Perplexity API Key": "Perplexity API kalitini kiriting", "Enter Playwright Timeout": "Dramaturg vaq<PERSON>i kiriting", "Enter Playwright WebSocket URL": "Playwright WebSocket URL manzilini kiriting", "Enter proxy URL (e.g. **************************:port)": "Proksi-serverning URL manzilini kiriting (masalan, **************************:port)", "Enter reasoning effort": "Fikrlash harakatini kiriting", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON> kiriting (masalan, Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kiri<PERSON> (masalan, Karras)", "Enter Score": "<PERSON><PERSON>i kiriting", "Enter SearchApi API Key": "SearchApi API kalitini kiriting", "Enter SearchApi Engine": "SearchApi tizimiga kiring", "Enter Searxng Query URL": "Searxng so'rovi URL manzilini kiriting", "Enter Seed": "Seed-ga kiring", "Enter SerpApi API Key": "SerpApi API kalitini kiriting", "Enter SerpApi Engine": "SerpApi dvigateliga kiring", "Enter Serper API Key": "Serper API kalitini kiriting", "Enter Serply API Key": "Serply API kalitini kiriting", "Enter Serpstack API Key": "Serpstack API kalitini kiriting", "Enter server host": "Server xostiga kiring", "Enter server label": "Server yorlig'ini kiriting", "Enter server port": "Server portini kiriting", "Enter Sougou Search API sID": "Sougou Search API sID ni kiriting", "Enter Sougou Search API SK": "Sougou Search API SK ni kiriting", "Enter stop sequence": "To'xtash ketma-ketligini kiriting", "Enter system prompt": "<PERSON><PERSON><PERSON> so'rovini kiriting", "Enter system prompt here": "Bu erda tizim so'rovini kiriting", "Enter Tavily API Key": "Tavily API kalitini kiriting", "Enter Tavily Extract Depth": "<PERSON><PERSON> ekstrakti chuqurligini kiriting", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "WebUI ning umumiy URL manzilini kiriting. Bu URL bildirishnomalarda havolalar yaratish uchun ishlatiladi.", "Enter the URL of the function to import": "Import qilinadigan funksiyaning URL manzilini kiriting", "Enter the URL to import": "Import qilish uchun URL manzilini kiriting", "Enter Tika Server URL": "Tika Server URL manzilini kiriting", "Enter timeout in seconds": "<PERSON><PERSON>t tugashini soniya<PERSON>a kiriting", "Enter to Send": "Yuborish uchun kiring", "Enter Top K": "Top K.ga kiring", "Enter Top K Reranker": "Top K Reranker-ga kiring", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL manzilini kiriting (masalan, http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL manzilini kiriting (masalan, http://localhost:11434)", "Enter Yacy Password": "<PERSON><PERSON> parolini kiriting", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Yacy URL manzilini kiriting (ma<PERSON>an, http://yacy.example.com:8090)", "Enter Yacy Username": "<PERSON><PERSON> foy<PERSON><PERSON><PERSON> nomini kiriting", "Enter your current password": "<PERSON><PERSON><PERSON> kiri<PERSON>", "Enter Your Email": "Elektron pochtangizni kiriting", "Enter Your Full Name": "<PERSON><PERSON><PERSON><PERSON> is<PERSON><PERSON>ni kiriting", "Enter your message": "Xabaringizni kiriting", "Enter your name": "Ismingizni kiri<PERSON>", "Enter Your Name": "Ismingizni kiri<PERSON>", "Enter your new password": "Yangi parolingizni kiriting", "Enter Your Password": "Parolingizni kiriting", "Enter Your Role": "Rolingizni kiriting", "Enter Your Username": "Foydalanuvchi nomingizni kiriting", "Enter your webhook URL": "Vebhuk URL manzilingizni kiriting", "Error": "Xato", "ERROR": "XATO", "Error accessing Google Drive: {{error}}": "Google Drive-ga kirishda xatolik yuz berdi: {{error}}", "Error accessing media devices.": "Media qurilmalariga kirishda xatolik yuz berdi.", "Error starting recording.": "<PERSON><PERSON><PERSON><PERSON> bosh<PERSON>da xatolik yuz berdi.", "Error unloading model: {{error}}": "<PERSON><PERSON> y<PERSON> x<PERSON> yuz berdi: {{error}}", "Error uploading file: {{error}}": "<PERSON><PERSON><PERSON> yuk<PERSON> x<PERSON>lik yuz berdi: {{error}}", "Evaluations": "<PERSON><PERSON><PERSON>", "Everyone": "", "Exa API Key": "Exa API kaliti", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Misol: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Misol: ALL", "Example: mail": "Misol: pochta", "Example: ou=users,dc=foo,dc=example": "Misol: ou=users,dc=foo,dc=misol", "Example: sAMAccountName or uid or userPrincipalName": "Misol: sAMAccountName yoki uid yoki userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Litsenziyangizdagi oʻrinlar sonidan oshib ketdi. O'rindiqlar sonini ko'pay<PERSON><PERSON>h uchun qo'llab-quvvatlash xizmatiga murojaat qiling.", "Exclude": "Cheklash", "Execute code for analysis": "<PERSON><PERSON><PERSON> q<PERSON>h uchun kodni bajaring", "Executing **{{NAME}}**...": "**{{NAME}}** bajarilmoqda...", "Expand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Experimental": "Eksperimental", "Explain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "Kosmosni o'rganing", "Export": "Eksport", "Export All Archived Chats": "Barcha arxivlangan suhbatlarni eksport qilish", "Export All Chats (All Users)": "Barcha suhbatlarni eksport qilish (barcha foydalanuvchilar)", "Export chat (.json)": "Chatni eksport qilish (.json)", "Export Chats": "Chatlarni eksport qilish", "Export Config to JSON File": "Konfiguratsiyani JSON fayliga eksport qiling", "Export Functions": "Eksport funktsiyalari", "Export Models": "Eksport modellari", "Export Presets": "Oldindan sozlamalarni eksport qilish", "Export Prompt Suggestions": "Eksport b<PERSON>'y<PERSON>a <PERSON>", "Export Prompts": "Eksport takliflari", "Export to CSV": "CSV ga eksport qilish", "Export Tools": "Eksport vositalari", "External": "<PERSON><PERSON><PERSON>", "External Document Loader URL required.": "Tashqi hujjat yuklovchi URL manzili talab qilinadi.", "External Task Model": "Tashqi vazifa modeli", "External Web Loader API Key": "Tashqi Web Loader API kaliti", "External Web Loader URL": "Tashqi veb yuklovchi URL manzili", "External Web Search API Key": "Tashqi veb-qidiruv API kaliti", "External Web Search URL": "Tashqi veb-qidiruv URL manzili", "Fade Effect for Streaming Text": "", "Failed to add file.": "<PERSON><PERSON> qo‘shib bo<PERSON>l<PERSON>i.", "Failed to connect to {{URL}} OpenAPI tool server": "{{URL}} OpenAPI asbob serveriga ulanib boʻlmadi", "Failed to copy link": "<PERSON><PERSON><PERSON> nus<PERSON><PERSON><PERSON> bo<PERSON>l<PERSON>i", "Failed to create API Key.": "API kalitini yaratib bo<PERSON>l<PERSON>i.", "Failed to delete note": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>chirib bo<PERSON>l<PERSON>i", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "<PERSON><PERSON><PERSON> olib bo<PERSON>l<PERSON>i", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "<PERSON><PERSON> yuk<PERSON> bo<PERSON><PERSON>.", "Failed to read clipboard contents": "<PERSON><PERSON><PERSON> tark<PERSON> o‘qib bo‘l<PERSON>i", "Failed to save connections": "<PERSON><PERSON><PERSON><PERSON>", "Failed to save models configuration": "<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Failed to update settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yang<PERSON> bo<PERSON>l<PERSON>i", "Failed to upload file.": "<PERSON><PERSON>.", "Features": "Xususiyatlari", "Features Permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Fikr-m<PERSON><PERSON><PERSON> ta<PERSON>i", "Feedbacks": "<PERSON>kr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Feel free to add specific details": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON><PERSON><PERSON> qo'shishing<PERSON> mumkin", "File": "<PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON> m<PERSON><PERSON> qo's<PERSON>.", "File content updated successfully.": "<PERSON><PERSON> ma<PERSON>muni muva<PERSON>tl<PERSON> yang<PERSON>.", "File Mode": "<PERSON><PERSON>", "File not found.": "<PERSON><PERSON>.", "File removed successfully.": "<PERSON><PERSON><PERSON> olib ta<PERSON>.", "File size should not exceed {{maxSize}} MB.": "<PERSON><PERSON> hajmi {{maxSize}} MB dan oshmasligi kerak.", "File Upload": "<PERSON><PERSON>", "File uploaded successfully": "<PERSON><PERSON><PERSON> yuk<PERSON>i", "Files": "<PERSON><PERSON>", "Filter": "", "Filter is now globally disabled": "Filtr endi butun dunyo bo'ylab o'chirib qo'yilgan", "Filter is now globally enabled": "Filtr endi global miqyosda yoqilgan", "Filters": "Filtrlar", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "<PERSON><PERSON><PERSON> izi al<PERSON>: avatar si<PERSON><PERSON><PERSON> bosh harf<PERSON>an foydal<PERSON>sh imkonsiz. Standart profil rasmi.", "Firecrawl API Base URL": "Firecrawl API asosiy URL manzili", "Firecrawl API Key": "Firecrawl API kaliti", "Fluidly stream large external response chunks": "<PERSON><PERSON> ta<PERSON> javob b<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Focus chat input": "<PERSON>t kiritis<PERSON> fokuslash", "Folder deleted successfully": "<PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Folder Name": "", "Folder name cannot be empty.": "Jild nomi boʻsh boʻlishi mumkin emas.", "Folder name updated successfully": "Jild nomi muva<PERSON>aq<PERSON><PERSON>i yang<PERSON>", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mukammal amal qildi", "Force OCR": "OCRni ma<PERSON>", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "PDF-ning barcha sahifalarida OCRni majburlash. Agar PDF-fayllaringizda yaxshi matn bo'l<PERSON>, bu yomon natijalarga olib kelishi mumkin. Birlamchi parametrlar False.", "Forge new paths": "<PERSON><PERSON> yo'll<PERSON>ni oching", "Form": "<PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "O'z<PERSON>uv<PERSON>larni quyidagi kabi qavslar yordamida formatlang:", "Forwards system user session credentials to authenticate": "Autentifikatsiya qilish uchun tizim foydalanuvchisi seansi hisob ma'lumotlarini yo'naltiradi", "Full Context Mode": "<PERSON><PERSON>l<PERSON> konte<PERSON>t rejimi", "Function": "Funktsiya", "Function Calling": "<PERSON><PERSON><PERSON><PERSON> chaq<PERSON>", "Function created successfully": "Funktsiya muvaffaqiyatli yaratildi", "Function deleted successfully": "Funktsiya muvaffaq<PERSON><PERSON>i o'chiri<PERSON>i", "Function Description": "Funktsiya tavsifi", "Function ID": "Funktsiya identifikatori", "Function imported successfully": "Funktsiya muvaffaqiyatli import qilindi", "Function is now globally disabled": "Funktsiya endi butun dunyo bo'y<PERSON><PERSON> o'chiri<PERSON>gan", "Function is now globally enabled": "Funktsiya endi global miqyosda yoqilgan", "Function Name": "Funktsiya nomi", "Function updated successfully": "Funktsiya muvaffaqiyatli yang<PERSON>ndi", "Functions": "<PERSON><PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution.": "Funktsiyalar o'zboshimchalik bilan kodni bajarishga imkon beradi.", "Functions imported successfully": "Funktsiyalar muvaffaqiyatli import qilindi", "Gemini": "Egizaklar", "Gemini API Config": "Gemini API konfiguratsiyasi", "Gemini API Key is required.": "Gemini API kaliti talab qilinadi.", "General": "General", "Generate": "<PERSON><PERSON><PERSON>", "Generate an image": "<PERSON><PERSON><PERSON>", "Generate Image": "<PERSON><PERSON> ya<PERSON>ish", "Generate prompt pair": "Tezkor juftlikni yarating", "Generating search query": "<PERSON><PERSON><PERSON><PERSON> so'rovi ya<PERSON><PERSON>", "Generating...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "Get information on {{name}} in the UI": "", "Get started": "<PERSON><PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "{{WEBUI_NAME}} bilan boshlang", "Global": "Global", "Good Response": "<PERSON><PERSON><PERSON> javob", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API kaliti", "Google PSE Engine Id": "Google PSE Engine identifikatori", "Group created successfully": "<PERSON><PERSON> muva<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Group deleted successfully": "<PERSON><PERSON> muva<PERSON><PERSON><PERSON><PERSON><PERSON>", "Group Description": "<PERSON><PERSON> tav<PERSON><PERSON>", "Group Name": "<PERSON><PERSON> nomi", "Group updated successfully": "<PERSON><PERSON> muva<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Groups": "<PERSON><PERSON><PERSON>", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Haptik fikr-mulohazalar", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Fikr-muloha<PERSON><PERSON><PERSON> tarixini baham ko‘rish orqali eng yaxshi hamjamiyat yetakchilari jadvalini yaratishga yordam bering!", "Hex Color": "<PERSON><PERSON><PERSON> bur<PERSON><PERSON> rang", "Hex Color - Leave empty for default color": "Hex Color - standart rang uchun bo'sh qoldiring", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Hide from Sidebar": "", "Hide Model": "<PERSON><PERSON>", "High Contrast Mode": "<PERSON><PERSON><PERSON> k<PERSON>", "Home": "U<PERSON>", "Host": "Xost", "How can I help you today?": "<PERSON>ugun sizga qanday yordam bera olaman?", "How would you rate this response?": "Bu javobni qanday bahola<PERSON>iz?", "HTML": "HTML", "Hybrid Search": "<PERSON><PERSON><PERSON> qidiruv", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Men o'qiganimni tan olaman va o'z harakatlarimning oqibatlarini tushunaman. Men o'zboshimchalik bilan kodni bajarish bilan bog'liq xavflardan xabardorman va manbaning ishon<PERSON>liligini tasdiqladim.", "ID": "ID", "iframe Sandbox Allow Forms": "iframe Sandbox ruxsat shakllari", "iframe Sandbox Allow Same Origin": "iframe Sandbox bir xil kelib chiqishiga ruxsat beradi", "Ignite curiosity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yo<PERSON>", "Image": "Rasm", "Image Compression": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "<PERSON><PERSON><PERSON>", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> (eksperimental)", "Image Generation Engine": "<PERSON><PERSON><PERSON> ya<PERSON> mexa<PERSON>", "Image Max Compression Size": "Tasvirning maksimal siqish hajmi", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "<PERSON><PERSON><PERSON> so'rovini yaratish", "Image Prompt Generation Prompt": "<PERSON><PERSON><PERSON> ya<PERSON> ta<PERSON>", "Image Settings": "<PERSON><PERSON>", "Images": "Tasvirlar", "Import": "Import", "Import Chats": "Chatlarni import qilish", "Import Config from JSON File": "JSON faylidan konfiguratsiyani import qiling", "Import From Link": "Havoladan import qilish", "Import Functions": "<PERSON><PERSON>rt <PERSON>", "Import Models": "Import modellari", "Import Notes": "Eslatmalarni import qilish", "Import Presets": "Oldindan sozlamalarni import qilish", "Import Prompt Suggestions": "Import bo'yicha ta<PERSON>lar", "Import Prompts": "Import ko'rsatmalari", "Import Tools": "Import vositalari", "Include": "O'z ichiga oladi", "Include `--api-auth` flag when running stable-diffusion-webui": "Stabil-diffusion-webui is<PERSON> `--api-auth` bayrog'ini qo'shing", "Include `--api` flag when running stable-diffusion-webui": "Stabil-diffusion-webui is<PERSON>ganda `--api` bayrog'ini qo'shing", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Algoritm yaratilgan matndan olingan fikr-mulohazaga qanchalik tez javob berishiga ta'sir qiladi. Pastroq o'rganish tezligi sozlashning sekinlash<PERSON><PERSON> olib keladi, yu<PERSON><PERSON> o'rganish tezligi esa algoritmni yanada sezgir qiladi.", "Info": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Kompleks qayta ishlash uchun butun tarkibni kontekst sifatida kiriting, bu murakkab so'rovlar uchun tavsiya etiladi.", "Input commands": "<PERSON><PERSON><PERSON>", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Github URL manzilidan oʻrnating", "Instant Auto-Send After Voice Transcription": "<PERSON><PERSON><PERSON><PERSON>kripsi<PERSON>dan keyin darhol avtomatik yub<PERSON>h", "Integration": "Integratsiya", "Interface": "Interfeys", "Invalid file content": "<PERSON><PERSON> ma<PERSON> noto‘g‘ri", "Invalid file format.": "<PERSON>l formati noto‘g‘ri.", "Invalid JSON file": "JSON fayli <PERSON>", "Invalid Tag": "Teg noto‘g‘ri", "is typing...": "yoz<PERSON><PERSON><PERSON>...", "Italic": "", "January": "<PERSON><PERSON>", "Jina API Key": "Jina API kaliti", "join our Discord for help.": "yordam uchun <PERSON>ga qo'shiling.", "JSON": "JSON", "JSON Preview": "JSON ko'rib chiqish", "July": "iyul", "June": "iyun", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT muddati", "JWT Token": "JWT tokeni", "Kagi Search API Key": "<PERSON><PERSON> qidiruv API kaliti", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON>", "Keyboard shortcuts": "Klavia<PERSON>i", "Knowledge": "<PERSON><PERSON><PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON><PERSON> kirish", "Knowledge Base": "", "Knowledge created successfully.": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON> ya<PERSON>.", "Knowledge deleted successfully.": "Ma<PERSON>lumot<PERSON> muvaffaq<PERSON>tl<PERSON> o<PERSON>.", "Knowledge Public Sharing": "<PERSON><PERSON><PERSON><PERSON><PERSON> omma<PERSON>y alma<PERSON>sh", "Knowledge reset successfully.": "<PERSON><PERSON>l<PERSON><PERSON><PERSON> qayta tik<PERSON>i.", "Knowledge updated successfully": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON> ya<PERSON>", "Kokoro.js (Browser)": "Kokoro.js (brauzer)", "Kokoro.js Dtype": "Kokoro.js D turi", "Label": "<PERSON><PERSON><PERSON>", "Landing Page Mode": "<PERSON><PERSON><PERSON> sa<PERSON> rejimi", "Language": "Til", "Language Locales": "<PERSON><PERSON><PERSON><PERSON> tillar", "Languages": "<PERSON><PERSON>", "Last Active": "<PERSON><PERSON><PERSON><PERSON> faol", "Last Modified": "<PERSON><PERSON><PERSON><PERSON>", "Last reply": "<PERSON><PERSON><PERSON><PERSON> javob", "LDAP": "LDAP", "LDAP server updated": "LDAP serveri yangilandi", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>i", "Learn more about OpenAPI tool servers.": "OpenAPI vositasi serverlari haqida ko'proq bilib oling.", "Leave empty for no compression": "", "Leave empty for unlimited": "<PERSON><PERSON><PERSON><PERSON> uchun bo'sh qoldiring", "Leave empty to include all models from \"{{url}}\" endpoint": "“{{url}}” soʻnggi nuqtasidagi barcha modellarni kiritish uchun boʻsh qoldiring", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "“{{url}}/api/tags” soʻnggi nuqtasidagi barcha modellarni kiritish uchun boʻsh qoldiring", "Leave empty to include all models from \"{{url}}/models\" endpoint": "“{{url}}/models” soʻnggi nuqtasidagi barcha modellarni kiritish uchun boʻsh qoldiring", "Leave empty to include all models or select specific models": "<PERSON><PERSON> modellarni kiritish yoki muayyan modellarni tanlash uchun bo'sh qoldiring", "Leave empty to use the default prompt, or enter a custom prompt": "Standart takli<PERSON>dan foy<PERSON><PERSON>sh uchun boʻsh qoldiring yoki maxsus taklifni kiriting", "Leave model field empty to use the default model.": "Standart modeldan foydal<PERSON>sh uchun model may<PERSON><PERSON> bo'sh qoldiring.", "License": "Litsenziya", "Lift List": "", "Light": "<PERSON><PERSON>", "Listening...": "Tinglanmoqda...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLM<PERSON> xato q<PERSON> mumkin. <PERSON><PERSON> ma'lumot<PERSON><PERSON> tasdi<PERSON>.", "Loader": "Yuklagich", "Loading Kokoro.js...": "Kokoro.js yuklanmoqda...", "Local": "<PERSON><PERSON><PERSON><PERSON>", "Local Task Model": "<PERSON><PERSON><PERSON><PERSON> vazi<PERSON>i", "Location access not allowed": "Joylashuvga ruxsat berilmagan", "Lost": "Yo'qot<PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Open WebUI hamjamiyati tomonidan yaratilgan", "Make password visible in the user interface": "", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> o'rab qo'yganingizga ishonch hosil qiling", "Make sure to export a workflow.json file as API format from ComfyUI.": "Workflow.json faylini ComfyUI’dan API formati sifatida eksport qilganingizga ishonch hosil qiling.", "Manage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Manage Direct Connections": "To'g'ridan-to'g'ri ul<PERSON><PERSON><PERSON> bosh<PERSON>h", "Manage Models": "<PERSON><PERSON><PERSON>", "Manage Ollama": "<PERSON><PERSON><PERSON> b<PERSON>", "Manage Ollama API Connections": "Ollama <PERSON> ulanish<PERSON><PERSON> boshqaring", "Manage OpenAI API Connections": "OpenAI API ulanishlarini boshqaring", "Manage Pipelines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Manage Tool Servers": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> b<PERSON>", "March": "Mart", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "", "Max Speakers": "<PERSON><PERSON><PERSON><PERSON>", "Max Upload Count": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON> soni", "Max Upload Size": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON> hajmi", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Bir vaqtning o'zida maksimal 3 ta modelni yuklab olish mumkin. Key<PERSON><PERSON>q qayta urinib ko‘ring.", "May": "may", "Memories accessible by LLMs will be shown here.": "LLMlar kirishi mumkin bo'lgan xotiralar bu erda ko'rsatiladi.", "Memory": "<PERSON><PERSON><PERSON>", "Memory added successfully": "<PERSON>otira muva<PERSON>aq<PERSON>tli qo'shildi", "Memory cleared successfully": "<PERSON>otira muva<PERSON>tl<PERSON>", "Memory deleted successfully": "<PERSON>otira muva<PERSON>tl<PERSON>", "Memory updated successfully": "<PERSON>otira muva<PERSON>tli ya<PERSON>", "Merge Responses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Merged Response": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> javob", "Message rating should be enabled to use this feature": "<PERSON>u <PERSON><PERSON><PERSON><PERSON> foydalanish uchun xabarlar reytingi yoqilishi kerak", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Ha<PERSON>lani yaratganingizdan keyin yuborgan xabarlaringiz ulashilmaydi. URL manzili bo'lgan foydalanuvchilar umumiy chatni ko'rishlari mumkin bo'ladi.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (shaxsiy)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (ish/maktab)", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "Mistral OCR API kaliti talab qilinadi.", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "“{{modelName}}” modeli yuk<PERSON>b o<PERSON>.", "Model '{{modelTag}}' is already in queue for downloading.": "“{{modelTag}}” modeli allaqachon yuklab olish uchun navbatda turibdi.", "Model {{modelId}} not found": "{{modelId}} modeli topilmadi", "Model {{modelName}} is not vision capable": "{{modelName}} modeli ko'rish qobiliyatiga ega emas", "Model {{name}} is now {{status}}": "{{name}} modeli endi {{status}}", "Model {{name}} is now hidden": "{{name}} modeli endi yashirin", "Model {{name}} is now visible": "{{name}} modeli endi koʻ<PERSON>di", "Model accepts file inputs": "Model fayl kiri<PERSON><PERSON><PERSON> qabul qiladi", "Model accepts image inputs": "Model rasm kiri<PERSON><PERSON>i qabul qiladi", "Model can execute code and perform calculations": "Model k<PERSON><PERSON> bajarishi va hisob-kitob<PERSON>ni amalga oshi<PERSON>hi mumkin", "Model can generate images based on text prompts": "Model matn tak<PERSON><PERSON><PERSON>i as<PERSON><PERSON> tasvir<PERSON>ni yaratishi mumkin", "Model can search the web for information": "Model ma'lumot uchun <PERSON> qidirishi mumkin", "Model created successfully!": "Model muva<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Model fayl tizimi yoʻ<PERSON>. <PERSON><PERSON><PERSON> model qisqa nomi talab qili<PERSON>, da<PERSON><PERSON> et<PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON>.", "Model Filtering": "<PERSON><PERSON>", "Model ID": "Model ID", "Model IDs": "Model identifikatorlari", "Model Name": "Model nomi", "Model not selected": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Model Params": "Model parametrlari", "Model Permissions": "Model rux<PERSON><PERSON><PERSON><PERSON>", "Model unloaded successfully": "Model m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yuk<PERSON><PERSON>i", "Model updated successfully": "Model m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Model(s) do not support file upload": "Model(lar) fay<PERSON>i yuk<PERSON> qo'llab-quv<PERSON><PERSON><PERSON><PERSON>", "Modelfile Content": "Model f<PERSON><PERSON> ta<PERSON>ibi", "Models": "<PERSON><PERSON>", "Models Access": "<PERSON><PERSON><PERSON> kirish", "Models configuration saved successfully": "<PERSON><PERSON> konfigu<PERSON><PERSON><PERSON><PERSON> muvaffaq<PERSON>tli saq<PERSON>i", "Models Public Sharing": "<PERSON><PERSON><PERSON> omma<PERSON>", "Mojeek Search API Key": "Mojeek qidiruv API kaliti", "more": "<PERSON><PERSON>proq", "More": "<PERSON><PERSON>proq", "Name": "Ism", "Name your knowledge base": "Bilimlar bazasini nomlang", "Native": "<PERSON><PERSON><PERSON><PERSON>", "New Chat": "<PERSON><PERSON> chat", "New Folder": "<PERSON><PERSON> jild", "New Function": "<PERSON><PERSON>", "New Note": "<PERSON><PERSON>", "New Password": "<PERSON><PERSON> parol", "New Tool": "<PERSON><PERSON> vosita", "new-channel": "yangi kanal", "Next message": "", "No chats found": "", "No chats found for this user.": "<PERSON>u foy<PERSON><PERSON><PERSON><PERSON> uchun hech qanday chat topilmadi.", "No chats found.": "<PERSON><PERSON> qanday chat top<PERSON><PERSON>i.", "No content": "Kontent yo'q", "No content found": "<PERSON><PERSON> qanday kontent topil<PERSON>i", "No content found in file.": "<PERSON><PERSON> kontent topil<PERSON>.", "No content to speak": "Gapiradigan tarkib yo'q", "No distance available": "<PERSON><PERSON><PERSON> mav<PERSON>as", "No feedbacks found": "<PERSON><PERSON> qanday fikr top<PERSON>i", "No file selected": "<PERSON><PERSON> qanday fayl tan<PERSON>gan", "No groups with access, add a group to grant access": "Ruxsat berilgan guruhlar yo‘q, ruxsat berish uchun guruh qo‘shing", "No HTML, CSS, or JavaScript content found.": "HTML, CSS yoki JavaScript kontenti topilmadi.", "No inference engine with management support found": "Boshqaruv yordami bilan xulosa chiqarish mexanizmi topilmadi", "No knowledge found": "<PERSON><PERSON> qanday bilim top<PERSON>i", "No memories to clear": "<PERSON><PERSON><PERSON> uchun xotiralar yo'q", "No model IDs": "Model identifika<PERSON><PERSON>i yo'q", "No models found": "<PERSON><PERSON> q<PERSON>y model <PERSON><PERSON><PERSON><PERSON>", "No models selected": "<PERSON><PERSON> qanday model <PERSON><PERSON><PERSON><PERSON>", "No Notes": "<PERSON><PERSON><PERSON><PERSON> yo'q", "No results found": "<PERSON><PERSON> qanday natija <PERSON>i", "No search query generated": "<PERSON><PERSON> qanday qidiruv soʻrovi yarat<PERSON>i", "No source available": "Man<PERSON> mavjud emas", "No users were found.": "<PERSON><PERSON> qanday foy<PERSON><PERSON><PERSON> topilmad<PERSON>.", "No valves to update": "<PERSON><PERSON><PERSON> uchun k<PERSON> yo'q", "None": "Yo'q", "Not factually correct": "<PERSON><PERSON><PERSON> to'g'ri emas", "Not helpful": "<PERSON><PERSON><PERSON><PERSON> emas", "Note deleted successfully": "<PERSON><PERSON><PERSON><PERSON> muva<PERSON> o<PERSON>", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Eslatma: <PERSON><PERSON> siz minimal ball qo'<PERSON><PERSON><PERSON>, qidiruv faqat minimal balldan kattaroq yoki unga teng ballga ega hujjatlarni qaytaradi.", "Notes": "Eslatmalar", "Notification Sound": "Bildirishnoma o<PERSON>zi", "Notification Webhook": "Bildirishnoma veb-huk", "Notifications": "Bildirishnomalar", "November": "noyabr", "OAuth ID": "OAuth ID", "October": "oktyabr", "Off": "Oʻchirilgan", "Okay, Let's Go!": "<PERSON><PERSON>, ketaylik!", "OLED Dark": "OLED qorong'i", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API sozlamalari yangilandi", "Ollama Version": "<PERSON><PERSON><PERSON> vers<PERSON>", "On": "<PERSON><PERSON><PERSON>", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "<PERSON><PERSON><PERSON> harf-raq<PERSON><PERSON> belgilar va defislarga ruxsat beriladi", "Only alphanumeric characters and hyphens are allowed in the command string.": "Buyruqlar qatorida faqat harf-raq<PERSON>li belgilar va defislarga ruxsat beriladi.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Faqat to'p<PERSON><PERSON>ni tahrirlash mumkin, huj<PERSON><PERSON><PERSON>ni tahrirlash/qo'shish uchun yangi bilimlar bazasini yarating.", "Only markdown files are allowed": "Faqat markdown fayllariga ruxsat beriladi", "Only select users and groups with permission can access": "<PERSON><PERSON><PERSON> ruxsati bor tanlangan foydalanu<PERSON><PERSON><PERSON> va guruhl<PERSON>ga kirish mumkin", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Voy! URL noto‘g‘ri ko‘rinadi. Il<PERSON><PERSON>, ikki marta tekshiring va qayta urinib ko'ring.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Voy! Hali ham fayllar yuk<PERSON>. Il<PERSON>mos, yuk<PERSON> tugashini kuting.", "Oops! There was an error in the previous response.": "Voy! Avvalgi javobda xatolik yuz berdi.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Voy! Siz qoʻllab-quvvatlanmaydigan usuldan foydalanmoqdasiz (faqat frontend). Iltimos, WebUI-ga backend or<PERSON>li xizmat ko'rsating.", "Open file": "<PERSON><PERSON><PERSON> o<PERSON>", "Open in full screen": "Toʻliq ekranda oching", "Open modal to configure connection": "", "Open new chat": "<PERSON><PERSON> chat oching", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI har qanday OpenAPI serveri tomonidan taqdim etilgan vositalardan foydalanishi mumkin.", "Open WebUI uses faster-whisper internally.": "Open WebUI ichki tez shivirlashdan foydalanadi.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI SpeechT5 va CMU Arctic dinamik oʻrnatishlaridan foydal<PERSON>di.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Ochiq WebUI versiyasi (v{{OPEN_WEBUI_VERSION}}) talab qilingan versiyadan (v{{REQUIRED_VERSION}}) pastroq", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API konfiguratsiyasi", "OpenAI API Key is required.": "OpenAI API kaliti talab qilinadi.", "OpenAI API settings updated": "OpenAI API sozlamalari yangilandi", "OpenAI URL/Key required.": "OpenAI URL/Kalit talab qilinadi.", "openapi.json URL or Path": "openapi.json URL yoki yoʻl", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "yoki", "Ordered List": "", "Organize your users": "Foydalanuvchilaringizni tartibga soling", "Other": "<PERSON><PERSON><PERSON>", "OUTPUT": "<PERSON><PERSON><PERSON>", "Output format": "<PERSON><PERSON><PERSON> formati", "Output Format": "<PERSON><PERSON><PERSON> formati", "Overview": "<PERSON><PERSON><PERSON>", "page": "<PERSON>hifa", "Paginate": "<PERSON><PERSON><PERSON><PERSON>", "Parameters": "Parametrlar", "Password": "<PERSON><PERSON>", "Paste Large Text as File": "<PERSON>ta matnni fayl sifatida joy<PERSON>", "PDF document (.pdf)": "PDF hujjat (.pdf)", "PDF Extract Images (OCR)": "PDF ekstrakti rasmlari (OCR)", "pending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pending": "", "Pending User Overlay Content": "Kutilayotgan foydalanuvchi Overlay kontenti", "Pending User Overlay Title": "<PERSON><PERSON><PERSON><PERSON><PERSON> foydal<PERSON><PERSON><PERSON> sa<PERSON>", "Permission denied when accessing media devices": "Media qurilmalarga kirishda ruxsat rad etildi", "Permission denied when accessing microphone": "Mikrofonga kirishda ruxsat beril<PERSON>i", "Permission denied when accessing microphone: {{error}}": "Mikrofonga kirishda ruxsat rad etildi: {{error}}", "Permissions": "Ruxsatlar", "Perplexity API Key": "Qiyinchilik API kaliti", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON>n", "Pinned": "Qadalgan", "Pioneer insights": "<PERSON><PERSON><PERSON>", "Pipe": "", "Pipeline deleted successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pipeline downloaded successfully": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON>", "Pipelines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pipelines Not Detected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pipelines Valves": "Quvur quvurlari k<PERSON>i", "Plain text (.md)": "<PERSON><PERSON><PERSON> (.md)", "Plain text (.txt)": "<PERSON><PERSON><PERSON> (.txt)", "Playground": "O'yin may<PERSON><PERSON><PERSON>", "Playwright Timeout (ms)": "Dramaturg ta<PERSON> (ms)", "Playwright WebSocket URL": "Dramaturg WebSocket URL manzili", "Please carefully review the following warnings:": "<PERSON><PERSON><PERSON><PERSON> og<PERSON> diqqat bilan ko'rib chiqing:", "Please do not close the settings page while loading the model.": "<PERSON><PERSON> y<PERSON> so<PERSON> sa<PERSON><PERSON><PERSON> yo<PERSON>.", "Please enter a prompt": "<PERSON><PERSON><PERSON>, tak<PERSON><PERSON> kiriting", "Please enter a valid path": "<PERSON><PERSON><PERSON><PERSON> yo‘lni kiriting", "Please enter a valid URL": "Yaroqli URL manzilini kiriting", "Please fill in all fields.": "<PERSON><PERSON><PERSON>, barcha may<PERSON> to<PERSON><PERSON><PERSON>.", "Please select a model first.": "<PERSON><PERSON><PERSON>, avval <PERSON>ni tan<PERSON>.", "Please select a model.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> tan<PERSON>.", "Please select a reason": "<PERSON><PERSON><PERSON><PERSON>", "Port": "Port", "Positive attitude": "<PERSON><PERSON><PERSON><PERSON>", "Prefix ID": "Prefiks identifikatori", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefiks identifikatori model identifikatorlariga prefiks qo'shish orqali boshqa ulanishlar bilan ziddiyatlarni oldini olish uchun ishlatiladi - o'chirish uchun bo'sh qoldiring.", "Prevent file creation": "", "Preview": "Ko<PERSON>rib chiqish", "Previous 30 days": "Oldingi 30 kun", "Previous 7 days": "Oldingi 7 kun", "Previous message": "", "Private": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Profile Image": "<PERSON>il rasmi", "Prompt": "Tezkor", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Tezkor (ma<PERSON>an, menga Rim imperiyasi haqida qiziqarli faktni aytib bering)", "Prompt Autocompletion": "Tezkor avtomatik yakunlash", "Prompt Content": "Tezkor tarkib", "Prompt created successfully": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Prompt suggestions": "Tezkor takliflar", "Prompt updated successfully": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Prompts Access": "<PERSON><PERSON><PERSON><PERSON> tak<PERSON><PERSON> qiladi", "Prompts Public Sharing": "<PERSON><PERSON><PERSON> ta<PERSON> q<PERSON>di", "Public": "<PERSON><PERSON><PERSON><PERSON>", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com saytidan “{{searchValue}}”ni torting", "Pull a model from Ollama.com": "Ollama.com dan modelni torting", "Query Generation Prompt": "So'rovni yaratish taklifi", "RAG Template": "RAG shabloni", "Rating": "<PERSON><PERSON>", "Re-rank models by topic similarity": "<PERSON><PERSON><PERSON> mav<PERSON> o'<PERSON><PERSON> bo'yicha qayta tartiblang", "Read": "<PERSON>'<PERSON>ing", "Read Aloud": "<PERSON><PERSON><PERSON> chiq<PERSON> o'qing", "Reason": "", "Reasoning Effort": "<PERSON><PERSON><PERSON><PERSON>", "Record": "<PERSON><PERSON><PERSON>", "Record voice": "<PERSON><PERSON><PERSON><PERSON> yozib oling", "Redirecting you to Open WebUI Community": "Sizni Open WebUI hamjamiyatiga yoʻnaltirmoqda", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "<PERSON><PERSON><PERSON><PERSON> narsa<PERSON> ya<PERSON> eh<PERSON> ka<PERSON>. <PERSON><PERSON><PERSON> qiymat (masalan, 100) turli xil javoblar beradi, past<PERSON><PERSON> qiymat (ma<PERSON><PERSON>, 10) esa konservativ bo'ladi.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"Foydalanu<PERSON><PERSON>\" deb ko'rsating (ma<PERSON><PERSON>, \"<PERSON>oydalanuvchi ispan tilini o'rganmoqda\")", "References from": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Refused when it shouldn't have": "Bo'lmasligi kerak bo'lganda rad etildi", "Regenerate": "<PERSON><PERSON><PERSON> tik<PERSON>", "Reindex": "<PERSON><PERSON><PERSON><PERSON>", "Reindex Knowledge Base Vectors": "Reindex bilimlar bazasi vektorlari", "Release Notes": "<PERSON><PERSON><PERSON><PERSON>", "Releases": "Relizlar", "Relevance": "Muvofiqlik", "Relevance Threshold": "Muvofiqlik chegarasi", "Remember Dismissal": "", "Remove": "O'chirish", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON> o<PERSON> tash<PERSON>", "Remove this tag from list": "", "Rename": "<PERSON><PERSON>", "Reorder Models": "<PERSON><PERSON><PERSON> qayta tartiblash", "Reply in Thread": "<PERSON><PERSON><PERSON><PERSON> javob bering", "Reranking Engine": "Dvigatelni qayta tartiblash", "Reranking Model": "<PERSON><PERSON><PERSON> tart<PERSON> modeli", "Reset": "<PERSON><PERSON><PERSON> tik<PERSON>", "Reset All Models": "<PERSON><PERSON> qayta o'r<PERSON>", "Reset Upload Directory": "<PERSON><PERSON><PERSON> katalog<PERSON> tiklash", "Reset Vector Storage/Knowledge": "Vektor <PERSON>/bilim<PERSON> qayta o'r<PERSON>ish", "Reset view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiklash", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "<PERSON><PERSON><PERSON> fao<PERSON> bo<PERSON><PERSON><PERSON>, chunki veb-sayt ruxsatnomalari rad etilgan. Ke<PERSON><PERSON> ruxsat berish uchun brauzer sozlam<PERSON>riga tashrif buyuring.", "Response splitting": "<PERSON><PERSON><PERSON><PERSON>", "Response Watermark": "<PERSON><PERSON><PERSON> suv belgisi", "Result": "Natija", "Retrieval": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval Query Generation": "<PERSON><PERSON><PERSON><PERSON> so'r<PERSON><PERSON><PERSON> yaratish", "Rich Text Input for Chat": "Chat uchun boy matn kiritish", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON> qara<PERSON>'ay", "Rosé Pine Dawn": "At<PERSON><PERSON><PERSON> qarag'ay tong", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON><PERSON>", "Save & Create": "<PERSON><PERSON><PERSON> va yaratish", "Save & Update": "<PERSON><PERSON><PERSON> va yang<PERSON>sh", "Save As Copy": "<PERSON><PERSON><PERSON> sifatida saq<PERSON>", "Save Tag": "<PERSON><PERSON> sa<PERSON>", "Saved": "<PERSON><PERSON><PERSON><PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Chat jurnallarini bevosita brauzeringiz xotirasiga saqlash endi qo‘llab-quvvatlanmaydi. Quyidagi tugmani bosish orqali suhbat jurnallaringizni yuklab oling va oʻchiring. <PERSON>avot<PERSON>, siz chat jurnallarini backend orqali osongina qayta import qilishingiz mumkin", "Scroll On Branch Change": "<PERSON><PERSON><PERSON> o'<PERSON><PERSON><PERSON><PERSON> bo'yicha a<PERSON>", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search a model": "<PERSON><PERSON>", "Search Base": "<PERSON><PERSON><PERSON><PERSON> bazasi", "Search Chats": "<PERSON><PERSON><PERSON><PERSON>", "Search Collection": "<PERSON><PERSON><PERSON><PERSON><PERSON> qidirish", "Search Filters": "Qidiruv filtrlari", "search for tags": "teg<PERSON><PERSON> q<PERSON>", "Search Functions": "<PERSON><PERSON><PERSON><PERSON>", "Search Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON> q<PERSON>", "Search Models": "<PERSON><PERSON><PERSON>", "Search Notes": "", "Search options": "Qidiruv <PERSON><PERSON>i", "Search Prompts": "Qidiruv ko'rsatmalari", "Search Result Count": "<PERSON><PERSON><PERSON><PERSON> natijalari soni", "Search the internet": "Internetda qidiring", "Search Tools": "Qidiruv vositalari", "SearchApi API Key": "SearchApi API kaliti", "SearchApi Engine": "Search<PERSON>pi <PERSON>", "Searched {{count}} sites": "{{count}} ta sayt qidirildi", "Searching \"{{searchQuery}}\"": "“{{searchQuery}}” q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Searching Knowledge for \"{{searchQuery}}\"": "“{{searchQuery}}” boʻyicha maʼ<PERSON>lar qidirilmoqda", "Searching the web...": "Internetda qidirilmoqda...", "Searxng Query URL": "Searchxng so'rovi URL", "See readme.md for instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uchun readme.md ga qarang", "See what's new": "<PERSON><PERSON> ya<PERSON> borligini ko'ring", "Seed": "Urug'", "Select a base model": "<PERSON><PERSON><PERSON><PERSON>", "Select a conversation to preview": "", "Select a engine": "Dvigatelni tanlang", "Select a function": "Funkts<PERSON>ni tan<PERSON>", "Select a group": "<PERSON><PERSON><PERSON>", "Select a model": "<PERSON><PERSON>", "Select a pipeline": "<PERSON><PERSON><PERSON><PERSON><PERSON> tanlang", "Select a pipeline url": "Qu<PERSON>r liniyasining URL manzilini tanlang", "Select a tool": "As<PERSON><PERSON><PERSON> tanlang", "Select an auth method": "Auth usulini tanlang", "Select an Ollama instance": "<PERSON><PERSON><PERSON> misolini tanlang", "Select Engine": "Dvigatelni tanlang", "Select Knowledge": "B<PERSON>m-ni tanlang", "Select only one model to call": "Q<PERSON>'<PERSON>'<PERSON><PERSON><PERSON> qilish uchun faqat bitta modelni tanlang", "Selected model(s) do not support image inputs": "Tanlangan model(lar) tasvir kiri<PERSON>i qo‘llab-quv<PERSON><PERSON><PERSON><PERSON>", "Semantic distance to query": "So'rov uchun semantik masofa", "Send": "Yuborish", "Send a Message": "<PERSON><PERSON> y<PERSON>", "Send message": "<PERSON><PERSON> y<PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "So‘rovda `stream_options: { include_usage: true }` yuboradi.\nQo'llab-quvvatlanadigan provayderlar o'rnatilganda javobda token foydalanish ma'lumot<PERSON><PERSON> qaytaradi.", "September": "sentyabr", "SerpApi API Key": "SerpApi API kaliti", "SerpApi Engine": "<PERSON>p<PERSON><PERSON> d<PERSON>", "Serper API Key": "Serper API kaliti", "Serply API Key": "Serply API kaliti", "Serpstack API Key": "Serpstack API kaliti", "Server connection verified": "Server ul<PERSON><PERSON> ta<PERSON>", "Set as default": "<PERSON>art sifatida o'r<PERSON>", "Set CFG Scale": "CFG shkalasini o'rnating", "Set Default Model": "<PERSON>art modelni o'r<PERSON>", "Set embedding model": "O'r<PERSON>ish modelini o'r<PERSON>", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON><PERSON> modelini oʻrnatish (ma<PERSON>an, {{model}})", "Set Image Size": "Rasm hajmini o'r<PERSON>", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON> tartiblash model<PERSON> o'r<PERSON>ish (ma<PERSON>an, {{model}})", "Set Sampler": "<PERSON>una oluvchini sozlash", "Set Scheduler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'<PERSON>", "Set Steps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "GPU-ga yuklanadigan qatlamlar sonini belgilang. <PERSON><PERSON><PERSON> qiymatni oshirish GPU tezlashtirish uchun optimallashtirilgan modellar uchun ish faoliyatini sezilarli darajada yax<PERSON><PERSON>i mumkin, lekin ko'proq quvvat va GPU resurslarini iste'mol qilishi mumkin.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "His<PERSON>lash uchun ishlatiladigan ishchi iplar sonini belgilang. Ushbu parametr kiruvchi so'rovlarni bir vaqtning o'zida qayta ishlash uchun qancha mavzu ishlatilishini nazorat qiladi. Ushbu qiymatni oshirish yuqori bir vaqtda ish yukida ishlashni yaxshilashi mumkin, lekin ko'proq CPU resurslarini iste'mol qilishi mumkin.", "Set Voice": "Ovozni sozlash", "Set whisper model": "Pichirlash modelini o'<PERSON>", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "<PERSON>ch bo'lmaganda bir marta paydo bo'lgan tokenlarga nisbatan tekis chiziq o'rnatadi. <PERSON><PERSON><PERSON> qiymat (ma<PERSON><PERSON>, 1,5) takrorlash uchun qattiqroq jazola<PERSON>, pastroq qiymat (ma<PERSON><PERSON>, 0,9) esa yumshoqroq bo'ladi. 0 bo'l<PERSON>, u o'chirilgan.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "<PERSON>ancha marta paydo bo'l<PERSON><PERSON> qarab, takrorlanishlarni jazolash uchun tokenlarga nisbatan masshtabni o'rnatadi. <PERSON><PERSON><PERSON> qiymat (masalan, 1,5) takrorlash uchun qattiq<PERSON>q jazola<PERSON>, pastroq qiymat (ma<PERSON><PERSON>, 0,9) esa yumshoqroq bo'ladi. 0 bo'l<PERSON>, u o'chirilgan.", "Sets how far back for the model to look back to prevent repetition.": "Takrorlanishning oldini olish uchun modelning orqaga qarashini belgi<PERSON>.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Nasl qilish uchun tasodifiy sonlar urug'ini o'r<PERSON>adi. Buni ma'lum bir raq<PERSON><PERSON> o'r<PERSON><PERSON>, modelni bir xil so'rov uchun bir xil matn yaratishga majbur qiladi.", "Sets the size of the context window used to generate the next token.": "<PERSON><PERSON><PERSON>ni yaratish uchun foydal<PERSON>ladigan kontekst oynasining hajmini bel<PERSON>.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "<PERSON><PERSON><PERSON><PERSON><PERSON> uchun to'xtash ketma-k<PERSON><PERSON><PERSON><PERSON> o'rnat<PERSON>. <PERSON><PERSON><PERSON> naq<PERSON>ga duch kelganda, LLM matn ya<PERSON> to'x<PERSON>di va qaytib keladi. Model fay<PERSON>a bir nechta alohida to'xtash paramet<PERSON><PERSON>ni belgilash orqali bir nechta to'xtash naqshlari o'r<PERSON><PERSON>hi mumkin.", "Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings saved successfully!": "<PERSON><PERSON><PERSON>alar muvaffaqiyatli saqlandi!", "Share": "<PERSON><PERSON><PERSON>", "Share Chat": "<PERSON><PERSON><PERSON>", "Share to Open WebUI Community": "Open WebUI hamjamiyatiga ulashing", "Sharing Permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON><PERSON><PERSON><PERSON>ish", "Show \"What's New\" modal on login": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON> narsalar\" modalini ko'rsating", "Show Admin Details in Account Pending Overlay": "<PERSON><PERSON> k<PERSON><PERSON>otgan qatlamda administrator ma<PERSON>l<PERSON><PERSON><PERSON><PERSON> ko‘rsatish", "Show All": "<PERSON><PERSON><PERSON> k<PERSON>'r<PERSON>", "Show image preview": "", "Show Less": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>'r<PERSON>ish", "Show Model": "<PERSON><PERSON> k<PERSON>'r<PERSON>ish", "Show shortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON> ko'rsatish", "Show your support!": "Qo'llab-quvvatlang!", "Showcased creativity": "Ko'rsatilgan ijodkorlik", "Sign in": "tiz<PERSON><PERSON> kirish", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} hisobiga kiring", "Sign in to {{WEBUI_NAME}} with LDAP": "LDAP yordamida {{WEBUI_NAME}} say<PERSON>ga kiring", "Sign Out": "<PERSON><PERSON><PERSON><PERSON> chiqish", "Sign up": "<PERSON>o'yxa<PERSON><PERSON> o'tish", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}} tarmog‘ida ro‘yxatdan o‘ting", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "<PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>, inline matematika va tartibni aniqlashni yaxshilash uchun LLM yordamida aniqlikni sezilarli darajada yaxshilaydi. Kechikish vaqtini oshiradi. Standartlar rost.", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} hisobiga kirish", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON><PERSON>", "Skip the cache and re-run the inference. Defaults to False.": "<PERSON><PERSON><PERSON> o't<PERSON><PERSON><PERSON> yuboring va xulosani qayta ishga tushiring. Birlamchi parametrlar False.", "Sougou Search API sID": "Sougou Search API sID", "Sougou Search API SK": "Sougou Search API SK", "Source": "Manba", "Speech Playback Speed": "Nutqni ijro etish tezligi", "Speech recognition error: {{error}}": "<PERSON><PERSON><PERSON><PERSON> an<PERSON> xato<PERSON>: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "<PERSON><PERSON><PERSON><PERSON> vosita", "Stop": "STOP", "Stop Generating": "", "Stop Sequence": "Ketma-ket<PERSON><PERSON> to'x<PERSON>ish", "Stream Chat Response": "Stream Chat javobi", "Strikethrough": "", "Strip Existing OCR": "<PERSON><PERSON><PERSON><PERSON>", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Mavjud OCR matnini PDF faylidan olib tashlang va OCRni qayta ishga tushiring. Majburiy OCR yoqilgan bo'lsa, e'tiborga olinmaydi. Birlamchi parametrlar False.", "STT Model": "STT modeli", "STT Settings": "STT sozlamalari", "Stylized PDF Export": "Stillashtirilgan PDF eksporti", "Subtitle (e.g. about the Roman Empire)": "Subtitr (ma<PERSON><PERSON>, <PERSON><PERSON> imperi<PERSON> ha<PERSON>)", "Success": "Mu<PERSON><PERSON><PERSON><PERSON><PERSON>", "Successfully updated.": "Muvaffaqiyatli ya<PERSON>.", "Suggested": "<PERSON>v<PERSON>ya <PERSON>ilgan", "Support": "<PERSON><PERSON><PERSON><PERSON><PERSON>-quv<PERSON><PERSON>", "Support this plugin:": "<PERSON><PERSON><PERSON> plaginni qo'llab-quvvatlang:", "Supported MIME Types": "", "Sync directory": "<PERSON><PERSON><PERSON><PERSON> katal<PERSON>i", "System": "<PERSON><PERSON><PERSON>", "System Instructions": "<PERSON><PERSON><PERSON>", "System Prompt": "<PERSON><PERSON><PERSON> so'rovi", "Tags": "Teglar", "Tags Generation": "<PERSON><PERSON><PERSON> ya<PERSON>ish", "Tags Generation Prompt": "<PERSON><PERSON><PERSON> ya<PERSON>ish ta<PERSON>", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "<PERSON>qi<PERSON><PERSON> kamroq ehtimoliy tokenlarning ta'sirini kamaytirish uchun quyruq<PERSON>z namuna olish qo'llaniladi. <PERSON><PERSON><PERSON> qiymat (<PERSON><PERSON><PERSON>, 2.0) ta<PERSON><PERSON><PERSON> koʻp<PERSON><PERSON> ka<PERSON>, 1.0 qiymati esa bu sozlamani oʻchirib qoʻyadi.", "Talk to model": "Model bilan gaplashing", "Tap to interrupt": "To<PERSON>x<PERSON>ish uchun bosing", "Task List": "", "Task Model": "Vazifa modeli", "Tasks": "Vazifalar", "Tavily API Key": "<PERSON>ly <PERSON> kaliti", "Tavily Extract Depth": "<PERSON><PERSON> ekstrakti chuqur<PERSON>gi", "Tell us more:": "Bizga ko'proq ma'lumot bering:", "Temperature": "<PERSON><PERSON><PERSON>", "Temporary Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text Splitter": "<PERSON><PERSON>", "Text-to-Speech": "", "Text-to-Speech Engine": "<PERSON><PERSON><PERSON> a<PERSON> mexaniz<PERSON>", "Thanks for your feedback!": "Fikr-mulohazangiz uchun tashakkur!", "The Application Account DN you bind with for search": "Qi<PERSON><PERSON> uchun siz bog'langan ilova hisobi DN", "The base to search for users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qidirish uchun asos", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "To'plam hajmi bir vaqtning o'zida nechta matn so'rovlari birgalikda qayta ishlanishini aniqlaydi. Kattaroq partiya hajmi modelning ishlashi va tezligini o<PERSON><PERSON><PERSON> mumkin, lekin u ham ko'proq xotira talab qiladi.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "<PERSON><PERSON><PERSON> plagin ortidagi ishlab chiquvchilar jamiyatning ehtirosli ko'ngillilaridir. Agar siz ushbu plaginni foydali deb bils<PERSON><PERSON>, uning rivojlanishiga hissa qo'shing.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Baholash peshqadami Elo reyting tizimiga asoslanadi va real vaqt rejimida yang<PERSON>nadi.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "Kirish audiosining tili. Kirish tilini ISO-639-1 (ma<PERSON><PERSON>, en) formatida taqdim etish aniqlik va kechikishni yaxshilaydi. Tilni avtomatik aniqlash uchun bo'sh qoldiring.", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP atributi foydalanuvchilarning tizimga kirishda foydalanadigan pochta manziliga mos keladi.", "The LDAP attribute that maps to the username that users use to sign in.": "Foydalanuvchilar tizimga kirish uchun foydalanadigan foydalanuvchi nomiga mos keladigan LDAP atributi.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Peshqadamlar jadvali hozirda beta-versiyada va biz algoritmni takomillashtirish jarayonida reyting hisob-kit<PERSON>larini sozlashim<PERSON> mumkin.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Faylning maksimal hajmi MB. <PERSON>gar fayl hajmi ushbu chegaradan o<PERSON>, fayl y<PERSON><PERSON>.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Chatda bir vaqtning o'zida ishlatilishi mumkin bo'lgan maksimal fayllar soni. Agar fayllar soni ushbu chegaradan o<PERSON>, fayllar yuk<PERSON>.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Matn uchun chiqish formati. \"json\", \"markdown\" yoki \"html\" bo'lishi mumkin. Birlamchi \"markdown\" uchun.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Bal 0,0 (0%) va 1,0 (100%) oralig'ida bo'lishi kerak.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "<PERSON><PERSON><PERSON> harorat. <PERSON><PERSON><PERSON><PERSON> o<PERSON>ni yanada ijodiy javob beris<PERSON> majbur qiladi.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON><PERSON>", "Thinking...": "O'ylash...", "This action cannot be undone. Do you wish to continue?": "Bu amalni ortga qaytarib bo<PERSON>l<PERSON><PERSON><PERSON>. Davom etishni x<PERSON>?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Bu kanal {{createdAt}} da yaratilgan. Bu {{channelName}} kanalining boshlanishi.", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Bu sizning qimmatli suhbatlaringiz ma'lumotlar bazasiga xavfsiz tarzda saqlanishini ta'minlaydi. Rahm<PERSON>!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Bu eksperimental xususiyat bo'lib, u kutilganidek ishlamasligi va istalgan vaqtda o'z<PERSON><PERSON> mumkin.", "This model is not publicly available. Please select another model.": "<PERSON><PERSON>bu model hamma uchun ochiq emas. Iltimos, boshqa model<PERSON> tanlang.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Us<PERSON>bu parametr kontekstni yangilashda qancha tokenlar saqlanishini nazorat qiladi. <PERSON><PERSON><PERSON>, agar 2 ga oʻ<PERSON><PERSON><PERSON> boʻ<PERSON><PERSON>, <PERSON><PERSON><PERSON> konte<PERSON><PERSON>ing oxirgi 2 ta belgisi saqlanib qoladi. Kontekstni saqlash suhbatning uzluksizligini saqlashga yordam beradi, lekin bu yangi mavzularga javob berish qobiliyatini kamaytirishi mumkin.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "<PERSON><PERSON>bu parametr model j<PERSON><PERSON><PERSON> yaratishi mumkin bo'lgan tokenlarning maksimal sonini belgilaydi. Ushbu chegarani oshirish modelga uzoqroq javoblarni taqdim etish imkonini beradi, biroq u foydasiz yoki ahamiyatsiz kontent yarat<PERSON>h ehtimolini ham oshirishi mumkin.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "<PERSON><PERSON><PERSON> parametr to'plamdagi barcha mavjud fayllarni o'chiradi va ularni yangi yuklangan fayllar bilan almashtiradi.", "This response was generated by \"{{model}}\"": "<PERSON>u javob \"{{model}}\" tomonidan yaratilgan", "This will delete": "<PERSON><PERSON> <PERSON>'chiri<PERSON>i", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Bu <strong>{{NAME}}</strong> va <strong>barcha ma<PERSON></strong> o<PERSON>chirib tash<PERSON>.", "This will delete all models including custom models": "<PERSON><PERSON> barcha <PERSON>, shu jumladan max<PERSON> model<PERSON><PERSON> o'chirib ta<PERSON>di", "This will delete all models including custom models and cannot be undone.": "<PERSON>u barcha model<PERSON>, j<PERSON><PERSON><PERSON>, max<PERSON> modellarni ham o‘chirib tashlaydi va ularni ortga qaytarib bo<PERSON>l<PERSON><PERSON>i.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Bu bilimlar bazasini qayta tiklaydi va barcha fayllarni sinxronlashtiradi. Davom etishni xoh<PERSON>zmi?", "Thorough explanation": "To'liq tush<PERSON><PERSON>sh", "Thought for {{DURATION}}": "{{DURATION}} uchun fikr", "Thought for {{DURATION}} seconds": "{{DURATION}} son<PERSON> o<PERSON>", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika Server URL manzili talab qilinadi.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Maslahat: <PERSON><PERSON> bir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> keyin chat kiritishidagi yorliq tug<PERSON> bosib ketma-ket bir nechta oʻzgaru<PERSON>chi slotlarni yangilang.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Sarlav<PERSON> (masalan, menga qiziqarli faktni ayting)", "Title Auto-Generation": "<PERSON><PERSON><PERSON><PERSON> av<PERSON><PERSON> ya<PERSON>", "Title cannot be an empty string.": "<PERSON><PERSON><PERSON><PERSON> bo'sh qator bo'lishi mumkin emas.", "Title Generation": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Title Generation Prompt": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON> ta<PERSON>", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON><PERSON><PERSON> o<PERSON> uchun mavjud model nomlariga kirish uchun,", "To access the GGUF models available for downloading,": "<PERSON><PERSON><PERSON> olish mumkin bo'lgan GGUF modellariga kirish uchun,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI-ga kirish uchun administrator bilan bog'laning. <PERSON><PERSON> foy<PERSON><PERSON><PERSON><PERSON> holatini Administrator panel<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> mumkin.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Bu yerda bilimlar bazasini biriktirish uchun avval ularni “Bilim” ish maydoniga qo‘shing.", "To learn more about available endpoints, visit our documentation.": "<PERSON><PERSON><PERSON><PERSON> so'nggi nuqtalar haqida ko'proq bilish uchun hujjatlarimizga tashrif buyuring.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Maxfiyligingizni himoya qilish uchun fikr-mulohazalaringizdan faqat reytinglar, model identifikatorlari, teglar va meta-maʼlumotlar baham koʻriladi — chat jurnallaringiz shaxsiy boʻ<PERSON><PERSON> qoladi va ularga kiritil<PERSON>i.", "To select actions here, add them to the \"Functions\" workspace first.": "Bu yerda amallarni tanlash uchun avval ularni “Funksiyalar” ish maydoniga qo‘shing.", "To select filters here, add them to the \"Functions\" workspace first.": "Bu yerda filtrlarni tanlash uchun avval ularni “Funksiyalar” ish maydoniga qo‘shing.", "To select toolkits here, add them to the \"Tools\" workspace first.": "<PERSON>u yerda asboblar to‘plamini tanlash uchun avval ularni “Asboblar” ish maydoniga qo‘shing.", "Toast notifications for new updates": "<PERSON><PERSON> ya<PERSON><PERSON> haqida bi<PERSON>", "Today": "<PERSON><PERSON><PERSON>", "Toggle search": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Toggle settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Toggle sidebar": "<PERSON>n <PERSON><PERSON>", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "<PERSON><PERSON>", "Tool created successfully": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Tool deleted successfully": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'<PERSON>iri<PERSON>i", "Tool Description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Tool ID": "Asbob identifikatori", "Tool imported successfully": "Asbob muvaffaqiyatli import qilindi", "Tool Name": "<PERSON><PERSON><PERSON> nomi", "Tool Servers": "Asbob serverlari", "Tool updated successfully": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "Tools": "Asboblar", "Tools Access": "Asbobla<PERSON> kirish", "Tools are a function calling system with arbitrary code execution": "Asboblar - bu <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bilan kod bajarilishi bilan funksiyalarni chaqirish tizimi", "Tools Function Calling Prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tools have a function calling system that allows arbitrary code execution.": "Asboblar o'zboshimchalik bilan kodni bajarishga imkon beruvchi funksiyalarni chaqirish tizimiga ega.", "Tools Public Sharing": "<PERSON><PERSON><PERSON> v<PERSON>", "Top K": "<PERSON><PERSON><PERSON>", "Top K Reranker": "Top K Reranker", "Transformers": "Transformatorlar", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON><PERSON> kirishda muammo bormi?", "Trust Proxy Environment": "<PERSON><PERSON><PERSON><PERSON> proksi muhiti", "TTS Model": "TTS modeli", "TTS Settings": "TTS sozlamalari", "TTS Voice": "TTS ovozi", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (<PERSON><PERSON><PERSON>) URL manzilini kiriting", "Uh-oh! There was an issue with the response.": "Uh-oh! <PERSON><PERSON><PERSON><PERSON> muammo yuzaga keldi.", "UI": "UI", "Unarchive All": "<PERSON><PERSON><PERSON> ar<PERSON><PERSON><PERSON> chiq<PERSON>h", "Unarchive All Archived Chats": "<PERSON><PERSON> arxiv<PERSON><PERSON> suh<PERSON>ni arxivdan chiq<PERSON>h", "Unarchive Chat": "<PERSON><PERSON><PERSON> a<PERSON><PERSON> chiq<PERSON>h", "Underline": "", "Unloads {{FROM_NOW}}": "{{FROM_NOW}} yuk<PERSON><PERSON>", "Unlock mysteries": "<PERSON><PERSON><PERSON>", "Unpin": "<PERSON><PERSON><PERSON>", "Unravel secrets": "<PERSON><PERSON><PERSON>", "Unsupported file type.": "", "Untagged": "Belgilanmagan", "Untitled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON>", "Update and Copy Link": "<PERSON><PERSON><PERSON> ya<PERSON> va nusxalash", "Update for the latest features and improvements.": "Eng yangi xususiyatlar va yaxshilanishlar uchun yangilang.", "Update password": "<PERSON><PERSON><PERSON>", "Updated": "Yangilangan", "Updated at": "Yangilangan", "Updated At": "Yangilangan", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>, max<PERSON> mav<PERSON><PERSON><PERSON><PERSON> va brendlash hamda maxsus yordam uchun litsenziyalangan rejaga yang<PERSON>ng.", "Upload": "Yuklash", "Upload a GGUF model": "GGUF modelini yuklang", "Upload Audio": "Audio yuklash", "Upload directory": "<PERSON><PERSON><PERSON> o<PERSON> ka<PERSON>i", "Upload files": "<PERSON><PERSON><PERSON> yuk<PERSON>", "Upload Files": "<PERSON><PERSON><PERSON> yuk<PERSON>", "Upload Pipeline": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> yuk<PERSON>", "Upload Progress": "<PERSON><PERSON><PERSON>", "URL": "URL", "URL Mode": "URL rejimi", "Usage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Use '#' in the prompt input to load and include your knowledge.": "O'z bilimlaringizni yuklash va kiritish uchun so'rovnomada \"#\" dan foydalaning.", "Use Gravatar": "Gravatar-dan foy<PERSON><PERSON>ng", "Use groups to group your users and assign permissions.": "Foydalanuvchilaringizni guruhlash va ruxsatlarni belgilash uchun guruhl<PERSON>an foydalaning.", "Use Initials": "<PERSON><PERSON> ha<PERSON> foydalaning", "Use LLM": "LLM dan foydalaning", "Use no proxy to fetch page contents.": "<PERSON><PERSON><PERSON> ma<PERSON><PERSON> olish uchun proksi-<PERSON><PERSON> foydal<PERSON>g.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "<PERSON><PERSON><PERSON> olish uchun http_proxy va https_proxy muhit oʻzgaruvchilari tomonidan belgilangan proksi-serverdan foydalaning.", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User location successfully retrieved.": "Foydalanu<PERSON><PERSON> joy<PERSON>uvi muvaffaq<PERSON><PERSON>i olindi.", "User menu": "", "User Webhooks": "Foydalanuvchi veb-huklari", "Username": "Foydalanuvchi nomi", "Users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Barcha modellar bilan standart arena modelidan foydalanish. Maxsus modellarni qo'shish uchun orti<PERSON><PERSON> tug<PERSON> bosing.", "Valid time units:": "<PERSON><PERSON><PERSON><PERSON> vaqt birl<PERSON>i:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "<PERSON><PERSON><PERSON> ya<PERSON>", "Valves updated successfully": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "variable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Verify Connection": "<PERSON><PERSON><PERSON><PERSON> tekshir<PERSON>", "Verify SSL Certificate": "SSL sertifikatini tekshiring", "Version": "Vers<PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "{{totalVersions}} versiyasining {{selectedVersion}} versiyasi", "View Replies": "Jav<PERSON>lar<PERSON> ko'rish", "View Result from **{{NAME}}**": "**{{NAME}}** nati<PERSON><PERSON> ko‘rish", "Visibility": "<PERSON><PERSON><PERSON><PERSON>sh", "Vision": "Vizyon", "Voice": "Ovoz", "Voice Input": "<PERSON><PERSON><PERSON><PERSON> kiritish", "Voice mode": "<PERSON><PERSON><PERSON>mi", "Warning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Warning:": "Ogohlantirish:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Ogohlantirish: <PERSON><PERSON> yoqish foydalanuvchilarga serverga ixtiyoriy kodni yuklash imkonini beradi.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Ogohlantirish: <PERSON><PERSON> siz o'r<PERSON>ish modelingizni yangilasangiz yoki o'zgartirsangiz, barcha hujjatlarni qayta import qilishingiz kerak bo'ladi.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Ogohlantirish: <PERSON><PERSON><PERSON><PERSON>'zboshimchalik bilan kod bajaril<PERSON>ni ta'min<PERSON>di, bu xavfsizlikka jiddiy xavf tug'diradi - juda ehtiyotkorlik bilan davom eting.", "Web": "V<PERSON>", "Web API": "Web API", "Web Loader Engine": "Web Loader Engine", "Web Search": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>v", "Web Search Engine": "<PERSON><PERSON> qidiruv tizimi", "Web Search in Chat": "<PERSON><PERSON><PERSON> veb-qidiruv", "Web Search Query Generation": "<PERSON><PERSON><PERSON>q<PERSON><PERSON>uv so'rov<PERSON><PERSON> yaratish", "Webhook URL": "Webhook URL manzili", "WebUI Settings": "WebUI sozlamalari", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "WebUI “{{url}}” manziliga so‘rov yuboradi", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI “{{url}}/api/chat” manziliga so‘rov yuboradi", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI \"{{url}}/chat/completions\" manziliga so'rov yuboradi", "Weight of BM25 Retrieval": "BM25 olishning og'irligi", "What are you trying to achieve?": "<PERSON><PERSON><PERSON>?", "What are you working on?": "Nima ustida ishl<PERSON>?", "What's New in": "", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, model real vaqt rejimida har bir chat xabariga javob beradi va foydalanuvchi xabar yuborishi bilanoq javob hosil qiladi. <PERSON><PERSON><PERSON> rejim jonli chat ilovalari uchun foydal<PERSON>, lekin sekinroq uskunaning ishlashiga ta'sir qilishi mumkin.", "wherever you are": "qay<PERSON>a bo'l<PERSON><PERSON> ham", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Chiqishni sahifalash kerakmi. Har bir sahifa gorizontal qoida va sahifa raqami bilan ajratiladi. Birlamchi parametrlar False.", "Whisper (Local)": "<PERSON><PERSON><PERSON> (mahalliy)", "Why?": "Nega?", "Widescreen Mode": "<PERSON>g ekran rejimi", "Won": "<PERSON><PERSON><PERSON><PERSON>i", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Top-k bilan birga ishlaydi. <PERSON><PERSON><PERSON> qiymat (masalan, 0,95) matnning xilma-x<PERSON><PERSON><PERSON> olib kela<PERSON>, pastroq qiymat esa (masalan, 0,5) ko'proq diqqatli va konservativ matnni yaratadi.", "Workspace": "<PERSON><PERSON>", "Workspace Permissions": "<PERSON><PERSON> rux<PERSON>", "Write": "Yozing", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON> taklif yozing (masalan, siz kimsiz?)", "Write a summary in 50 words that summarizes [topic or keyword].": "[mavzu yoki kalit so'zni] umumlashtiruvchi 50 ta so'zdan iborat xulosa yozing.", "Write something...": "<PERSON>iror narsa yozing ...", "Yacy Instance URL": "<PERSON><PERSON> Instance URL", "Yacy Password": "<PERSON><PERSON> parol", "Yacy Username": "<PERSON><PERSON>oy<PERSON><PERSON> nomi", "Yesterday": "<PERSON><PERSON>", "You": "Siz", "You are currently using a trial license. Please contact support to upgrade your license.": "Siz hozirda sinov litsenziyasidan foydalanmoqdasiz. Litsenziyangizni yangilash uchun qoʻllab-quvvatlash xizmatiga murojaat qiling.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Siz bir vaqtning o'zida ko'pi bilan {{maxCount}} ta fayl(lar) bilan suhbatlash<PERSON>iz mumkin.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "<PERSON><PERSON><PERSON><PERSON> “<PERSON>sh<PERSON>ris<PERSON>” tugmasi orqali xotiralar qo‘shish orqali LLMlar bilan o‘zaro aloqalaringizni shaxsiylashtiris<PERSON><PERSON> mum<PERSON>, bu ularni yanada foydal<PERSON>q va sizga moslashtiradi.", "You cannot upload an empty file.": "<PERSON>z bo'sh faylni yuklay olmaysiz.", "You do not have permission to upload files.": "Sizda fayllarni yuklash uchun ruxsat yo'q.", "You have no archived conversations.": "Sizda arxivlangan suhbatlar yoʻq.", "You have shared this chat": "Siz bu chatni baham ko'r<PERSON><PERSON>", "You're a helpful assistant.": "<PERSON><PERSON> foydali yo<PERSON>.", "You're now logged in.": "Siz endi tizimga kirdingiz.", "Your account status is currently pending activation.": "Hisobingiz holati hozirda faollas<PERSON>irishni kutmoqda.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Sizning barcha hiss<PERSON><PERSON> to'g'ridan-to'g'ri plagin ishlab chiqaru<PERSON> o'tadi; Open WebUI hech qanday foizni olmaydi. Biroq, tanlangan moliyalash<PERSON>rish platformasi o'z to'lovlariga ega bo'lishi mumkin.", "Youtube": "Youtube", "Youtube Language": "Youtube tili", "Youtube Proxy URL": "Youtube proksi URL"}