{"-1 for no limit, or a positive integer for a specific limit": "Sınır yoksa -1, be<PERSON><PERSON><PERSON> bir sınır i<PERSON>in pozitif bir tamsayı.", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' veya sü<PERSON>iz i<PERSON> '-1'.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(örn. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(örn. `sh webui.sh --api`)", "(latest)": "(en son)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} Yanıt", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}}'ın <PERSON>i", "{{webUIName}} Backend Required": "{{webUIName}} Arka<PERSON><PERSON><PERSON>", "*Prompt node ID(s) are required for image generation": "*Görüntü oluşturma için düğüm kimlikleri gereklidir", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON>ni bir sürüm (v{{LATEST_VERSION}}) artık mevcut.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Bir görev modeli, sohbetler ve web arama sorguları için başlık oluşturma gibi görevleri yerine getirirken kullanılır", "a user": "bir kullan<PERSON>ı", "About": "Hakkında", "Accept autocomplete generation / Jump to prompt variable": "Otomatik tamamlama üretimini kabul et / İstem değişkenine atla", "Access": "<PERSON><PERSON><PERSON><PERSON>", "Access Control": "<PERSON><PERSON><PERSON><PERSON>", "Accessible to all users": "<PERSON><PERSON><PERSON> k<PERSON>anıcılara erişilebilir", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Hesap Aktiva<PERSON>u Bekleniyor", "Accurate information": "Doğru bilgi", "Action": "", "Actions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Activate": "Aktif <PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "<PERSON><PERSON><PERSON> girişine \"/{{COMMAND}}\" yazarak bu komutu etkinleştirin.", "Active Users": "<PERSON><PERSON><PERSON>", "Add": "<PERSON><PERSON>", "Add a model ID": "Bir model <PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a short description about what this model does": "Bu modelin ne yaptığı hakkında kısa bir açıklama ekleyin", "Add a tag": "<PERSON>ir etiket e<PERSON>", "Add Arena Model": "Arena Modeli Ekle", "Add Connection": "Bağlantı Ekle", "Add Content": "İçerik Ekle", "Add content here": "Buraya içerik ekleyin", "Add Custom Parameter": "", "Add custom prompt": "<PERSON><PERSON> prompt e<PERSON><PERSON>", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "Grup <PERSON>", "Add Memory": "<PERSON><PERSON>", "Add Model": "<PERSON>", "Add Reaction": "<PERSON><PERSON><PERSON>", "Add Tag": "Etiket Ekle", "Add Tags": "<PERSON><PERSON><PERSON><PERSON>", "Add text content": "<PERSON><PERSON>", "Add User": "Kullanıcı Ekle", "Add User Group": "Kullanıcı Grubu Ekle", "Adjusting these settings will apply changes universally to all users.": "<PERSON>u a<PERSON><PERSON><PERSON>, değişiklikleri tüm kullanıcılara evrensel olarak uygulayacaktır.", "admin": "yönetici", "Admin": "Yönetici", "Admin Panel": "Yönetici Paneli", "Admin Settings": "Yönetici Ayarları", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Yönetici<PERSON> her zaman tüm araçlara <PERSON>; kullanıcıların çalışma alanında<PERSON> model ba<PERSON><PERSON><PERSON> atanmış araçlara ihtiyacı vardır.", "Advanced Parameters": "Gelişmiş Parametreler", "Advanced Params": "Gelişmiş Parametreler", "AI": "", "All": "<PERSON><PERSON><PERSON>", "All Documents": "<PERSON><PERSON><PERSON>", "All models deleted successfully": "<PERSON>üm modeller b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "<PERSON><PERSON><PERSON> Silmeye İzin Ver", "Allow Chat Deletion": "<PERSON><PERSON><PERSON> Silmeye İzin Ver", "Allow Chat Edit": "<PERSON><PERSON><PERSON> Silmeye İzin Ver", "Allow Chat Export": "Sohbetin Dışa Aktarımına İzin Ver", "Allow Chat Share": "<PERSON><PERSON><PERSON><PERSON>ılmasına İzin Ver", "Allow Chat System Prompt": "", "Allow File Upload": "Dosya Yüklemeye İzin Ver", "Allow Multiple Models in Chat": "So<PERSON>bette Birden Fazla Modele İzin Ver", "Allow non-local voices": "<PERSON><PERSON> seslere izin verin", "Allow Speech to Text": "", "Allow Temporary Chat": "Geçici Sohbetlere İzin Ver", "Allow Text to Speech": "", "Allow User Location": "Kullanıcı Konumuna İzin Ver", "Allow Voice Interruption in Call": "Aramada Ses Kesintisine İzin Ver", "Allowed Endpoints": "İzin Verilen Uç Noktalar", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Zaten bir hesabın<PERSON>z mı var?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "<PERSON><PERSON>", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "<PERSON><PERSON>", "an assistant": "bir asistan", "Analyzed": "<PERSON><PERSON><PERSON>", "Analyzing...": "<PERSON><PERSON>z ediliyor...", "and": "ve", "and {{COUNT}} more": "ve {{COUNT}} daha", "and create a new shared link.": "ve yeni bir paylaşılan bağlantı oluşturun.", "Android": "", "API": "", "API Base URL": "API Temel URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API Anahtarı", "API Key created.": "API Anahtarı oluşturuldu.", "API Key Endpoint Restrictions": "API Anahtarı Uç Nokta Kısıtlamaları", "API keys": "API anahtarları", "API Version": "", "Application DN": "Uygulama DN", "Application DN Password": "Uygulama DN Parola", "applies to all users with the \"user\" role": "\"kullanıcı\" rolüne sahip tüm kullanıcılar için geçerlidir", "April": "<PERSON><PERSON>", "Archive": "Arşiv", "Archive All Chats": "Tüm Sohbetleri Arşivle", "Archived Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archived-chat-export": "arşivlenmiş-sohbet-aktarımı", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "Bu kanalı silmek istediğinizden emin misiniz?", "Are you sure you want to delete this message?": "Bu mesajı silmek istediğinizden emin misiniz?", "Are you sure you want to unarchive all archived chats?": "Arşivlenmiş tüm sohbetlerin arşivini kaldırmak istediğinizden emin misiniz?", "Are you sure?": "Emin misiniz?", "Arena Models": "Arena Modelleri", "Artifacts": "<PERSON><PERSON><PERSON>", "Ask": "Sor", "Ask a question": "Bir soru sorun", "Assistant": "Asistan", "Attach file from knowledge": "", "Attention to detail": "Ayrıntılara dikkat", "Attribute for Mail": "", "Attribute for Username": "Kullanıcı Adı için <PERSON>", "Audio": "Ses", "August": "<PERSON><PERSON><PERSON><PERSON>", "Auth": "<PERSON><PERSON>", "Authenticate": "Kimlik Doğrulama", "Authentication": "Kimlik Doğrulama", "Auto": "", "Auto-Copy Response to Clipboard": "Yanıtı Panoya Otomatik Kopyala", "Auto-playback response": "Yanıtı otomatik oynatma", "Autocomplete Generation": "Otomatik Tamamlama <PERSON>", "Autocomplete Generation Input Max Length": "Otomatik Tamamlama Üretimi Maksimum Uzunlukta Giriş", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API Kimlik Doğrulama Dizesi", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Temel URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Temel URL gereklidir.", "Available list": "Mevcut liste", "Available Tools": "<PERSON><PERSON><PERSON>", "available!": "mevcut!", "Awful": "Berbat", "Azure AI Speech": "Azure AI Konuşma", "Azure Region": "Azure B<PERSON><PERSON>si", "Back": "<PERSON><PERSON>", "Bad Response": "Kötü Yanıt", "Banners": "<PERSON><PERSON><PERSON><PERSON>", "Base Model (From)": "Temel Model ('den)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "önce", "Being lazy": "Tembelleşi<PERSON><PERSON>", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing Arama V7 Uç Noktası", "Bing Search V7 Subscription Key": "Bing Arama V7 Abonelik Anahtarı", "Bocha Search API Key": "Bocha Arama API Anahtarı", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Search API Anahtarı", "Bullet List": "", "By {{name}}": "{{name}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "Takvim", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Web STT motoru kullanılırken arama özelliği desteklenmiyor", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "İptal", "Capabilities": "Yetenekler", "Capture": "<PERSON><PERSON><PERSON>", "Capture Audio": "<PERSON><PERSON>", "Certificate Path": "<PERSON><PERSON><PERSON><PERSON>", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "Kanal Adı", "Channels": "<PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Otomatik tamamlama üretimi girişi için karakter sınırı", "Chart new frontiers": "", "Chat": "<PERSON><PERSON><PERSON>", "Chat Background Image": "Sohbet Arka Plan Resmi", "Chat Bubble UI": "<PERSON><PERSON><PERSON>lonu <PERSON>ü<PERSON>", "Chat Controls": "<PERSON><PERSON><PERSON>", "Chat direction": "<PERSON><PERSON><PERSON>", "Chat Overview": "Sohbet Genel Bakış", "Chat Permissions": "<PERSON><PERSON><PERSON>", "Chat Tags Auto-Generation": "Sohbet Etiketleri Otomatik Oluşturma", "Chats": "<PERSON><PERSON><PERSON><PERSON>", "Check Again": "Tekrar Kontrol <PERSON>", "Check for updates": "Güncellemeleri kontrol et", "Checking for updates...": "Güncellemeler kontrol ediliyor...", "Choose a model before saving...": "Kaydetmeden önce bir model seçin...", "Chunk Overlap": "Chunk Çakışması", "Chunk Size": "Chunk Boyutu", "Ciphers": "<PERSON><PERSON><PERSON><PERSON>", "Citation": "Alıntı", "Citations": "", "Clear memory": "<PERSON><PERSON><PERSON> te<PERSON>", "Clear Memory": "<PERSON><PERSON><PERSON>", "click here": "buraya tıklayın", "Click here for filter guides.": "Filtre kılavuzları için buraya tıklayın.", "Click here for help.": "<PERSON><PERSON>m için buraya tıklayın.", "Click here to": "<PERSON><PERSON><PERSON> yapmak için buraya tıklayın:", "Click here to download user import template file.": "Kullanıcı içe aktarma şablon dosyasını indirmek için buraya tıklayın.", "Click here to learn more about faster-whisper and see the available models.": "faster-whisper hakkında daha fazla bilgi edinmek ve mevcut modelleri görmek için buraya tıklayın.", "Click here to see available models.": "Erişilebilir Modelleri Görmek İçin Buraya Tıklayın", "Click here to select": "Seçmek için buraya tıklayın", "Click here to select a csv file.": "Bir CSV dosyası seçmek için buraya tıklayın.", "Click here to select a py file.": "Bir py dosyası seçmek için buraya tıklayın.", "Click here to upload a workflow.json file.": "Bir workflow.json dosyası yüklemek için buraya tıklayın.", "click here.": "buraya tıklayın.", "Click on the user role button to change a user's role.": "Bir kullanıcının rolünü değiştirmek için kullanıcı rolü düğmesine tıklayın.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Panoya yazma izni reddedildi. Tarayıcı ayarlarını kontrol ederek gerekli izinleri sağlayabilirsiniz.", "Clone": "Klon", "Clone Chat": "<PERSON><PERSON><PERSON><PERSON>", "Clone of {{TITLE}}": "{{TITLE}}'ın kopyası", "Close": "Ka<PERSON><PERSON>", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "<PERSON><PERSON>", "Code Execution": "Kod Çalıştırma", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "Kod başarıyla biçimlendirildi", "Code Interpreter": "Kod Yorumlayıcısı", "Code Interpreter Engine": "<PERSON><PERSON>", "Code Interpreter Prompt Template": "Kod Yorumlama İstem Şablonu", "Collapse": "", "Collection": "Koleksiyon", "Color": "Renk", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API Anahtarı", "ComfyUI Base URL": "ComfyUI Temel URL", "ComfyUI Base URL is required.": "ComfyUI Temel URL gerekli.", "ComfyUI Workflow": "ComfyUI İş Akışı", "ComfyUI Workflow Nodes": "ComfyUI İş Akışı Düğümleri", "Command": "<PERSON><PERSON><PERSON>", "Comment": "", "Completions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Concurrent Requests": "Eşzamanlı İstekler", "Configure": "Yapılandırma", "Confirm": "<PERSON><PERSON><PERSON>", "Confirm Password": "Parolayı Onayla", "Confirm your action": "İşleminizi onaylayın", "Confirm your new password": "Yeni parolanızı onaylayın", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "Kendi OpenAPI uyumlu harici araç sunucularınıza bağlanın.", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Bağlantılar", "Connections saved successfully": "Bağlantılar başarıyla kaydedildi", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "WebUI Erişimi için Yöneticiyle İletişime Geçin", "Content": "İçerik", "Content Extraction Engine": "İçerik Çıkarma Motoru", "Continue Response": "<PERSON><PERSON><PERSON>", "Continue with {{provider}}": "{{provider}} ile devam et", "Continue with Email": "E-posta ile devam edin", "Continue with LDAP": "LDAP ile devam edin", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON> met<PERSON>in TTS istekleri için nasıl bölüneceğini kontrol edin. '<PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 'paragraflar' paragraflara böler ve 'hiçbiri' mesajı tek bir dize olarak tutar.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Kopyalandı", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Paylaşılan sohbet URL'si panoya kopyalandı!", "Copied to clipboard": "Panoya kopyalandı", "Copy": "Kopyala", "Copy Formatted Text": "Biçimlenirilmiş Metni Kopyala", "Copy last code block": "<PERSON> kod blo<PERSON><PERSON><PERSON> k<PERSON>ala", "Copy last response": "Son yanıtı kopyala", "Copy link": "", "Copy Link": "Bağlantıyı Kopyala", "Copy to clipboard": "<PERSON><PERSON> k<PERSON>", "Copying to clipboard was successful!": "Panoya kopyalama başarılı!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "Open WebUI’dan gelen isteklerin kabul edilebilmesi için <PERSON>ının CORS yapılandırmasının doğru şekilde yapılmış olması gerekir.", "Create": "Oluştur", "Create a knowledge base": "Bir bilgi tabanı oluştur", "Create a model": "Bir model oluştur", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "Yönetici Hesabı Oluştur", "Create Channel": "Kanal Oluştur", "Create Folder": "", "Create Group": "Grup Oluştur", "Create Knowledge": "<PERSON><PERSON>gi <PERSON>", "Create new key": "<PERSON><PERSON>", "Create new secret key": "<PERSON>ni gizli anahtar o<PERSON>", "Create Note": "Not Oluştur", "Create your first note by clicking on the plus button below.": "İlk notunuzu aşağıdaki artı düğmesine basarak oluşturun.", "Created at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "Created At": "Şu Tarihte Oluşturuldu:", "Created by": "<PERSON><PERSON>un tarafından oluşturuldu:", "CSV Import": "CSV İçe Aktarma", "Ctrl+Enter to Send": "", "Current Model": "Mevcut Model", "Current Password": "<PERSON><PERSON><PERSON>", "Custom": "<PERSON><PERSON>", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "Tehlikeli Bölge", "Dark": "<PERSON><PERSON>", "Database": "Veritabanı", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Aralık", "Default": "Varsayılan", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Varsayılan (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Varsay<PERSON>lan mod, araçları yürütmeden önce bir kez çağırarak daha geniş bir model ye<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>. <PERSON><PERSON> mod, modelin yerleşik araç ça<PERSON><PERSON><PERSON> yet<PERSON> yararlanır, ancak modelin bu özelliği doğal olarak desteklemesini gerektirir.", "Default Model": "Varsayılan Model", "Default model updated": "Varsayılan model g<PERSON><PERSON><PERSON><PERSON>", "Default Models": "Varsayılan Modeller", "Default permissions": "Varsayılan i<PERSON>ler", "Default permissions updated successfully": "Varsayılan izinler başarıyla güncellendi", "Default Prompt Suggestions": "Varsayılan Prompt Önerileri", "Default to 389 or 636 if TLS is enabled": "TLS etkinse 389 veya 636'ya var<PERSON>ılan olarak", "Default to ALL": "TÜMÜ'nü <PERSON> o<PERSON>ak", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Varsayılan Kullanıcı Rolü", "Delete": "Sil", "Delete a model": "Bir modeli sil", "Delete All Chats": "<PERSON><PERSON><PERSON> Sohbetleri Sil", "Delete All Models": "Tüm Modelleri Sil", "Delete chat": "<PERSON><PERSON><PERSON><PERSON> sil", "Delete Chat": "So<PERSON>bet<PERSON> Sil", "Delete chat?": "<PERSON><PERSON><PERSON>i sil?", "Delete folder?": "Klasörü sil?", "Delete function?": "Fonksiyonu sil?", "Delete Message": "Mesajı Sil", "Delete message?": "<PERSON><PERSON>?", "Delete note?": "Not Silinsin mi?", "Delete prompt?": "Promptu sil?", "delete this link": "bu bağlantıyı sil", "Delete tool?": "Aracı sil?", "Delete User": "Kullanıcıyı Sil", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} silindi", "Deleted {{name}}": "{{name}} silindi", "Deleted User": "Kullanıcı Silindi", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Bilgi tabanınızı ve hedeflerinizi açıklayın", "Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Talimatları tam olarak takip etmedi", "Direct": "<PERSON><PERSON><PERSON><PERSON>", "Direct Connections": "<PERSON><PERSON><PERSON><PERSON>", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Devre Dışı", "Discover a function": "Bir fonksiyon keşfedin", "Discover a model": "Bir model ke<PERSON><PERSON><PERSON>", "Discover a prompt": "Bir prompt ke<PERSON><PERSON><PERSON>", "Discover a tool": "Bir araç k<PERSON><PERSON>din", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "Harikaları keşfedin", "Discover, download, and explore custom functions": "Özel fonksiyonları keşfedin, indirin ve inceleyin", "Discover, download, and explore custom prompts": "<PERSON><PERSON> promptları keş<PERSON>din, indirin ve inceleyin", "Discover, download, and explore custom tools": "<PERSON><PERSON> a<PERSON>ç<PERSON>ı keşfedin, indirin ve inceleyin", "Discover, download, and explore model presets": "Model <PERSON>n ayarların<PERSON> keş<PERSON>din, indirin ve inceleyin", "Display": "", "Display Emoji in Call": "<PERSON><PERSON>", "Display the username instead of You in the Chat": "Sohbet'te Siz yerine kullanıcı adını göster", "Displays citations in the response": "Yanıtta alıntıları gösterir", "Dive into knowledge": "Bilgiye dalmak", "Do not install functions from sources you do not fully trust.": "Tamamen güvenmediğiniz kaynaklardan fonksiyonlar yüklemeyin.", "Do not install tools from sources you do not fully trust.": "Tamamen güvenmediğiniz kaynaklardan araçlar yüklemeyin.", "Docling": "", "Docling Server URL required.": "", "Document": "Belge", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Dökümantasyon", "Documents": "<PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "herhangi bir harici bağlantı yapmaz ve verileriniz güvenli bir şekilde yerel olarak barındırılan sunucunuzda kalır.", "Domain Filter List": "", "Don't have an account?": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "don't install random functions from sources you don't trust.": "Tanımadığınız kaynaklardan rastgele fonksiyonlar yüklemeyin.", "don't install random tools from sources you don't trust.": "Tanımadığınız kaynaklardan rastgele araçlar yüklemeyin.", "Don't like the style": "Tarzını beğenmedim", "Done": "Tamamlandı", "Download": "<PERSON><PERSON><PERSON>", "Download as SVG": "", "Download canceled": "İndirme iptal edildi", "Download Database": "Veritabanını İndir", "Drag and drop a file to upload or select a file to view": "Yüklemek için bir dosyayı sürükleyip bırakın veya görüntülemek için bir dosya seçin", "Draw": "Çiz", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "örn. '30s', '10m'. Geçerli zaman birimleri 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "örn. \"json\" veya JSON şablonu", "e.g. 60": "örn. 60", "e.g. A filter to remove profanity from text": "örn. Metinden küfürleri kaldırmak için bir filtre", "e.g. en": "", "e.g. My Filter": "örn<PERSON> <PERSON><PERSON>", "e.g. My Tools": "örn. <PERSON><PERSON>", "e.g. my_filter": "örn. benim_filtrem", "e.g. my_tools": "örn. benim_araçlarım", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": " örn.Çeşitli işlemleri gerçekleştirmek için a<PERSON>", "e.g., 3, 4, 5 (leave blank for default)": "örn. 3, 4, 5 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> i<PERSON><PERSON> bo<PERSON> bı<PERSON>)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "örn. en-US, ja-<PERSON> (otomatik tanıma için bo<PERSON> bırakın)", "e.g., westus (leave blank for eastus)": "ö", "e.g.) en,fr,de": "", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Arena Modelini Düzenle", "Edit Channel": "Kanalı Düzenle", "Edit Connection": "Bağlantıyı Düzenle", "Edit Default Permissions": "Varsayılan İzinleri Düzenle", "Edit Folder": "", "Edit Memory": "<PERSON><PERSON><PERSON>", "Edit User": "Kullanıcıyı Düzenle", "Edit User Group": "Kullanıcı Grubunu <PERSON>le", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "E-posta", "Embark on adventures": "Maceralara atıl", "Embedding": "G<PERSON><PERSON>", "Embedding Batch Size": "<PERSON><PERSON><PERSON>ığı<PERSON>", "Embedding Model": "<PERSON><PERSON><PERSON>i", "Embedding Model Engine": "Gömme Modeli Motoru", "Embedding model set to \"{{embedding_model}}\"": "Gömme modeli \"{{embedding_model}}\" olarak <PERSON>landı", "Enable API Key": "API Anahtarını Etkinleştir", "Enable autocomplete generation for chat messages": "So<PERSON>bet mesajları için otomatik tamamlama üretimini etkinleştir", "Enable Code Execution": "Kod Çalıştırmayı Etkinleştir", "Enable Code Interpreter": "Kod Yorumlayıcıyı Etkinleştir", "Enable Community Sharing": "Topluluk Paylaşımını Etkinleştir", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "<PERSON><PERSON>meyi Etkinleştir", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Yeni Kayıtları Etkinleştir", "Enabled": "<PERSON><PERSON><PERSON>", "Endpoint URL": "Uçnokta URL", "Enforce Temporary Chat": "Geçici Sohbete Zorla", "Enhance": "İyileştir", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSV dosyanızın şu sırayla 4 sütun içerdiğinden emin olun: İsim, E-posta, Şifre, Rol.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON> {{role}} me<PERSON><PERSON><PERSON>n<PERSON> girin", "Enter a detail about yourself for your LLMs to recall": "LLM'lerinizin hatırlaması için kendiniz hakkında bir bilgi girin", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "<PERSON>pi auth dizesini girin (örn. kullanıcı adı:parola)", "Enter Application DN": "Uygulama DN'sini Girin", "Enter Application DN Password": "Uygulama DN Parolasını Girin", "Enter Bing Search V7 Endpoint": "Bing Arama V7 Uç Noktasını Girin", "Enter Bing Search V7 Subscription Key": "Bing Arama V7 Abonelik Anahtarını Girin", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Brave Search API Anahtarını Girin", "Enter certificate path": "<PERSON><PERSON><PERSON><PERSON> yolunu girin", "Enter CFG Scale (e.g. 7.0)": "CFG Ölçeğini Girin (örn. 7.0)", "Enter Chunk Overlap": "Chunk Örtüşmesini Girin", "Enter Chunk Size": "Chunk <PERSON>", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Açıklama girin", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Github Raw URL'sini girin", "Enter Google PSE API Key": "Google PSE API Anahtarını Girin", "Enter Google PSE Engine Id": "Google PSE Engine Id'sini Girin", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (örn. 512x512)", "Enter Jina API Key": "Jina API Anahtarını Girin", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "Kagi Search API Anahtarını Girin", "Enter Key Behavior": "", "Enter language codes": "<PERSON>l kodlarını girin", "Enter Mistral API Key": "", "Enter Model ID": "Model ID'sini <PERSON>", "Enter model tag (e.g. {{modelTag}})": "Model etiketini girin (örn. {{modelTag}})", "Enter Mojeek Search API Key": "Mojeek Search API Anahtarını Girin", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON> G<PERSON>n (örn. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "Örne<PERSON><PERSON><PERSON> (örn. Euler a)", "Enter Scheduler (e.g. Karras)": "Zamanlayıcıyı Girin (örn. <PERSON>)", "Enter Score": "<PERSON><PERSON><PERSON>", "Enter SearchApi API Key": "Arama-API Anahtarını Girin", "Enter SearchApi Engine": "Arama-API Motorunu Girin", "Enter Searxng Query URL": "Searxng Sorgu URL'sini girin", "Enter Seed": " <PERSON><PERSON>(seed) <PERSON><PERSON><PERSON>", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Serper API Anahtarını Girin", "Enter Serply API Key": "Serply API Anahtarını Girin", "Enter Serpstack API Key": "Serpstack API Anahtarını Girin", "Enter server host": "<PERSON><PERSON><PERSON> ana bilgisayarını girin", "Enter server label": "<PERSON><PERSON><PERSON> et<PERSON>i girin", "Enter server port": "<PERSON><PERSON><PERSON> bağlantı noktasını girin", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Durdurma dizisini girin", "Enter system prompt": "Sistem promptunu girin", "Enter system prompt here": "Sistem promptunu buraya girin.", "Enter Tavily API Key": "Tavily API Anahtarını Girin", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "<PERSON><PERSON> URL'sini G<PERSON>", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Top K'yı girin", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "U<PERSON>'<PERSON><PERSON> (örn. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "U<PERSON>'<PERSON><PERSON> (e.g. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "Mevcut parolanızı girin", "Enter Your Email": "E-postanızı Girin", "Enter Your Full Name": "Tam Adınızı Girin", "Enter your message": "Mesajınızı girin", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "<PERSON>ni parolanızı girin", "Enter Your Password": "Parolanızı Girin", "Enter Your Role": "Rolünüzü Girin", "Enter Your Username": "Kullanıcı Adınızı Girin", "Enter your webhook URL": "Webhook URL'nizi girin", "Error": "<PERSON><PERSON>", "ERROR": "HATA", "Error accessing Google Drive: {{error}}": "Google Drive'a eri<PERSON>im hatası: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "<PERSON><PERSON><PERSON> hata o<PERSON>: {{error}}", "Evaluations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Everyone": "", "Exa API Key": "Exa API Anahtarı", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Örnek: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Örnek: ALL", "Example: mail": "Örnek: mail", "Example: ou=users,dc=foo,dc=example": "Örnek: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Örnek: sAMAccountName or uid or userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "<PERSON><PERSON> tut", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "Genişlet", "Experimental": "Den<PERSON>sel", "Explain": "Açıkla", "Explore the cosmos": "Evreni keşfet", "Export": "Dışa Aktar", "Export All Archived Chats": "Tüm Arşivlenmiş Sohbetleri Dışa Aktar", "Export All Chats (All Users)": "Tüm Sohbetleri Dışa Aktar (Tüm Kullanıcılar)", "Export chat (.json)": "<PERSON><PERSON><PERSON><PERSON> dışa aktar (.json)", "Export Chats": "Sohbetleri Dışa Aktar", "Export Config to JSON File": "Yapılandırmayı JSON Dosyasına Aktar", "Export Functions": "Fonksiyonları Dışa Aktar", "Export Models": "Modelleri Dışa Aktar", "Export Presets": "Ön <PERSON>ı Dışa Aktar", "Export Prompt Suggestions": "", "Export Prompts": "Promptları Dışa Aktar", "Export to CSV": "CSV'ye Aktar", "Export Tools": "Araçları Dışa Aktar", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "<PERSON><PERSON><PERSON>.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "API Anahtarı oluşturulamadı.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to read clipboard contents": "Pano içeriği okunamadı", "Failed to save connections": "", "Failed to save models configuration": "Modeller yapılandırması kaydedilemedi", "Failed to update settings": "<PERSON><PERSON><PERSON>", "Failed to upload file.": "<PERSON><PERSON><PERSON>.", "Features": "<PERSON><PERSON><PERSON><PERSON>", "Features Permissions": "Özellik Yetkileri", "February": "Ş<PERSON><PERSON>", "Feedback Details": "", "Feedback History": "<PERSON><PERSON> Bildirim G<PERSON>ç<PERSON>şi", "Feedbacks": "<PERSON><PERSON>", "Feel free to add specific details": "Spesifik ayrıntılar eklemekten çekinmeyin", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> eklendi.", "File content updated successfully.": "Dosya içeriği başarıyla güncellendi.", "File Mode": "<PERSON><PERSON><PERSON>", "File not found.": "<PERSON><PERSON><PERSON> b<PERSON>.", "File removed successfully.": "<PERSON><PERSON>a başar<PERSON>yla kaldırıldı.", "File size should not exceed {{maxSize}} MB.": "<PERSON><PERSON><PERSON> boy<PERSON> {{maxSize}} <PERSON>'<PERSON><PERSON>.", "File Upload": "", "File uploaded successfully": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Filter": "", "Filter is now globally disabled": "Filtre artık global olarak devre dışı", "Filter is now globally enabled": "Filtre artık global olarak devrede", "Filters": "<PERSON><PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Parmak izi sahteciliği tespit edildi: <PERSON><PERSON> o<PERSON> baş harfler kullanılamıyor. Varsayılan profil resmine dönülüyor.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Büyük harici yanıt chunklarını akıcı bir şekilde yayınlayın", "Focus chat input": "<PERSON><PERSON><PERSON>", "Folder deleted successfully": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Folder Name": "", "Folder name cannot be empty.": "Klasör adı boş olamaz.", "Folder name updated successfully": "Klasör adı başarıyla güncellendi", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Talimatları mükemmel şekilde takip etti", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "<PERSON><PERSON> yollar açın", "Form": "Form", "Format your variables using brackets like this:": "Değişkenlerinizi şu şekilde parantez kullanarak biçimlendirin:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "Fonksiyon", "Function Calling": "Fonksiyon Çağırma", "Function created successfully": "Fonksiyon başarıyla oluşturuldu", "Function deleted successfully": "Fonksiyon başarı<PERSON> si<PERSON>i", "Function Description": "Fonksiyon Açıklaması", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "Fonksiyon artık global olarak devre dışı", "Function is now globally enabled": "Fonksiyon artık global olarak aktif", "Function Name": "Fonksiyon Adı", "Function updated successfully": "Fonksiyon başarıyla güncellendi", "Functions": "Fonksiyonlar", "Functions allow arbitrary code execution.": "Fonksiyonlar keyfi kod yürütülmesine izin verir.", "Functions imported successfully": "Fonksiyonlar başarıyla içe aktarıldı", "Gemini": "Gemini", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "<PERSON><PERSON>", "Generate": "Oluştur", "Generate an image": "Bir Görsel Oluştur", "Generate Image": "<PERSON><PERSON><PERSON><PERSON>", "Generate prompt pair": "", "Generating search query": "<PERSON><PERSON> so<PERSON>", "Generating...": "Oluşturuluyor...", "Get information on {{name}} in the UI": "", "Get started": "Başlayın", "Get started with {{WEBUI_NAME}}": "{{WEBUI_NAME}} ile ba<PERSON><PERSON><PERSON>n", "Global": "<PERSON><PERSON><PERSON><PERSON>", "Good Response": "<PERSON>yi <PERSON>", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API Anahtarı", "Google PSE Engine Id": "Google PSE Engine Id", "Group created successfully": "Grup başarıyla oluşturuldu", "Group deleted successfully": "Grup başar<PERSON><PERSON>", "Group Description": "Grup Açıklaması", "Group Name": "Grup Adı", "Group updated successfully": "Grup başarıyla gü<PERSON>llendi", "Groups": "Gruplar", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Dokunsal Geri Bildirim", "Hello, {{name}}": "<PERSON><PERSON><PERSON><PERSON>, {{name}}", "Help": "Yardım", "Help us create the best community leaderboard by sharing your feedback history!": "En iyi topluluk lider tablosunu oluşturmamıza yardımcı olun, geri bildirim geçmişinizi paylaşın!", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON>", "Hide from Sidebar": "", "Hide Model": "<PERSON><PERSON>", "High Contrast Mode": "", "Home": "", "Host": "<PERSON>", "How can I help you today?": "Bugün size nasıl yardımcı olabilirim?", "How would you rate this response?": "Bu yanıtı nasıl değerlendirirsiniz?", "HTML": "", "Hybrid Search": "<PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON><PERSON><PERSON>min sonuçlarını okuduğumu ve anladığımı kabul ediyorum. Rastgele kod çalıştırmayla ilgili risklerin farkındayım ve kaynağın güvenilirliğini doğruladım.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "<PERSON><PERSON>", "Image": "G<PERSON><PERSON><PERSON>", "Image Compression": "Görüntü Sıkıştırma", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Gör<PERSON><PERSON><PERSON>", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Deneysel)", "Image Generation Engine": "Görüntü Oluşturma Motoru", "Image Max Compression Size": "Görüntü Maksimum Sıkıştırma Boyutu", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "Gör<PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "Sohbetleri İçe Aktar", "Import Config from JSON File": "Yapılandırmayı JSON Dosyasından İçe Aktar", "Import From Link": "", "Import Functions": "Fonksiyonları İçe Aktar", "Import Models": "Modelleri İçe Aktar", "Import Notes": "Notları İçe Aktar", "Import Presets": "Ön A<PERSON>ları İçe Aktar", "Import Prompt Suggestions": "", "Import Prompts": "Promptları İçe Aktar", "Import Tools": "Araçları İçe Aktar", "Include": "<PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "stable-diffusion-webui <PERSON>ı<PERSON>tırılırken `--api-auth` bayrağını dahil edin", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-web<PERSON>ıştırılırken `--api<PERSON> bayrağını dahil edin", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "<PERSON><PERSON><PERSON>", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "<PERSON><PERSON><PERSON> komutları", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Github URL'sinden yükley<PERSON>", "Instant Auto-Send After Voice Transcription": "Ses Transkripsiyonundan Sonra Anında Otomatik Gönder", "Integration": "", "Interface": "Arayüz", "Invalid file content": "", "Invalid file format.": "Geçersiz dosya biçimi.", "Invalid JSON file": "", "Invalid Tag": "Geçersiz etiket", "is typing...": "yazıyor...", "Italic": "", "January": "Ocak", "Jina API Key": "Jina API Anahtarı", "join our Discord for help.": "<PERSON>ım için Disco<PERSON>'um<PERSON> katılın.", "JSON": "JSON", "JSON Preview": "JSON Önizlemesi", "July": "Temmuz", "June": "Haziran", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT Bitişi", "JWT Token": "JWT Token", "Kagi Search API Key": "Kagi Arama API Anahtarı", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Klavye kısayolları", "Knowledge": "<PERSON><PERSON><PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON>", "Knowledge Base": "", "Knowledge created successfully.": "Bilgi başarıyla oluşturuldu.", "Knowledge deleted successfully.": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Bilgi başarıyla sıfırlandı.", "Knowledge updated successfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "Etiket", "Landing Page Mode": "Açılış Sayfa Modu", "Language": "Dil", "Language Locales": "", "Languages": "", "Last Active": "Son Aktivite", "Last Modified": "<PERSON>", "Last reply": "<PERSON> yanıt", "LDAP": "LDAP", "LDAP server updated": "LDAP sun<PERSON><PERSON>u <PERSON>", "Leaderboard": "Liderlik Tablosu", "Learn more about OpenAPI tool servers.": "OpenAPI araç sunucuları hakkında daha fazla bilgi edinin.", "Leave empty for no compression": "", "Leave empty for unlimited": "Sınırsız için boş bırakınız", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Tüm modelleri dahil etmek için boş bırakın veya belirli modelleri seçin", "Leave empty to use the default prompt, or enter a custom prompt": "Varsayılan promptu kullanmak için boş bırakın veya özel bir prompt girin", "Leave model field empty to use the default model.": "", "License": "Lisa<PERSON>", "Lift List": "", "Light": "Açık", "Listening...": "Dinleniyor...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLM'ler hata yapabilir. Önemli bilgileri doğrulayın.", "Loader": "<PERSON><PERSON><PERSON><PERSON>", "Loading Kokoro.js...": "Kokoro.js Yükleniyor...", "Local": "<PERSON><PERSON>", "Local Task Model": "", "Location access not allowed": "", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "Soldan Sağa", "Made by Open WebUI Community": "OpenWebUI Topluluğu tarafından yapılmıştır", "Make password visible in the user interface": "", "Make sure to enclose them with": "Değişkenlerinizi şu şekilde biçimlendirin:", "Make sure to export a workflow.json file as API format from ComfyUI.": "ComfyUI'dan API formatında bir workflow.json dosyası olarak dışa aktardığınızdan emin olun.", "Manage": "<PERSON><PERSON><PERSON>", "Manage Direct Connections": "<PERSON><PERSON><PERSON><PERSON> Bağlantıları Yönet", "Manage Models": "<PERSON><PERSON><PERSON>", "Manage Ollama": "Ollama'yı <PERSON>", "Manage Ollama API Connections": "Ollama API Bağlantılarını Yönet", "Manage OpenAI API Connections": "OpenAI API Bağlantılarını Yönet", "Manage Pipelines": "Pipelineları Yönet", "Manage Tool Servers": "Araç Sunucularını Yönet", "March": "Mart", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "<PERSON><PERSON><PERSON><PERSON> Sayısı", "Max Upload Size": "<PERSON><PERSON><PERSON><PERSON>", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Aynı anda en fazla 3 model indirilebilir. Lütfen daha sonra tekrar deneyin.", "May": "<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "LLM'ler tarafından erişilebilen bellekler burada gösterilecektir.", "Memory": "Bellek", "Memory added successfully": "Bellek başarıyla eklendi", "Memory cleared successfully": "Bellek baş<PERSON><PERSON><PERSON> te<PERSON>", "Memory deleted successfully": "Bellek baş<PERSON><PERSON><PERSON>", "Memory updated successfully": "Bellek başarıyla gü<PERSON>llendi", "Merge Responses": "Yanıtları Birleştir", "Merged Response": "Birleştirilmiş <PERSON>", "Message rating should be enabled to use this feature": "Bu özelliği kullanmak için mesaj derecelendirmesi etkinleştirilmelidir", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Bağlantınızı oluşturduktan sonra gönderdiğiniz mesajlar paylaşılmayacaktır. URL'ye sahip kullanıcılar paylaşılan sohbeti görüntüleyebilecektir.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (kişisel)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (iş/okul)", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "Mistral OCR API Anahtarı Gerekli", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "'{{modelName}}' ba<PERSON><PERSON><PERSON><PERSON> indirildi.", "Model '{{modelTag}}' is already in queue for downloading.": "'{{modelTag}}' zaten indirme sırasında.", "Model {{modelId}} not found": "{{modelId}} bulunamadı", "Model {{modelName}} is not vision capable": "Model {{modelName}} g<PERSON><PERSON><PERSON><PERSON><PERSON>", "Model {{name}} is now {{status}}": "{{name}} modeli art<PERSON> {{status}}", "Model {{name}} is now hidden": "Model {{name}} art<PERSON>k gizli", "Model {{name}} is now visible": "Model {{name}} <PERSON><PERSON><PERSON> g<PERSON>", "Model accepts file inputs": "", "Model accepts image inputs": "Model g<PERSON><PERSON><PERSON><PERSON><PERSON> girdilerini kabul eder", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Model ba<PERSON><PERSON><PERSON><PERSON> oluşturuldu!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Model <PERSON><PERSON> siste<PERSON> yolu al<PERSON>. Güncelleme için model k<PERSON><PERSON> ad<PERSON>, devam edilemiyor.", "Model Filtering": "Model Filtreleme", "Model ID": "Model ID", "Model IDs": "<PERSON> <PERSON><PERSON><PERSON>", "Model Name": "Model Adı", "Model not selected": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Model Params": "Model Parametreleri", "Model Permissions": "<PERSON><PERSON><PERSON>", "Model unloaded successfully": "", "Model updated successfully": "Model b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Model(s) do not support file upload": "", "Modelfile Content": "Model Dosyası İçeriği", "Models": "Modeller", "Models Access": "<PERSON><PERSON><PERSON>", "Models configuration saved successfully": "<PERSON><PERSON>in ya<PERSON>ılandırması başarıyla kaydedildi", "Models Public Sharing": "", "Mojeek Search API Key": "Mojeek Search API Anahtarı", "more": "daha fazla", "More": "<PERSON><PERSON>", "Name": "Ad", "Name your knowledge base": "Bilgi tabanınıza bir ad verin", "Native": "", "New Chat": "<PERSON><PERSON>", "New Folder": "<PERSON><PERSON>", "New Function": "", "New Note": "<PERSON><PERSON>", "New Password": "<PERSON><PERSON>", "New Tool": "", "new-channel": "yeni-kanal", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "İçerik yok", "No content found": "İçerik bulunamadı", "No content found in file.": "Dosyada içerik bulunamadı", "No content to speak": "Konuşacak içerik yok", "No distance available": "<PERSON><PERSON> mevcut de<PERSON>", "No feedbacks found": "<PERSON><PERSON> bi<PERSON><PERSON><PERSON> bulu<PERSON>ı", "No file selected": "Hiçbir dosya seçilmedi", "No groups with access, add a group to grant access": "<PERSON><PERSON>şimi olan grup yok, <PERSON><PERSON><PERSON><PERSON> için bir grup ekleyin", "No HTML, CSS, or JavaScript content found.": "HTML, CSS veya JavaScript içeriği bulunamadı.", "No inference engine with management support found": "", "No knowledge found": "<PERSON><PERSON><PERSON> bulunamadı", "No memories to clear": "Temizlenecek bellek yok", "No model IDs": "Model ID yok", "No models found": "Model bulunamadı", "No models selected": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "No Notes": "Not Yok", "No results found": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "No search query generated": "Hiç arama sorgusu oluşturulmadı", "No source available": "<PERSON><PERSON>k mevcut de<PERSON>", "No users were found.": "Kullanıcı bulunamadı.", "No valves to update": "Güncellenecek valvler yok", "None": "Yok", "Not factually correct": "Gerçeklere göre doğru <PERSON>", "Not helpful": "Yardımcı olmadı", "Note deleted successfully": "Not ba<PERSON><PERSON><PERSON><PERSON> silindi", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Not: Minimum bir skor belirlerseniz, arama yalnızca minimum skora eşit veya daha yüksek bir skora sahip belgeleri getirecektir.", "Notes": "Notlar", "Notification Sound": "<PERSON><PERSON><PERSON><PERSON>", "Notification Webhook": "<PERSON><PERSON><PERSON><PERSON>", "Notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "November": "Kasım", "OAuth ID": "OAuth ID", "October": "<PERSON><PERSON>", "Off": "<PERSON><PERSON><PERSON>", "Okay, Let's Go!": "<PERSON><PERSON>, <PERSON><PERSON> Başlayalım!", "OLED Dark": "OLED Koyu", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API ayarları güncellendi", "Ollama Version": "Ollama Sürümü", "On": "Açık", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Yalnızca alfasayısal karakterler ve tireler kabul edilir", "Only alphanumeric characters and hyphens are allowed in the command string.": "Komut dizisinde yalnızca alfasayısal karakterler ve tireler kabul edilir.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Yalnızca koleksiyonlar düzenlenebilir, be<PERSON><PERSON>i düzenlemek/eklemek için yeni bir bilgi tabanı oluşturun.", "Only markdown files are allowed": "Yalnızca markdown biç<PERSON>li dosyalar kullanılabilir", "Only select users and groups with permission can access": "İzinli kullanıcılar ve gruplar yalnızca erişebilir", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hop! URL geçersiz gibi görünüyor. Lütfen tekrar kontrol edin ve yeniden deneyin.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hop! Hala yüklenen dosyalar var. Yüklemenin tamamlanmasını bekleyin.", "Oops! There was an error in the previous response.": "Hop! Önceki yanıtta bir hata oluş<PERSON>.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hop! Desteklenmeyen bir yöntem kullanıyorsunuz (yalnızca önyüz). Lütfen WebUI'yi arkayüzden sunun.", "Open file": "Dosyayı aç", "Open in full screen": "Tam ekranda aç", "Open modal to configure connection": "", "Open new chat": "<PERSON><PERSON> so<PERSON> aç", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI OpenAPI tarafından sağlanan araçları kullanabilir", "Open WebUI uses faster-whisper internally.": "Open WebUI, da<PERSON>i olarak daha hızlı-fısıltı kullanır.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI, SpeechT5 ve CMU Arctic konuşmacı gömme kullanır.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open-WebUI sürümü (v{{OPEN_WEBUI_VERSION}}) gere<PERSON><PERSON> sürümden (v{{REQUIRED_VERSION}}) düşük", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API Konfigürasyonu", "OpenAI API Key is required.": "OpenAI API Anahtarı gereklidir.", "OpenAI API settings updated": "OpenAI API ayarları güncellendi", "OpenAI URL/Key required.": "OpenAI URL/Anahtar gereklidir.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "veya", "Ordered List": "", "Organize your users": "Kullanıcılarınızı düzenleyin", "Other": "<PERSON><PERSON><PERSON>", "OUTPUT": "ÇIKTI", "Output format": "Çıkt<PERSON> formatı", "Output Format": "", "Overview": "Genel Bakış", "page": "sayfa", "Paginate": "", "Parameters": "", "Password": "Pa<PERSON><PERSON>", "Paste Large Text as File": "Büyük Metni Dosya Olarak Yapıştır", "PDF document (.pdf)": "PDF belgesi (.pdf)", "PDF Extract Images (OCR)": "PDF Görüntülerini Çıkart (OCR)", "pending": "be<PERSON><PERSON>e", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Medya c<PERSON>azlarına erişim izni reddedildi", "Permission denied when accessing microphone": "Mikrofona erişim izni reddedildi", "Permission denied when accessing microphone: {{error}}": "Mikrofona erişim izni reddedildi: {{error}}", "Permissions": "<PERSON><PERSON><PERSON>", "Perplexity API Key": "Perplexity API Anahtarı", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "<PERSON><PERSON><PERSON><PERSON>", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON><PERSON><PERSON>", "Pinned": "Sabitlenmiş", "Pioneer insights": "<PERSON><PERSON><PERSON> içgörüler", "Pipe": "", "Pipeline deleted successfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "Pipeline downloaded successfully": "Pipeline başarıyla g<PERSON>", "Pipelines": "Pipelinelar", "Pipelines Not Detected": "Pipeline Tespit Edilmedi", "Pipelines Valves": "<PERSON><PERSON><PERSON>", "Plain text (.md)": "<PERSON><PERSON><PERSON> (.md)", "Plain text (.txt)": "<PERSON><PERSON><PERSON> metin (.txt)", "Playground": "<PERSON><PERSON> Alanı", "Playwright Timeout (ms)": "Playwright <PERSON><PERSON><PERSON><PERSON><PERSON> (ms)", "Playwright WebSocket URL": "Playwright WebSocket URL", "Please carefully review the following warnings:": "Lütfen aşağıdaki uyarıları dikkatlice inceleyin:", "Please do not close the settings page while loading the model.": "Lütfen model <PERSON><PERSON><PERSON><PERSON><PERSON> ayarlar sayfasını kapatmayınız", "Please enter a prompt": "<PERSON>ü<PERSON>fen bir prompt girin", "Please enter a valid path": "Lütfen geçerli bir yol giriniz", "Please enter a valid URL": "Lütfen geçerli bir URL adresi giriniz", "Please fill in all fields.": "Lütfen tüm alanları doldurun.", "Please select a model first.": "Lütfen önce bir model seçin.", "Please select a model.": "Lütfen bir model seçin", "Please select a reason": "Lütfen bir neden se<PERSON>in", "Port": "Port", "Positive attitude": "O<PERSON>lu yaklaşım", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Önceki 30 gün", "Previous 7 days": "Önceki 7 gün", "Previous message": "", "Private": "<PERSON><PERSON><PERSON>", "Profile Image": "Profil Fotoğrafı", "Prompt": "İstem", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (örn. Roma İmparatorluğu hakkında ilginç bir bilgi verin)", "Prompt Autocompletion": "", "Prompt Content": "İstem İçeriği", "Prompt created successfully": "Prompt başarıyla oluşturuldu", "Prompt suggestions": "İstem önerileri", "Prompt updated successfully": "Prompt ba<PERSON><PERSON><PERSON><PERSON>", "Prompts": "İstemler", "Prompts Access": "İstemlere Erişim", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com'dan \"{{searchValue}}\" çekin", "Pull a model from Ollama.com": "Ollama.com'dan bir model <PERSON><PERSON><PERSON>", "Query Generation Prompt": "Sorgu Oluşturma Promptu", "RAG Template": "RAG Şablonu", "Rating": "Derecelendirme", "Re-rank models by topic similarity": "<PERSON><PERSON> ben<PERSON>ine göre modelleri yeniden sırala", "Read": "<PERSON><PERSON>", "Read Aloud": "<PERSON><PERSON><PERSON>", "Reason": "", "Reasoning Effort": "", "Record": "<PERSON><PERSON>", "Record voice": "<PERSON><PERSON> kaydı yap", "Redirecting you to Open WebUI Community": "OpenWebUI Topluluğuna yönlendiriliyorsunuz", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Kendinizden \"User\" o<PERSON><PERSON> b<PERSON> (<PERSON><PERSON><PERSON><PERSON>, \"User İspanyolca öğreniyor\")", "References from": "Referanslar arasından", "Refused when it shouldn't have": "Reddedilmemesi gerekirken reddedildi", "Regenerate": "<PERSON><PERSON><PERSON>", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Releases": "", "Relevance": "İlgili", "Relevance Threshold": "<PERSON><PERSON><PERSON>", "Remember Dismissal": "", "Remove": "Kaldır", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON>", "Remove this tag from list": "", "Rename": "<PERSON><PERSON><PERSON>", "Reorder Models": "Modelleri <PERSON>", "Reply in Thread": "<PERSON><PERSON><PERSON>", "Reranking Engine": "", "Reranking Model": "<PERSON><PERSON><PERSON> Modeli", "Reset": "Sıfırla", "Reset All Models": "Tüm Modelleri Sıfırla", "Reset Upload Directory": "<PERSON><PERSON><PERSON><PERSON> Dizinini <PERSON>", "Reset Vector Storage/Knowledge": "Vektör <PERSON>/Bilgiyi Sıfırla", "Reset view": "Görünümü sıfırla", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Web sitesi izinleri reddedildiğinden yanıt bildirimleri etkinleştirilemiyor. Gerekli erişimi sağlamak için lütfen tarayıcı ayarlarınızı ziyaret edin.", "Response splitting": "<PERSON><PERSON><PERSON>", "Response Watermark": "", "Result": "<PERSON><PERSON><PERSON>", "Retrieval": "", "Retrieval Query Generation": "Alıntı Sorgu Oluşturma", "Rich Text Input for Chat": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "<PERSON><PERSON><PERSON>", "Run": "Çalıştır", "Running": "Çalışıyor", "Save": "<PERSON><PERSON>", "Save & Create": "<PERSON><PERSON> ve <PERSON>luştur", "Save & Update": "<PERSON><PERSON>", "Save As Copy": "<PERSON><PERSON><PERSON>", "Save Tag": "<PERSON><PERSON><PERSON><PERSON>", "Saved": "<PERSON><PERSON><PERSON><PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Sohbet kayıtlarının doğrudan tarayıcınızın depolama alanına kaydedilmesi artık desteklenmemektedir. Lütfen aşağıdaki butona tıklayarak sohbet kayıtlarınızı indirmek ve silmek için bir dakikanızı ayırın. Endişelenmeyin, sohbet günlüklerinizi arkayüze kolayca yeniden aktarabilirsiniz:", "Scroll On Branch Change": "", "Search": "Ara", "Search a model": "Bir model ara", "Search Base": "<PERSON><PERSON>", "Search Chats": "Sohbetleri Ara", "Search Collection": "Koleksiyon Ara", "Search Filters": "Filtreleri Ara", "search for tags": "etiketler i<PERSON>in ara", "Search Functions": "Fonksiyonları Ara", "Search Knowledge": "<PERSON><PERSON><PERSON>", "Search Models": "Modelleri Ara", "Search Notes": "", "Search options": "<PERSON><PERSON>", "Search Prompts": "Prompt Ara", "Search Result Count": "Arama Sonuc<PERSON>", "Search the internet": "<PERSON><PERSON><PERSON><PERSON>", "Search Tools": "Arama <PERSON>", "SearchApi API Key": "Arama-API API Anahtarı", "SearchApi Engine": "Arama-API Motoru", "Searched {{count}} sites": "{{count}} site arandı", "Searching \"{{searchQuery}}\"": "\"{{searchQuery}}\" aranıyor", "Searching Knowledge for \"{{searchQuery}}\"": "\"{{searchQuery}}\" i<PERSON><PERSON>", "Searching the web...": "İnternette aranıyor...", "Searxng Query URL": "Searxng Sorgu URL'si", "See readme.md for instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>in readme.md dosyasına bakın", "See what's new": "Yeniliklere göz atın", "Seed": "Seed", "Select a base model": "Bir temel model seç", "Select a conversation to preview": "", "Select a engine": "Bir motor seç", "Select a function": "Bir fonksiyon seç", "Select a group": "Bir grup seç", "Select a model": "Bir model seç", "Select a pipeline": "Bir pipeline seç", "Select a pipeline url": "Bir pipeline URL'si seç", "Select a tool": "Bir araç seç", "Select an auth method": "Yetkilendirme yöntemi seç", "Select an Ollama instance": "", "Select Engine": "Motor Seç", "Select Knowledge": "<PERSON><PERSON><PERSON>", "Select only one model to call": "<PERSON><PERSON><PERSON> sad<PERSON>e bir model seç", "Selected model(s) do not support image inputs": "Seçilen model(ler) görüntü girişlerini desteklemiyor", "Semantic distance to query": "<PERSON><PERSON><PERSON><PERSON> seman<PERSON>k mesafe", "Send": "<PERSON><PERSON><PERSON>", "Send a Message": "<PERSON><PERSON> <PERSON><PERSON>", "Send message": "<PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "İsteğe `stream_options: { include_usage: true }` gönderir.\nDesteklenen sağlayıcılar, ayarlandığında yanıtta token kullanım bilgilerini döndürecektir.", "September": "<PERSON><PERSON><PERSON><PERSON>", "SerpApi API Key": "SerpApi API Anahtarı", "SerpApi Engine": "SerpApi <PERSON>", "Serper API Key": "Serper API Anahtarı", "Serply API Key": "Serply API Anahtarı", "Serpstack API Key": "Serpstack API Anahtarı", "Server connection verified": "<PERSON><PERSON><PERSON> bağlantısı doğrulandı", "Set as default": "Varsayılan olarak a<PERSON>", "Set CFG Scale": "CFG Ölçeğini Ayarla", "Set Default Model": "Varsayılan Modeli Ayarla", "Set embedding model": "<PERSON><PERSON><PERSON> model<PERSON>", "Set embedding model (e.g. {{model}})": "Gömme modelini ayarlayın (örn. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Ye<PERSON>den sıralama modelini ayarlayın (örn. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set Scheduler": "Zamanlayıcıyı Ayarla", "Set Steps": "Adımları Ayarla", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON>", "Set whisper model": "Fısıltı modelini ayarla", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Kullanılacak durma dizilerini ayarlar. <PERSON><PERSON> desen<PERSON> ka<PERSON>, LLM metin oluşturmayı durduracak ve geri dönecektir. Birden çok durma deseni, bir modelfile'da birden çok ayrı durma parametresi belirterek ayarlanabilir.", "Settings": "<PERSON><PERSON><PERSON>", "Settings saved successfully!": "<PERSON><PERSON><PERSON> başar<PERSON>yla kaydedildi!", "Share": "Paylaş", "Share Chat": "<PERSON><PERSON><PERSON><PERSON>", "Share to Open WebUI Community": "OpenWebUI Topluluğu ile Paylaş", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "G<PERSON><PERSON><PERSON> \"Yenilikler\" modalını göster", "Show Admin Details in Account Pending Overlay": "Yönetici Ayrıntılarını Hesap Bekliyor Ekranında Göster", "Show All": "Tümünü <PERSON>ö<PERSON>", "Show image preview": "", "Show Less": "<PERSON><PERSON>", "Show Model": "<PERSON><PERSON>", "Show shortcuts": "Kısayolları göster", "Show your support!": "Desteğinizi gösterin!", "Showcased creativity": "Sergilenen yaratıcılık", "Sign in": "Oturum aç", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}'e giri<PERSON> yap", "Sign in to {{WEBUI_NAME}} with LDAP": "LDAP ile {{WEBUI_NAME}}'e giriş yap", "Sign Out": "Çıkış Yap", "Sign up": "<PERSON><PERSON><PERSON>", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}}'e kaydol", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}'e giri<PERSON>ı<PERSON>r", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "Konuşma Oynatma Hızı", "Speech recognition error: {{error}}": "Konuşma tanıma hatası: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Konuşmadan Metne Motoru", "Stop": "<PERSON><PERSON><PERSON>", "Stop Generating": "", "Stop Sequence": "<PERSON><PERSON><PERSON>", "Stream Chat Response": "Akış Sohbet Yanıtı", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT Modeli", "STT Settings": "STT Ayarları", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Alt başlık (örn. Roma İmparatorluğu hakkında)", "Success": "Başarılı", "Successfully updated.": "Başarıyla güncellendi.", "Suggested": "Önerilen", "Support": "Destek", "Support this plugin:": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> deste<PERSON>:", "Supported MIME Types": "", "Sync directory": "<PERSON><PERSON>i senkronize et", "System": "Sistem", "System Instructions": "Sistem Talimatları", "System Prompt": "Sistem Promptu", "Tags": "<PERSON><PERSON><PERSON><PERSON>", "Tags Generation": "Etiketler Oluşturma", "Tags Generation Prompt": "Etiketler Oluşturma Promptu", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "<PERSON> ile k<PERSON>", "Tap to interrupt": "Durdurmak iç<PERSON> do<PERSON>n", "Task List": "", "Task Model": "", "Tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tavily API Key": "Tavily API Anahtarı", "Tavily Extract Depth": "", "Tell us more:": "Bize daha fazlasını anlat:", "Temperature": "Temperature", "Temporary Chat": "Geçici Sohbet", "Text Splitter": "<PERSON><PERSON>", "Text-to-Speech": "", "Text-to-Speech Engine": "Metinden Sese Motoru", "Thanks for your feedback!": "<PERSON><PERSON> bildiri<PERSON>z i<PERSON> teşekkürler!", "The Application Account DN you bind with for search": "Arama için bağlandığınız Uygulama Hesap DN'si", "The base to search for users": "Kullanıcıları aramak için temel", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Bu eklentinin arkasındaki geliştiriciler topluluktan tutkulu gönüllülerdir. Bu eklentinin yararlı olduğunu dü<PERSON>ü<PERSON>ü<PERSON>n<PERSON>z, gel<PERSON><PERSON><PERSON><PERSON> katkıda bulunmayı düşünün.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "MB cinsinden maksimum dosya boyutu. <PERSON><PERSON>a boyutu bu sınır<PERSON> aşarsa, dosya yüklenmeyecektir.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Sohbette aynı anda kullanılabilecek maksimum dosya sayısı. Dosya sayısı bu sınırı aşarsa, dosyalar yüklenmeyecektir.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Puan 0.0 (%0) ile 1.0 (%100) arasında bir değer olmalıdır.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Düşünüyor...", "This action cannot be undone. Do you wish to continue?": "<PERSON>u eylem geri alınamaz. Devam etmek istiyor musunuz?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Bu, önemli konuşmalarınızın güvenli bir şekilde arkayüz veritabanınıza kaydedildiğini garantiler. Teşekkür ederiz!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "<PERSON>u deneysel bir <PERSON>, beklendiği gibi çalışmayabilir ve her an değişiklik yapılabilir.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "<PERSON><PERSON>, koleksiyondaki tüm mevcut dosyaları silecek ve bunları yeni yüklenen dosyalarla değiştirecek.", "This response was generated by \"{{model}}\"": "Bu yanıt \"{{model}}\" tarafından oluşturuldu", "This will delete": "<PERSON><PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<strong>{{NAME}}</strong> ve <strong>tü<PERSON></strong> si<PERSON><PERSON><PERSON>.", "This will delete all models including custom models": "<PERSON><PERSON>, özel modeller da<PERSON> olmak üzere tüm modelleri silecek", "This will delete all models including custom models and cannot be undone.": "<PERSON><PERSON>, özel modeller dahil olmak üzere tüm modelleri silecek ve geri alınamaz.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON><PERSON>, bilgi tabanını sıfırlayacak ve tüm dosyaları senkronize edecek. Devam etmek istiyor musunuz?", "Thorough explanation": "Kapsamlı açıklama", "Thought for {{DURATION}}": "{{DURATION}} saniye düş<PERSON>ü", "Thought for {{DURATION}} seconds": "{{DURATION}} saniye düş<PERSON>ü", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "<PERSON><PERSON> Sunucu URL'si gereklidir.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "İpucu: Her değiştirmeden sonra sohbet girişinde tab tuşuna basarak birden fazla değ<PERSON>şken yuvasını art arda günce<PERSON>yin.", "Title": "Başlık", "Title (e.g. Tell me a fun fact)": "Başlık (e.g. Ban<PERSON> ilginç bir bilgi ver)", "Title Auto-Generation": "Otomatik Başlık Oluşturma", "Title cannot be an empty string.": "Başlık boş bir dize olamaz.", "Title Generation": "Başlık Oluşturma", "Title Generation Prompt": "Başlık Oluşturma İstemi", "TLS": "TLS", "To access the available model names for downloading,": "İndirilebilir mevcut model <PERSON><PERSON><PERSON><PERSON>,", "To access the GGUF models available for downloading,": "İndirilebilir mevcut GGUF modellerine erişmek için,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI'ye erişmek için lütfen yöneticiyle iletişime geçin. Yöneticiler kullanıcı durumlarını Yönetici Panelinden yönetebilir.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Bilgi tabanını buraya eklemek için önce bunları \"Bilgi\" çalışma alanına ekleyin.", "To learn more about available endpoints, visit our documentation.": "Mevcut uç noktalar hakkında daha fazla bilgi edinmek için belgelerimize göz atın.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "Burada eylemleri seçmek için öncelikle bunları \"İşlevler\" çalışma alanına ekleyin.", "To select filters here, add them to the \"Functions\" workspace first.": "Filtreleri burada seçmek için öncelikle bunları \"İşlevler\" çalışma alanına ekleyin.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Araçları burada seçmek için öncelikle bunları \"Araçlar\" çalışma alanına ekleyin.", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle search": "", "Toggle settings": "Ayarları Aç/Kapat", "Toggle sidebar": "Kenar <PERSON>u Aç/Kapat", "Toggle whether current connection is active.": "", "Token": "<PERSON><PERSON><PERSON>", "Too verbose": "Çok ayrıntılı", "Tool created successfully": "<PERSON>ç başarıyla oluşturuldu", "Tool deleted successfully": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "Tool Description": "<PERSON><PERSON>", "Tool ID": "", "Tool imported successfully": "Araç başarıyla içe aktarıldı", "Tool Name": "<PERSON><PERSON>", "Tool Servers": "<PERSON><PERSON>", "Tool updated successfully": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "Tools": "Araçlar", "Tools Access": "<PERSON><PERSON><PERSON><PERSON>", "Tools are a function calling system with arbitrary code execution": "<PERSON><PERSON><PERSON>, keyfi kod yürütme ile bir fonksiyon çağırma sistemine sa<PERSON>r", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "<PERSON><PERSON><PERSON>, keyfi kod yürütme izni veren bir fonksiyon çağırma sistemine sa<PERSON>tir.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON><PERSON>ya erişmede sorun mu yaşıyorsunuz?", "Trust Proxy Environment": "", "TTS Model": "TTS Modeli", "TTS Settings": "TTS Ayarları", "TTS Voice": "TTS Sesi", "Type": "<PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "HuggingFace Çözümleme (İndirme) URL'sini Yazın", "Uh-oh! There was an issue with the response.": "Opps! Yanıtla ilgili bir sorun oluş<PERSON>", "UI": "Arayüz", "Unarchive All": "Tümünü Arşivden Çıkar", "Unarchive All Archived Chats": "Arşivlenmiş Tüm Sohbetleri Arşivden Çıkar", "Unarchive Chat": "Sohbeti Arşivden Çıkar", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "Sabitlemeyi Kaldır", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "Etiketsiz", "Untitled": "Başlıksız", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Güncelle ve Bağlantıyı Kopyala", "Update for the latest features and improvements.": "En son özellikler ve iyileştirmeler i<PERSON><PERSON>.", "Update password": "Parolayı Güncelle", "Updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated at": "<PERSON><PERSON> ta<PERSON> gü<PERSON>:", "Updated At": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "<PERSON><PERSON><PERSON>", "Upload a GGUF model": "Bir GGUF modeli yükle", "Upload Audio": "<PERSON><PERSON>", "Upload directory": "<PERSON><PERSON><PERSON>", "Upload files": "Dosyaları yükle", "Upload Files": "Dosyaları Yükle", "Upload Pipeline": "<PERSON><PERSON><PERSON>", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON>", "URL": "URL", "URL Mode": "URL Modu", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Bilginizi yüklemek ve dahil etmek için prompt girişinde '#' kullanın.", "Use Gravatar": "<PERSON><PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "Kullanıcılarınızı gruplamak ve izinler atamak için grupları kullanın.", "Use Initials": "Baş Harfleri Kullan", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "kullanıcı", "User": "Kullanıcı", "User location successfully retrieved.": "Kullanıcı konumu başarıyla alındı.", "User menu": "", "User Webhooks": "", "Username": "Kullanıcı Adı", "Users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Tüm modellerle varsayılan arena modelini kullanıyor. Özel modeller eklemek için artı düğmesine tıklayın.", "Valid time units:": "Geçerli zaman birimleri:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "<PERSON><PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "variable": "değişken", "Verify Connection": "Bağlantıyı Doğrula", "Verify SSL Certificate": "SSL Sertifikasını Doğrula", "Version": "S<PERSON>r<PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Sürüm {{selectedVersion}} / {{totalVersions}}", "View Replies": "Yanıtları Görüntüle", "View Result from **{{NAME}}**": "", "Visibility": "Görünürlük", "Vision": "", "Voice": "Ses", "Voice Input": "<PERSON><PERSON>", "Voice mode": "", "Warning": "Uyarı", "Warning:": "Uyarı:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Uyarı: <PERSON><PERSON> <PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sunucuya rastgele kod yüklemesine izin verilecektir.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Uyarı: Gömme modelinizi günceller veya <PERSON>ğiştirirseniz, tüm belgeleri yeniden içe aktarmanız gerekecektir.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Web", "Web API": "Web API", "Web Loader Engine": "", "Web Search": "Web Araması", "Web Search Engine": "Web Arama Motoru", "Web Search in Chat": "", "Web Search Query Generation": "Web Arama Sorgusu <PERSON>ma", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI Ayarları", "WebUI URL": "WebUI URL'si", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI, \"{{url}}/api/chat\" adresine istek yapa<PERSON>k", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI, \"{{url}}/chat/completions\" adresine istek yapa<PERSON>k", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "Ne yapmaya çalışıyorsunuz?", "What are you working on?": "Üzerinde çalıştığınız nedir?", "What's New in": "<PERSON><PERSON><PERSON><PERSON>:", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, model her sohbet mesajına ger<PERSON><PERSON> zamanlı olarak yanıt verecek ve kullanıcı bir mesaj gönder<PERSON>ğ<PERSON> anda bir yanıt üretecektir. Bu mod canlı sohbet uygulamaları için ya<PERSON>lıdır, ancak daha yavaş donanımlarda performansı etkileyebilir.", "wherever you are": "<PERSON><PERSON>e o<PERSON> olun", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Whisper (<PERSON><PERSON>)", "Why?": "<PERSON><PERSON>?", "Widescreen Mode": "Geniş Ekran <PERSON>", "Won": "kazandı", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Çalışma Alanı", "Workspace Permissions": "Çalışma Alanı İzinleri", "Write": "Yaz", "Write a prompt suggestion (e.g. Who are you?)": "Bir prompt <PERSON><PERSON><PERSON> ya<PERSON>ın (örn. <PERSON> kimsin?)", "Write a summary in 50 words that summarizes [topic or keyword].": "[Konuyu veya anahtar kelimeyi] özetleyen 50 kelimelik bir özet yazın.", "Write something...": "Bir şeyler yazın...", "Yacy Instance URL": "", "Yacy Password": "<PERSON><PERSON>", "Yacy Username": "<PERSON><PERSON>", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "<PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON><PERSON>ı anda en fazla {{maxCount}} dosya ile sohbet edebilirsiniz.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Aşağıdaki 'Yönet' düğmesi aracılığıyla bellekler ekleyerek LLM'lerle etkileşimlerinizi kişiselleştirebilir, onları daha yararlı ve size özel hale getirebilirsiniz.", "You cannot upload an empty file.": "Boş bir dosya yükleyemezsiniz.", "You do not have permission to upload files.": "Dosya yüklemek için izniniz yok.", "You have no archived conversations.": "Arşivlenmiş sohbetleriniz yok.", "You have shared this chat": "<PERSON>u sohbeti paylaştınız", "You're a helpful assistant.": "<PERSON>ımsever bir asistansın.", "You're now logged in.": "Şimdi giriş yaptınız.", "Your account status is currently pending activation.": "<PERSON><PERSON><PERSON> anda et<PERSON>ştirilmeyi bekliyor.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Tüm katkınız doğrudan eklenti geliştiricisine gidecektir; Open WebUI herhangi bir yüzde almaz. Ancak seçilen finansman platformunun kendi ücretleri olabilir.", "Youtube": "Youtube", "Youtube Language": "Youtube Dili", "Youtube Proxy URL": "Youtube Vekil URL'si"}