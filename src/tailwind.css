@import 'tailwindcss';

@config '../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@layer base {
	html,
	pre {
		font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'Vazirmatn', ui-sans-serif, system-ui,
			'Segoe UI', Roboto, Ubuntu, Cantarell, 'Noto Sans', sans-serif, 'Helvetica Neue', Arial,
			'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
	}

	pre {
		white-space: pre-wrap;
	}

	button {
		@apply cursor-pointer;
	}

	input::placeholder,
	textarea::placeholder {
		color: theme(--color-gray-400);
	}

	input[type='checkbox'] {
		@apply appearance-none size-3.5 align-middle bg-white border border-gray-300 rounded transition cursor-pointer focus:ring-2 focus:ring-blue-500 focus:outline-none dark:bg-gray-800 dark:border-gray-600 self-center;
		/* Center the custom mark */
		display: inline-block;
		position: relative;
	}
	input[type='checkbox']:checked {
		@apply bg-blue-600 border-blue-600;
	}
	input[type='checkbox']:after {
		content: '';
		display: block;
		width: 100%;
		height: 100%;
		/* Hide by default */
		opacity: 0;
		transition: opacity 0.2s;
		position: absolute;
		top: 0;
		left: 0;
		pointer-events: none;
		/* SVG checkmark as background image */
		background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 16 16" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path d="M4 8l3 3l5-5"/></svg>')
			center/80% no-repeat;
	}
	input[type='checkbox']:checked:after {
		opacity: 1;
	}
}

@custom-variant hover (&:hover);
