stages:        
  - build
  - deploy
  
.runners:
  # 指定runner名
  tags: 
    - ai-w2

.branches:
  # 在哪个分支上可用
  only:
    - staging

open-webui-build_dev:
  extends: 
    - .runners
    - .branches

  stage: build

  script:
    - cd /data/open-webui_dev
    - echo "當前工作目錄：$(pwd)"
    - git pull
    - npm install
    - npm run build
    - echo "當前工作目錄：$(pwd)"
    
open-webui-deploy_dev:
  extends: 
    - .runners
    - .branches

  stage: deploy

  needs:
    - job: open-webui-build_dev

  script:
    - echo "當前工作目錄：$(pwd)"
    - pm2 restart open-webui_dev
    - echo "done!"
  