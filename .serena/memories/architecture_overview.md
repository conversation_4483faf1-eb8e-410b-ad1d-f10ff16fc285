# 系统架构概览

## 整体架构

Open WebUI 采用现代全栈架构，前后端分离设计：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Svelte)  │────│  后端 (FastAPI)  │────│  数据库/向量库   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         │                        │                        │
    ┌─────────┐              ┌─────────┐              ┌─────────┐
    │ 静态资源 │              │ AI/LLM  │              │  文件存储 │
    └─────────┘              └─────────┘              └─────────┘
```

## 前端架构 (SvelteKit)

### 目录结构

```
src/
├── routes/                # 页面路由
│   ├── (app)/            # 主应用路由组
│   ├── auth/             # 认证页面
│   └── +layout.svelte    # 全局布局
├── lib/
│   ├── components/       # 可复用组件
│   ├── apis/            # API 调用封装
│   ├── stores/          # 状态管理
│   ├── utils/           # 工具函数
│   └── i18n/           # 国际化
└── app.html             # HTML 模板
```

### 核心组件

- **Chat**: 聊天界面核心组件
- **MessageInput**: 消息输入组件
- **ModelSelector**: 模型选择器
- **Sidebar**: 侧边栏导航
- **Admin**: 管理界面组件

### 状态管理

使用 Svelte 内置的 stores 进行状态管理：

- 用户状态
- 聊天历史
- 配置设置
- UI 状态

## 后端架构 (FastAPI)

### 目录结构

```
backend/open_webui/
├── routers/             # API 路由
├── models/              # 数据模型
├── utils/               # 工具函数
├── retrieval/           # RAG 相关
├── socket/              # WebSocket 处理
└── main.py             # 应用入口
```

### 核心模块

#### API 路由 (routers/)

- `auths.py`: 用户认证
- `chats.py`: 聊天管理
- `models.py`: 模型管理
- `files.py`: 文件处理
- `openai.py`: OpenAI API 兼容
- `ollama.py`: Ollama 集成

#### 数据模型 (models/)

- `users.py`: 用户模型
- `chats.py`: 聊天记录
- `files.py`: 文件管理
- `knowledge.py`: 知识库

#### 检索增强生成 (retrieval/)

- `vector/`: 向量数据库集成
- `web/`: 网络搜索集成
- `loaders/`: 文档加载器

## 数据层架构

### 主数据库

- **SQLite**: 默认数据库 (开发/小规模部署)
- **PostgreSQL**: 生产环境推荐
- **MySQL**: 可选支持

### 向量数据库

- **ChromaDB**: 默认向量数据库
- **Qdrant**: 高性能选项
- **Milvus**: 大规模部署
- **Pinecone**: 云服务选项

### 缓存层

- **Redis**: 会话缓存、API 缓存

## AI/LLM 集成架构

### 支持的 LLM 提供商

- **Ollama**: 本地模型运行
- **OpenAI**: GPT 系列模型
- **Anthropic**: Claude 系列
- **Google**: Gemini 系列
- **自定义 API**: OpenAI 兼容接口

### RAG 架构

```
文档输入 → 文档处理 → 向量化 → 向量存储
                                    ↓
用户查询 → 查询向量化 → 相似度搜索 → 上下文检索
                                    ↓
上下文 + 查询 → LLM 生成 → 响应返回
```

## 部署架构

### Docker 部署

- 单容器部署 (包含前后端)
- 多容器部署 (前后端分离)
- 包含 Ollama 的完整部署

### Kubernetes 部署

- Helm Charts 支持
- 水平扩展配置
- 服务发现和负载均衡

### 存储架构

- **本地存储**: 文件系统存储
- **云存储**: S3, Azure Blob, Google Cloud
- **数据库存储**: 元数据和配置

## 安全架构

### 认证授权

- JWT Token 认证
- OAuth 2.0 集成
- LDAP 集成
- 基于角色的访问控制 (RBAC)

### 安全特性

- API 密钥管理
- 请求速率限制
- 内容过滤
- 审计日志

## 可扩展性设计

### 插件系统

- **Pipelines**: 自定义处理管道
- **Functions**: Python 函数集成
- **Tools**: 外部工具集成

### API 扩展

- OpenAI 兼容 API
- 自定义端点
- Webhook 支持

## 监控和可观测性

### 日志系统

- **Loguru**: 结构化日志
- **审计日志**: 用户操作追踪
- **性能日志**: API 响应时间

### 监控集成

- **OpenTelemetry**: 分布式追踪
- **Langfuse**: LLM 调用监控
- **健康检查**: 服务状态监控
