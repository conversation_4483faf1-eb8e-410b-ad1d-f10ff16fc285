# Open WebUI 项目概览

## 项目目的

Open WebUI 是一个功能丰富、用户友好的自托管 AI 平台，设计为完全离线运行。它支持各种 LLM 运行器，如 Ollama 和 OpenAI 兼容的 API，内置推理引擎用于 RAG，是一个强大的 AI 部署解决方案。

## 技术栈

### 前端

- **框架**: SvelteKit (Svelte 4.2.18)
- **构建工具**: Vite 5.4.14
- **样式**: TailwindCSS 4.0.0
- **语言**: TypeScript 5.5.4
- **UI 组件**: 自定义组件 + bits-ui
- **编辑器**: ByteMD, CodeMirror, TipTap
- **图表**: Mermaid
- **国际化**: i18next

### 后端

- **框架**: FastAPI 0.115.7
- **服务器**: Uvicorn 0.34.0
- **Python 版本**: 3.11-3.12
- **数据库**: SQLAlchemy 2.0.38, Alembic (迁移)
- **支持数据库**: SQLite, PostgreSQL, MySQL
- **向量数据库**: ChromaDB, Qdrant, Milvus, Pinecone, Elasticsearch
- **认证**: JWT, OAuth, LDAP
- **AI/ML**: OpenAI, Anthropic, Transformers, LangChain
- **文档处理**: pypdf, pymupdf, unstructured
- **搜索**: 多种搜索引擎集成 (DuckDuckGo, Brave, Google PSE 等)

### 基础设施

- **容器化**: Docker, Docker Compose
- **部署**: Kubernetes (Helm), 静态部署
- **存储**: 本地文件系统, S3, Azure Blob, Google Cloud Storage
- **缓存**: Redis
- **监控**: OpenTelemetry, Langfuse

## 项目结构

```
open-webui/
├── backend/           # FastAPI 后端
│   └── open_webui/    # 主要 Python 包
├── src/               # SvelteKit 前端源码
├── static/            # 静态资源
├── docs/              # 文档
├── cypress/           # E2E 测试
└── scripts/           # 构建脚本
```
