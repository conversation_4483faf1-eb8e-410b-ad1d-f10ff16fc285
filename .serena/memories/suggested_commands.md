# 推荐的开发命令

## 系统命令 (macOS/Darwin)

```bash
# 基本文件操作
ls -la          # 列出文件 (包括隐藏文件)
find . -name    # 查找文件
grep -r         # 递归搜索文本
cd              # 切换目录
pwd             # 显示当前目录

# Git 操作
git status      # 查看状态
git log --oneline  # 简洁日志
git branch      # 查看分支
git pull        # 拉取更新
```

## 前端开发命令

### 开发服务器

```bash
npm run dev            # 启动开发服务器 (localhost:5173)
npm run dev:5050       # 启动开发服务器 (localhost:5050)
```

### 构建

```bash
npm run build          # 生产构建
npm run build:watch    # 监听模式构建
npm run preview        # 预览构建结果
```

### 代码质量

```bash
npm run lint           # 运行所有 lint 检查
npm run lint:frontend  # 前端 ESLint 检查
npm run lint:types     # TypeScript 类型检查
npm run lint:backend   # 后端 Pylint 检查
npm run format         # 格式化前端代码
npm run format:backend # 格式化后端代码 (Black)
```

### 测试

```bash
npm run test:frontend  # 前端单元测试 (Vitest)
npm run cy:open        # 打开 Cypress E2E 测试
```

### 国际化

```bash
npm run i18n:parse     # 解析国际化字符串
```

## 后端开发命令

### 开发服务器

```bash
cd backend
./dev.sh               # 启动后端开发服务器
# 或手动启动:
uvicorn open_webui.main:app --port 8080 --host 0.0.0.0 --reload
```

### Python 环境

```bash
cd backend
python -m venv venv    # 创建虚拟环境
source venv/bin/activate  # 激活虚拟环境 (macOS/Linux)
pip install -r requirements.txt  # 安装依赖
```

### 代码质量

```bash
black . --exclude ".venv/|/venv/"  # 格式化 Python 代码
pylint backend/                     # Python 代码检查
```

### 数据库迁移

```bash
cd backend
alembic upgrade head   # 应用数据库迁移
alembic revision --autogenerate -m "description"  # 创建新迁移
```

## Docker 开发命令

### 使用 Docker Compose

```bash
make install           # 安装并启动服务
make start            # 启动服务
make stop             # 停止服务
make startAndBuild    # 构建并启动
make update           # 更新服务
make remove           # 移除服务 (需要确认)
```

### 手动 Docker 命令

```bash
docker-compose up -d                    # 后台启动
docker-compose up -d --build           # 重新构建并启动
docker-compose logs -f open-webui      # 查看日志
docker-compose exec open-webui bash    # 进入容器
```

## 包管理

### Node.js 依赖

```bash
npm install            # 安装依赖
npm update             # 更新依赖
npm audit              # 安全审计
```

### Python 依赖

```bash
pip install -r requirements.txt  # 安装依赖
pip freeze > requirements.txt    # 导出依赖
pip list --outdated              # 查看过期包
```
