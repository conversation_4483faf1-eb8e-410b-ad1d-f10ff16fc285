# 任务完成检查清单

## 代码提交前必须执行的步骤

### 1. 代码质量检查

```bash
# 运行所有 lint 检查
npm run lint

# 如果有错误，分别修复:
npm run lint:frontend  # 修复前端 ESLint 错误
npm run lint:types     # 修复 TypeScript 类型错误
npm run lint:backend   # 修复后端 Pylint 错误
```

### 2. 代码格式化

```bash
# 格式化所有代码
npm run format         # 前端代码格式化
npm run format:backend # 后端代码格式化
```

### 3. 测试执行

```bash
# 运行前端测试
npm run test:frontend

# 如果修改了关键功能，运行 E2E 测试
npm run cy:open
```

### 4. 构建验证

```bash
# 验证构建是否成功
npm run build

# 如果是后端更改，确保后端可以启动
cd backend && ./dev.sh
```

### 5. 类型检查

```bash
# 确保 TypeScript 类型正确
npm run check
```

### 6. 国际化更新 (如果添加了新文本)

```bash
npm run i18n:parse
```

## 数据库相关任务

### 如果修改了数据模型

```bash
cd backend
# 创建新的数据库迁移
alembic revision --autogenerate -m "描述你的更改"

# 应用迁移
alembic upgrade head
```

## Docker 环境验证

### 如果修改了 Docker 相关配置

```bash
# 重新构建并测试
make startAndBuild

# 检查容器状态
docker-compose ps

# 查看日志确保无错误
docker-compose logs -f
```

## 提交前最终检查

1. ✅ 所有 lint 检查通过
2. ✅ 代码已格式化
3. ✅ 测试通过
4. ✅ 构建成功
5. ✅ 类型检查通过
6. ✅ 数据库迁移 (如适用)
7. ✅ Docker 构建测试 (如适用)
8. ✅ 国际化更新 (如适用)

## 常见问题解决

### 如果 lint 失败

- 查看具体错误信息
- 运行 `npm run format` 自动修复格式问题
- 手动修复逻辑错误

### 如果构建失败

- 检查 TypeScript 类型错误
- 确保所有依赖已安装
- 检查导入路径是否正确

### 如果测试失败

- 查看测试输出了解失败原因
- 更新测试用例 (如果业务逻辑有变)
- 修复代码中的 bug