# 开发工作流程

## 项目启动流程

### 1. 环境准备

```bash
# 确保 Node.js 版本 >= 18.13.0, <= 22.x.x
node --version

# 确保 Python 版本 3.11-3.12
python --version

# 安装前端依赖
npm install

# 设置后端环境
cd backend
python -m venv venv
source venv/bin/activate  # macOS/Linux
pip install -r requirements.txt
```

### 2. 开发服务器启动

```bash
# 方法 1: 分别启动前后端
# 终端 1 - 前端
npm run dev

# 终端 2 - 后端
cd backend && ./dev.sh

# 方法 2: 使用 Docker
make install
```

### 3. 访问应用

- 前端开发服务器: http://localhost:5173
- 后端 API: http://localhost:8080
- Docker 版本: http://localhost:3000

## 开发最佳实践

### 分支管理

- `main`: 主分支，稳定版本
- `dev`: 开发分支，最新功能
- 功能分支: `feature/功能名称`
- 修复分支: `fix/问题描述`

### 提交信息规范

```
feat: 添加新功能
fix: 修复 bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建或工具相关
```

### 代码审查要点

1. 代码风格一致性
2. 类型安全 (TypeScript)
3. 错误处理完整性
4. 性能考虑
5. 安全性检查
6. 测试覆盖率

## 调试技巧

### 前端调试

- 使用浏览器开发者工具
- Svelte DevTools 扩展
- 控制台日志调试
- 网络请求监控

### 后端调试

- FastAPI 自动生成的 API 文档: http://localhost:8080/docs
- Loguru 日志系统
- Python 调试器 (pdb)
- 数据库查询日志

### 全栈调试

- 检查前后端 API 通信
- 验证数据格式一致性
- 监控数据库操作
- 性能分析工具

## 常见开发任务

### 添加新 API 端点

1. 在 `backend/open_webui/routers/` 创建或修改路由
2. 更新对应的数据模型
3. 创建数据库迁移 (如需要)
4. 在前端添加 API 调用
5. 更新 TypeScript 类型定义

### 添加新页面

1. 在 `src/routes/` 创建新路由
2. 创建 Svelte 组件
3. 更新导航菜单
4. 添加国际化文本
5. 编写测试用例

### 修改数据库结构

1. 修改 SQLAlchemy 模型
2. 生成数据库迁移
3. 测试迁移脚本
4. 更新相关 API
5. 更新前端类型定义

## 性能优化建议

### 前端优化

- 使用 Svelte 的响应式特性
- 懒加载组件和路由
- 优化图片和静态资源
- 使用 Web Workers 处理重计算

### 后端优化

- 数据库查询优化
- 使用缓存 (Redis)
- 异步处理长时间任务
- API 响应分页

### 全栈优化

- 减少 API 调用次数
- 使用 WebSocket 实时通信
- CDN 静态资源分发
- 数据压缩传输
