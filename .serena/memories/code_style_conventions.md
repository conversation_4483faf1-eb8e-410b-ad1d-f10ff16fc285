# 代码风格和约定

## Python 后端代码风格

### 格式化工具

- **Black**: 用于 Python 代码格式化
- **Pylint**: 用于 Python 代码检查

### 代码约定

- 使用类型提示 (Type Hints)
- 枚举类继承自 `str, Enum`
- 错误消息使用 lambda 函数支持动态内容
- 使用 Loguru 进行日志记录
- 遵循 FastAPI 最佳实践

### 示例代码风格

```python
from enum import Enum
from typing import TYPE_CHECKING

class ERROR_MESSAGES(str, Enum):
    DEFAULT = lambda err="": f'{"Something went wrong :/" if err == "" else "[ERROR: " + str(err) + "]"}'
    USER_NOT_FOUND = "We could not find what you're looking for :/"
```

## 前端代码风格

### 格式化工具

- **Prettier**: 前端代码格式化
- **ESLint**: JavaScript/TypeScript 代码检查

### Prettier 配置

```json
{
    "useTabs": true,
    "singleQuote": true,
    "trailingComma": "none",
    "printWidth": 100,
    "plugins": ["prettier-plugin-svelte"]
}
```

### ESLint 配置

- 基于 `eslint:recommended`
- 支持 TypeScript 和 Svelte
- 集成 Cypress 测试规则
- 使用 Prettier 兼容配置

### 约定

- 使用 Tab 缩进
- 单引号字符串
- 行宽限制 100 字符
- 不使用尾随逗号
- 组件文件使用 PascalCase 命名
