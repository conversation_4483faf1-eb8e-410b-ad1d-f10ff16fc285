# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Open WebUI is a feature-rich, self-hosted AI platform built with SvelteKit and Python FastAPI. It supports various LLM runners including Ollama and OpenAI-compatible APIs, with built-in RAG capabilities.

## Development Commands

### Frontend Development

- `npm run dev` - Start development server with <PERSON><PERSON><PERSON><PERSON> fetch
- `npm run dev:5050` - Start dev server on port 5050
- `npm run build` - Build production frontend
- `npm run build:watch` - Build with watch mode

### Code Quality

- `npm run lint` - Run all linting (frontend, types, backend)
- `npm run lint:frontend` - ESLint for frontend code
- `npm run lint:types` - TypeScript type checking via svelte-check
- `npm run lint:backend` - Pylint for Python backend
- `npm run format` - Prettier for frontend files
- `npm run format:backend` - Black formatter for Python backend

### Testing

- `npm run test:frontend` - Run Vitest tests
- `npm run cy:open` - Open Cypress for e2e testing

### Internationalization

- `npm run i18n:parse` - Parse and format i18n files

### Python Backend

- Backend is in `backend/` directory using FastAPI
- Main entry: `backend/open_webui/main.py`
- Database models in `backend/open_webui/models/`
- API routes in `backend/open_webui/routers/`

## Architecture

### Frontend (SvelteKit)

- **Main source**: `src/` directory
- **Routes**: `src/routes/` - SvelteKit file-based routing
  - `(app)/` - Main application routes
  - `auth/` - Authentication pages
  - `admin/` - Admin interface
  - `workspace/` - Tools, models, prompts management
- **Components**: `src/lib/components/` - Reusable Svelte components organized by feature
- **APIs**: `src/lib/apis/` - Frontend API client functions
- **Stores**: `src/lib/stores/` - Svelte stores for state management
- **Utils**: `src/lib/utils/` - Utility functions and helpers

### Backend (Python/FastAPI)

- **Main app**: `backend/open_webui/main.py`
- **Database**: SQLAlchemy models in `backend/open_webui/models/`
- **Routes**: FastAPI routers in `backend/open_webui/routers/`
- **Utils**: Helper functions in `backend/open_webui/utils/`
- **Apps**: Modular features in `backend/apps/`

### Key Features Architecture

- **Chat System**: Multi-model conversations with streaming support
- **RAG Integration**: Document processing and retrieval via `knowledge/` components
- **User Management**: Role-based access control with groups and permissions
- **Plugin System**: Pipeline framework for extending functionality
- **Tool Integration**: Custom Python functions and external tools

## Configuration

### Environment Setup

- Node.js 18.13.0+ (<=22.x.x)
- Python backend runs separately from frontend
- Uses Pyodide for client-side Python execution

### Key Files

- `package.json` - Node.js dependencies and scripts  
- `svelte.config.js` - SvelteKit configuration with static adapter
- `tailwind.config.js` - Tailwind CSS with custom color variables
- `vite.config.ts` - Vite build configuration
- `backend/requirements.txt` - Python dependencies

## Development Workflow

1. **Frontend changes**: Work in `src/` directory, use `npm run dev`
2. **Backend changes**: Work in `backend/` directory
3. **Component development**: Follow existing patterns in `src/lib/components/`
4. **API integration**: Add client functions to `src/lib/apis/`
5. **Styling**: Uses Tailwind CSS with custom gray color palette
6. **Testing**: Use Cypress for e2e tests, Vitest for unit tests

## Important Notes

- Uses static adapter for deployment
- Supports i18n with multiple languages in `src/lib/i18n/locales/`
- Custom emoji support with SVG assets
- WebSocket integration for real-time features
- Pyodide integration for client-side Python execution
